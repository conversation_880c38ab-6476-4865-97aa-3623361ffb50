import { NextRequest, NextResponse } from 'next/server';
import { createClient, LiveTTSEvents } from '@deepgram/sdk';

export async function POST(request: NextRequest) {
  try {
    console.log('Testing Deepgram WebSocket TTS connection...');
    
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      console.error('DEEPGRAM_API_KEY environment variable not found');
      return NextResponse.json({ 
        error: 'Deepgram API key not configured',
        success: false 
      }, { status: 500 });
    }
    
    console.log('API key configured, testing WebSocket connection...');
    
    // Create Deepgram client
    const deepgram = createClient(deepgramApiKey);
    
    return new Promise((resolve) => {
      const testResults: any = {
        success: false,
        events: [],
        error: null,
        connectionCreated: false,
        connectionOpened: false,
        timeout: false
      };
      
      // Set a timeout for the test
      const timeout = setTimeout(() => {
        testResults.timeout = true;
        testResults.error = 'WebSocket connection timeout - no events received within 10 seconds';
        console.log('WebSocket test timeout');
        resolve(NextResponse.json(testResults, { status: 408 }));
      }, 10000);
      
      try {
        // Try to create WebSocket connection
        const connection = deepgram.speak.live({ 
          model: 'aura-asteria-en',
          encoding: 'mp3',
          smart_format: false,
          sample_rate: 16000
        });
        
        testResults.connectionCreated = true;
        testResults.events.push('WebSocket connection object created');
        console.log('WebSocket connection object created');
        
        connection.on(LiveTTSEvents.Open, () => {
          clearTimeout(timeout);
          testResults.connectionOpened = true;
          testResults.success = true;
          testResults.events.push('WebSocket connection opened successfully');
          console.log('WebSocket connection opened successfully');
          
          // Send a small test text
          connection.sendText('Test');
          connection.flush();
          testResults.events.push('Test text sent');
          
          // Close the connection after a short delay
          setTimeout(() => {
            connection.finish();
            testResults.events.push('Connection closed');
            resolve(NextResponse.json(testResults));
          }, 1000);
        });
        
        connection.on(LiveTTSEvents.Audio, (data: any) => {
          testResults.events.push('Audio data received');
          console.log('Audio data received in test');
        });
        
        connection.on(LiveTTSEvents.Close, () => {
          testResults.events.push('WebSocket connection closed');
          console.log('WebSocket connection closed');
        });
        
        connection.on(LiveTTSEvents.Error, (error: any) => {
          clearTimeout(timeout);
          testResults.error = error.message || 'WebSocket error';
          testResults.events.push(`WebSocket error: ${error.message}`);
          console.error('WebSocket error in test:', error);
          resolve(NextResponse.json(testResults, { status: 500 }));
        });
        
      } catch (connectionError: any) {
        clearTimeout(timeout);
        testResults.error = `Failed to create WebSocket connection: ${connectionError.message}`;
        testResults.events.push(`Connection creation failed: ${connectionError.message}`);
        console.error('Failed to create WebSocket connection:', connectionError);
        resolve(NextResponse.json(testResults, { status: 500 }));
      }
    });
    
  } catch (error: any) {
    console.error('Error in test-websocket-tts endpoint:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
