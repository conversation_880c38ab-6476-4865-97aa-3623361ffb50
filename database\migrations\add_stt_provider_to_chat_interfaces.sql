-- Add STT provider configuration to chat_interfaces table
-- This migration adds support for configuring speech-to-text providers per chat interface

-- Add STT provider column with default value 'auto'
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS stt_provider VARCHAR(20) DEFAULT 'auto';

-- Add STT advanced features configuration as <PERSON>SONB
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS stt_advanced_features JSONB DEFAULT '{}';

-- Add comment to document the new columns
COMMENT ON COLUMN chat_interfaces.stt_provider IS 'Speech-to-text provider: auto, deepgram, web_speech, disabled';
COMMENT ON COLUMN chat_interfaces.stt_advanced_features IS 'Advanced STT configuration: keyterm prompting, profanity filter, etc.';

-- Create index for STT provider for faster queries
CREATE INDEX IF NOT EXISTS idx_chat_interfaces_stt_provider ON chat_interfaces(stt_provider);

-- Update existing records to use 'auto' provider (this is already the default)
UPDATE chat_interfaces 
SET stt_provider = 'auto' 
WHERE stt_provider IS NULL;

-- Example of advanced features structure (for documentation):
-- {
--   "keyterm": ["technical", "terms", "to", "boost"],
--   "profanity_filter": true,
--   "punctuate": true,
--   "numerals": true,
--   "model": "nova-2"
-- }
