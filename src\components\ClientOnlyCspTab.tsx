'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard'

export function ClientOnlyCspTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [cspCode, setCspCode] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Fetch the embed code from the API
    const fetchEmbedCode = async () => {
      try {
        const response = await fetch(`/api/generate-embed-code?chatId=${chatId}`);
        if (response.ok) {
          const data = await response.json();
          setCspCode(data.cspEmbedCode);
        } else {
          console.error('Failed to fetch embed code');
          // Fallback to a basic embed code
          const siteOrigin = window.location.origin;
          setCspCode(`<!-- BotFusion Chat Widget - CSP-Friendly Version -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" src="${siteOrigin}/api/csp-embed-script?chatId=${chatId}"></script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' ${siteOrigin};
  frame-src 'self' ${siteOrigin};
  frame-ancestors 'self' ${siteOrigin};
">
-->`);
        }
      } catch (error) {
        console.error('Error fetching embed code:', error);
        // Use fallback code here too
        const siteOrigin = window.location.origin;
        setCspCode(`<!-- BotFusion Chat Widget - CSP-Friendly Version -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" src="${siteOrigin}/api/csp-embed-script?chatId=${chatId}"></script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' ${siteOrigin};
  frame-src 'self' ${siteOrigin};
  frame-ancestors 'self' ${siteOrigin};
">
-->`);
      }
    };

    fetchEmbedCode();
  }, [chatId, chatName])

  const { isCopied: isCspCopied, copyToClipboard: copyCsp } = useCopyToClipboard(2000)

  const copyToClipboard = (text: string) => {
    copyCsp(text, `CSP-friendly code copied to clipboard!`)
  }

  if (!isClient) {
    return <div className="p-4 text-gray-400">Loading CSP-friendly embed code...</div>
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-neon-blue mb-2">CSP-Friendly Embed Code</h3>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            onClick={() => copyToClipboard(cspCode)}
            className="glass-card hover:shadow-glow-blue"
            size="sm"
          >
            {isCspCopied ? 'COPIED' : 'Copy'}
          </Button>
        </div>
        <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
          {cspCode}
        </pre>
      </div>
      <p className="text-sm text-gray-400 mt-3">
        <strong>CSP-Friendly Embed</strong> - Use this if your site has strict Content Security Policy settings that block inline scripts.
        <br /><br />
        This option:
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>Uses an external script file instead of inline JavaScript</li>
          <li>Works with sites that block &apos;unsafe-inline&apos; scripts</li>
          <li>Uses iframes for both the button and chat window</li>
          <li>Requires fewer CSP exceptions</li>
        </ul>
        <br />
        Add the <code className="bg-gray-700 px-1 py-0.5 rounded text-xs">nonce</code> attribute if your site requires it, or remove it if not needed.
      </p>
    </div>
  )
}
