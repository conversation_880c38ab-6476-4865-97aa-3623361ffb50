import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters or use the default one
    const chatId = request.nextUrl.searchParams.get('chatId') || 'b76a3980-9f8e-47cd-ae7d-f02747552c4d'

    // Get the base URL from the environment or use the current origin
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || request.nextUrl.origin
    
    // Create the script content directly - this version doesn't create a button, just the iframe
    const scriptContent = `
/**
 * BotFusion Direct Embed Script
 * This script creates only the iframe without a button
 * Version: 1.0.0
 */
(function() {
  // Create the iframe element
  const iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.src = '${baseUrl}/embed/${chatId}';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'block'; // Always visible
  iframe.setAttribute('allow', 'microphone');
  iframe.setAttribute('title', 'Chat Widget');
  iframe.setAttribute('loading', 'lazy');
  iframe.setAttribute('importance', 'high');
  
  // Add message listener to handle close button in iframe
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframe
    if (event.data === 'botfusion-chat-close') {
      // Hide the iframe
      iframe.style.display = 'none';
    }
  });
  
  // Append iframe to the document
  document.body.appendChild(iframe);
  
  // Expose the BotFusionChat object to the window
  window.BotFusionChat = {
    open: function() {
      iframe.style.display = 'block';
    },
    close: function() {
      iframe.style.display = 'none';
    }
  };
  
  console.log('BotFusion Chat: Direct iframe initialized successfully');
})();`

    // Return the script with the correct content type
    return new NextResponse(scriptContent, {
      headers: {
        'Content-Type': 'application/javascript',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      }
    })
  } catch (error) {
    console.error('Error generating embed script:', error)
    return NextResponse.json({ error: 'Failed to generate embed script' }, { status: 500 })
  }
}
