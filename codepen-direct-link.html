<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodePen Direct Link</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .button {
            display: inline-block;
            background-color: #0ebeff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin-top: 20px;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>CodePen Direct Link</h1>
    
    <div class="note">
        <strong>Instructions:</strong> Click the button below to open a CodePen with our embed code already set up.
        This will test embedding on a different domain.
    </div>
    
    <p>This will create a new CodePen with the following HTML:</p>
    <pre>
&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Chat Widget Embed Test&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Chat Widget Embed Test on CodePen&lt;/h1&gt;
    
    &lt;iframe 
        src="https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/embed/1" 
        width="400" 
        height="600" 
        frameborder="0"
        style="border: 1px solid #ccc;"
    &gt;&lt;/iframe&gt;
    
    &lt;script&gt;
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });
        
        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all iframes and resources');
        });
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
    </pre>
    
    <a href="https://codepen.io/pen?template=abXYZqr" target="_blank" class="button">Open in CodePen</a>
    
    <script>
        // Create a form to submit to CodePen
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.button');
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const data = {
                    title: "Chat Widget Embed Test",
                    description: "Testing embedding of chat widget iframe",
                    html: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Widget Embed Test</title>
</head>
<body>
    <h1>Chat Widget Embed Test on CodePen</h1>
    
    <iframe 
        src="https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/embed/1" 
        width="400" 
        height="600" 
        frameborder="0"
        style="border: 1px solid #ccc;"
    ></iframe>
    
    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });
        
        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all iframes and resources');
        });
    </script>
</body>
</html>`,
                    css: "",
                    js: ""
                };
                
                const form = document.createElement('form');
                form.action = 'https://codepen.io/pen/define';
                form.method = 'POST';
                form.target = '_blank';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'data';
                input.value = JSON.stringify(data);
                
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            });
        });
    </script>
</body>
</html>
