# Coolify Deployment Guide

This guide will help you deploy your BotFusion X application to Coolify with the correct environment variables.

## Prerequisites

- A Coolify instance
- A Supabase project with the following credentials:
  - Supabase URL
  - Supabase Anon Key
  - Supabase Service Role Key

## Deployment Steps

### 1. Connect Your GitHub Repository

1. Log in to your Coolify dashboard
2. Navigate to "Sources" and connect your GitHub account if you haven't already
3. Add your repository as a source

### 2. Create a New Service

1. Go to "Services" and click "New Service"
2. Select "Application" as the service type
3. Choose your GitHub repository
4. Select the branch you want to deploy (usually `main`)

### 3. Configure the Service

1. Select "Docker" as the build method
2. Coolify should automatically detect the Dockerfile in your repository
3. Set the following configuration:
   - Name: `botfusion-x` (or any name you prefer)
   - Port: `3000` (the port your Next.js application runs on)
   - Build Command: `npm run build`
   - Start Command: `npm start`

### 4. Add Environment Variables

This is the critical step where you need to add your Supabase credentials:

1. Scroll down to the "Environment Variables" section
2. Add the following environment variables:

   | Name | Value |
   |------|-------|
   | `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL (e.g., `https://yourproject.supabase.co`) |
   | `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Your Supabase anonymous key |
   | `SUPABASE_SERVICE_ROLE_KEY` | Your Supabase service role key |

   > **Important**: Make sure to copy the exact values from your Supabase project dashboard or your local `.env.local` file.

### 5. Deploy

1. Click "Save" to save your service configuration
2. Click "Deploy" to start the deployment process
3. Wait for the build and deployment to complete

### 6. Verify Deployment

1. Once deployment is complete, click on the provided URL to open your application
2. Verify that your application is working correctly
3. Check the logs if you encounter any issues

## Troubleshooting

If you encounter issues with your deployment, check the following:

1. **Environment Variables**: Ensure all environment variables are correctly set in the Coolify service settings.
2. **Build Logs**: Check the build logs for any errors related to missing environment variables.
3. **Supabase Connection**: Verify that your Supabase project is accessible and that the credentials are correct.
4. **Docker Configuration**: Make sure your Dockerfile is correctly configured for a Next.js application.
5. **CORS Settings**: Make sure your Supabase project has the correct CORS settings to allow requests from your Coolify deployment URL.

## Supabase CORS Configuration

To configure CORS in your Supabase project:

1. Go to your Supabase project dashboard
2. Navigate to Project Settings > API
3. Under "CORS (Cross-Origin Resource Sharing)", add your Coolify deployment URL
4. Save the changes

## Additional Resources

- [Coolify Documentation](https://coolify.io/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
