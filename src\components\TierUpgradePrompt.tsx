'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { UserTier, getTierDisplayName, getTierPrice, getTierFeatures } from '@/lib/tiers'

interface TierUpgradePromptProps {
  currentTier: UserTier
  requiredTier: UserTier
  feature: string
  description?: string
  className?: string
  compact?: boolean
}

export default function TierUpgradePrompt({
  currentTier,
  requiredTier,
  feature,
  description,
  className = '',
  compact = false
}: TierUpgradePromptProps) {
  const requiredTierPrice = getTierPrice(requiredTier)
  const requiredTierFeatures = getTierFeatures(requiredTier)

  const getTierIcon = (tier: UserTier) => {
    switch (tier) {
      case 'free': return '🆓'
      case 'standard': return '⭐'
      case 'pro': return '💎'
      default: return '🎯'
    }
  }

  const getTierColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-500'
      case 'standard': return 'bg-blue-500'
      case 'pro': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  if (compact) {
    return (
      <div className={`p-3 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-600 rounded-lg ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">{getTierIcon(requiredTier)}</span>
            <div>
              <p className="text-sm font-medium text-blue-200">
                {feature} requires {getTierDisplayName(requiredTier)} tier
              </p>
              {description && (
                <p className="text-xs text-blue-300">{description}</p>
              )}
            </div>
          </div>
          <Button
            size="sm"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            onClick={() => {
              // TODO: Implement upgrade flow
              console.log(`Upgrade to ${requiredTier} tier`)
            }}
          >
            Upgrade
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card className={`bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-600 p-6 ${className}`}>
      <div className="text-center space-y-4">
        <div className="flex justify-center items-center gap-3">
          <Badge className={`${getTierColor(currentTier)} text-white`}>
            {getTierIcon(currentTier)} {getTierDisplayName(currentTier)}
          </Badge>
          <span className="text-gray-400">→</span>
          <Badge className={`${getTierColor(requiredTier)} text-white`}>
            {getTierIcon(requiredTier)} {getTierDisplayName(requiredTier)}
          </Badge>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Upgrade Required for {feature}
          </h3>
          {description && (
            <p className="text-gray-300 text-sm mb-4">{description}</p>
          )}
        </div>

        <div className="bg-white/10 rounded-lg p-4 space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-white font-medium">{getTierDisplayName(requiredTier)} Tier</span>
            <div className="text-right">
              <div className="text-2xl font-bold text-white">
                ${requiredTierPrice.monthly}
              </div>
              <div className="text-xs text-gray-400">per month</div>
            </div>
          </div>

          <div className="border-t border-gray-600 pt-3">
            <p className="text-sm text-gray-300 mb-2">What you'll get:</p>
            <div className="grid grid-cols-1 gap-1">
              {requiredTierFeatures.slice(0, 4).map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                  <span className="text-green-400">✓</span>
                  {feature}
                </div>
              ))}
              {requiredTierFeatures.length > 4 && (
                <div className="text-xs text-gray-400 mt-1">
                  +{requiredTierFeatures.length - 4} more features
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            className="flex-1 border-gray-600 text-gray-300 hover:bg-white/10"
            onClick={() => {
              // TODO: Implement learn more flow
              console.log('Learn more about tiers')
            }}
          >
            Learn More
          </Button>
          <Button
            className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            onClick={() => {
              // TODO: Implement upgrade flow
              console.log(`Upgrade to ${requiredTier} tier`)
            }}
          >
            Upgrade Now
          </Button>
        </div>

        <p className="text-xs text-gray-400">
          Cancel anytime • 30-day money-back guarantee
        </p>
      </div>
    </Card>
  )
}

// Convenience component for feature-specific upgrade prompts
export function VoiceUpgradePrompt({ currentTier }: { currentTier: UserTier }) {
  return (
    <TierUpgradePrompt
      currentTier={currentTier}
      requiredTier="standard"
      feature="Voice Features"
      description="Unlock text-to-speech and speech-to-text capabilities with multiple voice models."
      compact
    />
  )
}

export function BrandingUpgradePrompt({ currentTier }: { currentTier: UserTier }) {
  return (
    <TierUpgradePrompt
      currentTier={currentTier}
      requiredTier="standard"
      feature="Branding Removal"
      description="Remove 'Powered by BotFusion X' branding from your chat interfaces."
      compact
    />
  )
}

export function CustomizationUpgradePrompt({ currentTier }: { currentTier: UserTier }) {
  return (
    <TierUpgradePrompt
      currentTier={currentTier}
      requiredTier="standard"
      feature="Advanced Customization"
      description="Unlock gradient headers, custom colors, and advanced styling options."
      compact
    />
  )
}

export function WhiteLabelUpgradePrompt({ currentTier }: { currentTier: UserTier }) {
  return (
    <TierUpgradePrompt
      currentTier={currentTier}
      requiredTier="pro"
      feature="White Labeling"
      description="Complete white-labeling with custom branding text and URLs."
      compact
    />
  )
}

export function AnalyticsUpgradePrompt({ currentTier }: { currentTier: UserTier }) {
  return (
    <TierUpgradePrompt
      currentTier={currentTier}
      requiredTier="pro"
      feature="Analytics Dashboard"
      description="Detailed analytics and insights for your chat interfaces."
      compact
    />
  )
}
