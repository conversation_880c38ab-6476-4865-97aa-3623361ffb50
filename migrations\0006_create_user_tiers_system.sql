-- Create user tiers system for BotFusion X
-- This migration adds subscription tiers, user tier management, and feature restrictions

-- 1. Create user_profiles table to extend auth.users with tier information
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  tier TEXT NOT NULL DEFAULT 'free' CHECK (tier IN ('free', 'standard', 'pro')),
  subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial')),
  subscription_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for user_profiles
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable insert for authenticated users" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 2. Create tier_features table to define what each tier can access
CREATE TABLE IF NOT EXISTS tier_features (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tier TEXT NOT NULL CHECK (tier IN ('free', 'standard', 'pro')),
  feature_name TEXT NOT NULL,
  feature_value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert tier feature definitions
INSERT INTO tier_features (tier, feature_name, feature_value, description) VALUES
-- Free Tier Features
('free', 'max_chat_interfaces', '1', 'Maximum number of chat interfaces allowed'),
('free', 'voice_features_enabled', 'false', 'Whether voice features (TTS/STT) are available'),
('free', 'voice_models', '[]', 'Available voice models for TTS'),
('free', 'branding_removal', 'false', 'Whether "Powered by BotFusion X" can be removed'),
('free', 'white_labeling', 'false', 'Whether complete white-labeling is available'),
('free', 'custom_branding_text', 'false', 'Whether custom "Powered By" text is allowed'),
('free', 'advanced_customization', 'false', 'Whether advanced UI customization is available'),
('free', 'analytics_dashboard', 'false', 'Whether detailed analytics are available'),
('free', 'priority_support', 'false', 'Whether priority support is available'),

-- Standard Tier Features  
('standard', 'max_chat_interfaces', '-1', 'Unlimited chat interfaces'),
('standard', 'voice_features_enabled', 'true', 'Voice features available'),
('standard', 'voice_models', '["thalia", "asteria", "helena"]', 'Basic voice models available'),
('standard', 'branding_removal', 'true', 'Can remove "Powered by BotFusion X"'),
('standard', 'white_labeling', 'false', 'No complete white-labeling'),
('standard', 'custom_branding_text', 'false', 'No custom "Powered By" text'),
('standard', 'advanced_customization', 'true', 'Advanced colors, greetings, logo, dark mode'),
('standard', 'analytics_dashboard', 'false', 'No detailed analytics'),
('standard', 'priority_support', 'false', 'Email support only'),

-- Pro Tier Features
('pro', 'max_chat_interfaces', '-1', 'Unlimited chat interfaces'),
('pro', 'voice_features_enabled', 'true', 'Premium voice features available'),
('pro', 'voice_models', '["thalia", "asteria", "helena", "arcas", "apollo", "zeus"]', 'All voice models available'),
('pro', 'branding_removal', 'true', 'Can remove "Powered by BotFusion X"'),
('pro', 'white_labeling', 'true', 'Complete white-labeling available'),
('pro', 'custom_branding_text', 'true', 'Custom "Powered By" text allowed'),
('pro', 'advanced_customization', 'true', 'Complete UI customization'),
('pro', 'analytics_dashboard', 'true', 'Detailed analytics and data export'),
('pro', 'priority_support', 'true', 'Priority support available');

-- 3. Create usage_tracking table to monitor tier limits
CREATE TABLE IF NOT EXISTS usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type TEXT NOT NULL, -- 'chat_interfaces', 'messages', 'voice_requests'
  current_usage INTEGER NOT NULL DEFAULT 0,
  period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  period_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for usage_tracking
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;

-- RLS policies for usage_tracking
CREATE POLICY "Users can view own usage" ON usage_tracking
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage usage" ON usage_tracking
  FOR ALL USING (true);

-- 4. Create function to get user tier and features
CREATE OR REPLACE FUNCTION get_user_tier_features(user_uuid UUID)
RETURNS TABLE (
  tier TEXT,
  feature_name TEXT,
  feature_value JSONB,
  description TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.tier,
    tf.feature_name,
    tf.feature_value,
    tf.description
  FROM user_profiles up
  JOIN tier_features tf ON tf.tier = up.tier
  WHERE up.id = user_uuid;
END;
$$;

-- 5. Create function to check if user can create chat interface
CREATE OR REPLACE FUNCTION can_create_chat_interface(user_uuid UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier TEXT;
  max_interfaces INTEGER;
  current_count INTEGER;
BEGIN
  -- Get user tier
  SELECT tier INTO user_tier
  FROM user_profiles
  WHERE id = user_uuid;
  
  -- If no profile exists, default to free tier
  IF user_tier IS NULL THEN
    user_tier := 'free';
  END IF;
  
  -- Get max interfaces for tier
  SELECT (feature_value::TEXT)::INTEGER INTO max_interfaces
  FROM tier_features
  WHERE tier = user_tier AND feature_name = 'max_chat_interfaces';
  
  -- If unlimited (-1), allow creation
  IF max_interfaces = -1 THEN
    RETURN TRUE;
  END IF;
  
  -- Count current interfaces
  SELECT COUNT(*) INTO current_count
  FROM chat_interfaces
  WHERE user_id = user_uuid;
  
  -- Check if under limit
  RETURN current_count < max_interfaces;
END;
$$;

-- 6. Create function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO user_profiles (id, tier, subscription_status)
  VALUES (NEW.id, 'free', 'active');
  
  RETURN NEW;
END;
$$;

-- Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 7. Update chat_interfaces table policies to respect tier limits
DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON chat_interfaces;
CREATE POLICY "Enable insert access for authenticated users" ON chat_interfaces
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND 
    can_create_chat_interface(auth.uid())
  );

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_tier ON user_profiles(tier);
CREATE INDEX IF NOT EXISTS idx_tier_features_tier_feature ON tier_features(tier, feature_name);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_resource ON usage_tracking(user_id, resource_type);

-- 9. Add comments for documentation
COMMENT ON TABLE user_profiles IS 'Extended user profiles with subscription tier information';
COMMENT ON TABLE tier_features IS 'Feature definitions for each subscription tier';
COMMENT ON TABLE usage_tracking IS 'Track resource usage against tier limits';
COMMENT ON FUNCTION get_user_tier_features IS 'Get all features available to a user based on their tier';
COMMENT ON FUNCTION can_create_chat_interface IS 'Check if user can create another chat interface based on tier limits';
