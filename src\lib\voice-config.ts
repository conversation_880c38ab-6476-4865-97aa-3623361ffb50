// Voice configuration for Deepgram Aura TTS voices
export interface VoiceOption {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  model: string; // Full Deepgram model name
}

export const AVAILABLE_VOICES: VoiceOption[] = [
  // Original voices (keeping existing ones)
  {
    id: 'thalia',
    name: 'T<PERSON><PERSON>',
    gender: 'female',
    description: 'Warm, friendly female voice (default)',
    model: 'aura-2-thalia-en'
  },
  {
    id: 'asteria',
    name: 'Asteria',
    gender: 'female',
    description: 'Clear, professional female voice',
    model: 'aura-2-asteria-en'
  },
  {
    id: 'helena',
    name: '<PERSON>',
    gender: 'female',
    description: 'Elegant, sophisticated female voice',
    model: 'aura-2-helena-en'
  },
  {
    id: 'arcas',
    name: '<PERSON><PERSON>',
    gender: 'male',
    description: 'Deep, authoritative male voice',
    model: 'aura-2-arcas-en'
  },
  {
    id: 'apollo',
    name: '<PERSON>',
    gender: 'male',
    description: 'Smooth, confident male voice',
    model: 'aura-2-apollo-en'
  },
  {
    id: 'zeus',
    name: '<PERSON>',
    gender: 'male',
    description: 'Strong, commanding male voice',
    model: 'aura-2-zeus-en'
  },

  // Additional Featured Female Voices
  {
    id: 'andromeda',
    name: '<PERSON><PERSON><PERSON>',
    gender: 'female',
    description: 'Casual, expressive, comfortable',
    model: 'aura-2-andromeda-en'
  },
  {
    id: 'aurora',
    name: 'Aurora',
    gender: 'female',
    description: 'Cheerful, expressive, energetic',
    model: 'aura-2-aurora-en'
  },
  {
    id: 'electra',
    name: 'Electra',
    gender: 'female',
    description: 'Professional, engaging, knowledgeable',
    model: 'aura-2-electra-en'
  },
  {
    id: 'harmonia',
    name: 'Harmonia',
    gender: 'female',
    description: 'Empathetic, clear, calm, confident',
    model: 'aura-2-harmonia-en'
  },
  {
    id: 'hera',
    name: 'Hera',
    gender: 'female',
    description: 'Smooth, warm, professional',
    model: 'aura-2-hera-en'
  },
  {
    id: 'iris',
    name: 'Iris',
    gender: 'female',
    description: 'Cheerful, positive, approachable',
    model: 'aura-2-iris-en'
  },
  {
    id: 'luna',
    name: 'Luna',
    gender: 'female',
    description: 'Friendly, natural, engaging',
    model: 'aura-2-luna-en'
  },
  {
    id: 'minerva',
    name: 'Minerva',
    gender: 'female',
    description: 'Positive, friendly, natural',
    model: 'aura-2-minerva-en'
  },
  {
    id: 'ophelia',
    name: 'Ophelia',
    gender: 'female',
    description: 'Expressive, enthusiastic, cheerful',
    model: 'aura-2-ophelia-en'
  },
  {
    id: 'phoebe',
    name: 'Phoebe',
    gender: 'female',
    description: 'Energetic, warm, casual',
    model: 'aura-2-phoebe-en'
  },
  {
    id: 'selene',
    name: 'Selene',
    gender: 'female',
    description: 'Expressive, engaging, energetic',
    model: 'aura-2-selene-en'
  },
  {
    id: 'vesta',
    name: 'Vesta',
    gender: 'female',
    description: 'Natural, expressive, patient, empathetic',
    model: 'aura-2-vesta-en'
  },

  // Additional Featured Male Voices
  {
    id: 'aries',
    name: 'Aries',
    gender: 'male',
    description: 'Warm, energetic, caring',
    model: 'aura-2-aries-en'
  },
  {
    id: 'atlas',
    name: 'Atlas',
    gender: 'male',
    description: 'Enthusiastic, confident, approachable',
    model: 'aura-2-atlas-en'
  },
  {
    id: 'draco',
    name: 'Draco',
    gender: 'male',
    description: 'British accent, warm, approachable, trustworthy',
    model: 'aura-2-draco-en'
  },
  {
    id: 'hermes',
    name: 'Hermes',
    gender: 'male',
    description: 'Expressive, engaging, professional',
    model: 'aura-2-hermes-en'
  },
  {
    id: 'hyperion',
    name: 'Hyperion',
    gender: 'male',
    description: 'Australian accent, caring, warm, empathetic',
    model: 'aura-2-hyperion-en'
  },
  {
    id: 'jupiter',
    name: 'Jupiter',
    gender: 'male',
    description: 'Expressive, knowledgeable, baritone',
    model: 'aura-2-jupiter-en'
  },
  {
    id: 'mars',
    name: 'Mars',
    gender: 'male',
    description: 'Smooth, patient, trustworthy, baritone',
    model: 'aura-2-mars-en'
  },
  {
    id: 'neptune',
    name: 'Neptune',
    gender: 'male',
    description: 'Professional, patient, polite',
    model: 'aura-2-neptune-en'
  },
  {
    id: 'odysseus',
    name: 'Odysseus',
    gender: 'male',
    description: 'Calm, smooth, comfortable, professional',
    model: 'aura-2-odysseus-en'
  },
  {
    id: 'orion',
    name: 'Orion',
    gender: 'male',
    description: 'Approachable, comfortable, calm, polite',
    model: 'aura-2-orion-en'
  },
  {
    id: 'orpheus',
    name: 'Orpheus',
    gender: 'male',
    description: 'Professional, clear, confident, trustworthy',
    model: 'aura-2-orpheus-en'
  },
  {
    id: 'pluto',
    name: 'Pluto',
    gender: 'male',
    description: 'Smooth, calm, empathetic, baritone',
    model: 'aura-2-pluto-en'
  },
  {
    id: 'saturn',
    name: 'Saturn',
    gender: 'male',
    description: 'Knowledgeable, confident, baritone',
    model: 'aura-2-saturn-en'
  }
];

export const DEFAULT_VOICE = 'thalia';

// Helper function to get voice by ID
export function getVoiceById(id: string): VoiceOption | undefined {
  return AVAILABLE_VOICES.find(voice => voice.id === id);
}

// Helper function to get voice model name
export function getVoiceModel(voiceId: string): string {
  const voice = getVoiceById(voiceId);
  return voice ? voice.model : `aura-2-${voiceId}-en`;
}

// Helper function to group voices by gender
export function getVoicesByGender() {
  const female = AVAILABLE_VOICES.filter(voice => voice.gender === 'female');
  const male = AVAILABLE_VOICES.filter(voice => voice.gender === 'male');
  return { female, male };
}
