// Voice configuration for Deepgram Aura TTS voices
export interface VoiceOption {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  model: string; // Full Deepgram model name
}

export const AVAILABLE_VOICES: VoiceOption[] = [
  {
    id: 'thalia',
    name: '<PERSON><PERSON><PERSON>',
    gender: 'female',
    description: 'Warm, friendly female voice (default)',
    model: 'aura-2-thalia-en'
  },
  {
    id: 'asteria',
    name: 'As<PERSON><PERSON>',
    gender: 'female',
    description: 'Clear, professional female voice',
    model: 'aura-2-asteria-en'
  },
  {
    id: 'helena',
    name: '<PERSON>',
    gender: 'female',
    description: 'Elegant, sophisticated female voice',
    model: 'aura-2-helena-en'
  },
  {
    id: 'arcas',
    name: '<PERSON><PERSON>',
    gender: 'male',
    description: 'Deep, authoritative male voice',
    model: 'aura-2-arcas-en'
  },
  {
    id: 'apollo',
    name: 'Apollo',
    gender: 'male',
    description: 'Smooth, confident male voice',
    model: 'aura-2-apollo-en'
  },
  {
    id: 'zeus',
    name: '<PERSON>',
    gender: 'male',
    description: 'Strong, commanding male voice',
    model: 'aura-2-zeus-en'
  }
];

export const DEFAULT_VOICE = 'thalia';

// Helper function to get voice by ID
export function getVoiceById(id: string): VoiceOption | undefined {
  return AVAILABLE_VOICES.find(voice => voice.id === id);
}

// Helper function to get voice model name
export function getVoiceModel(voiceId: string): string {
  const voice = getVoiceById(voiceId);
  return voice ? voice.model : `aura-2-${voiceId}-en`;
}

// Helper function to group voices by gender
export function getVoicesByGender() {
  const female = AVAILABLE_VOICES.filter(voice => voice.gender === 'female');
  const male = AVAILABLE_VOICES.filter(voice => voice.gender === 'male');
  return { female, male };
}
