CREATE OR REPLACE FUNCTION create_chat_interface(
  p_name text,
  p_description text,
  p_webhook_url text
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  user_id uuid;
BEGIN
  -- Get current authenticated user
  user_id := auth.uid();
  
  IF user_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Not authenticated'
    );
  END IF;

  INSERT INTO chat_interfaces(name, description, "webhookUrl", createdat, user_id)
  VALUES (p_name, p_description, p_webhook_url, now(), user_id)
  RETURNING to_jsonb(chat_interfaces.*) INTO result;
  
  RETURN jsonb_build_object(
    'success', true,
    'data', result
  );
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'success', false,
    'error', SQLERRM
  );
END;
$$;