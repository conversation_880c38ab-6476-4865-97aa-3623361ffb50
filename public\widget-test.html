<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Widget Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0070f3;
            margin-bottom: 1rem;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
            background: white;
        }
        .code {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #0070f3;
            padding: 1rem;
            margin: 1rem 0;
        }
        .log {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .error { color: #d32f2f; }
        .warn { color: #f57c00; }
        .info { color: #0288d1; }
    </style>
</head>
<body>
    <h1>BotFusion Widget Test</h1>
    
    <div class="container">
        <div class="card">
            <h2>Widget Embed Test</h2>
            <p>This page tests the BotFusion chat widget embedding.</p>
            
            <div class="code">
&lt;script src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-widget"&gt;&lt;/script&gt;
            </div>
            
            <div class="note">
                <strong>Note:</strong> The chat button should appear in the bottom-right corner of this page.
            </div>
        </div>

        <div class="card">
            <h2>Console Output</h2>
            <div id="console-output" class="log"></div>
        </div>
    </div>

    <!-- Script to capture console output -->
    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        
        // Store original console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods
        console.log = function() {
            // Call original method
            originalConsole.log.apply(console, arguments);
            
            // Add to our display
            const logElement = document.createElement('div');
            logElement.className = 'log-entry';
            logElement.textContent = Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.error = function() {
            originalConsole.error.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry error';
            logElement.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.warn = function() {
            originalConsole.warn.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry warn';
            logElement.textContent = 'WARNING: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.info = function() {
            originalConsole.info.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry info';
            logElement.textContent = 'INFO: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        // Log page load
        console.info('Page loaded, waiting for BotFusion script to initialize...');

        // Check if script loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.BotFusionChat) {
                    console.info('BotFusion script loaded successfully!');
                } else {
                    console.error('BotFusion script not found after 3 seconds');
                }
            }, 3000);
        });
    </script>

    <!-- Widget Embed Script -->
    <script src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-widget"></script>
</body>
</html>
