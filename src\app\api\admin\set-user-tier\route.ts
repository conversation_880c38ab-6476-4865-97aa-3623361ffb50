import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { UserTier } from '@/lib/tiers'

export async function POST(request: NextRequest) {
  try {
    const { userId, tier } = await request.json()

    if (!userId || !tier) {
      return NextResponse.json({ 
        error: 'userId and tier are required' 
      }, { status: 400 })
    }

    // Validate tier
    const validTiers: UserTier[] = ['free', 'standard', 'pro']
    if (!validTiers.includes(tier)) {
      return NextResponse.json({ 
        error: 'Invalid tier. Must be one of: free, standard, pro' 
      }, { status: 400 })
    }

    const supabase = createClient()

    // Check if user profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('id, tier')
      .eq('id', userId)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching user profile:', fetchError)
      return NextResponse.json({ 
        error: 'Failed to fetch user profile' 
      }, { status: 500 })
    }

    if (existingProfile) {
      // Update existing profile
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ 
          tier,
          subscription_status: tier === 'free' ? 'active' : 'active',
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: tier === 'free' ? null : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating user profile:', updateError)
        return NextResponse.json({ 
          error: 'Failed to update user tier' 
        }, { status: 500 })
      }

      return NextResponse.json({ 
        message: `User tier updated from ${existingProfile.tier} to ${tier}`,
        userId,
        previousTier: existingProfile.tier,
        newTier: tier
      })
    } else {
      // Create new profile
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert({
          id: userId,
          tier,
          subscription_status: 'active',
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: tier === 'free' ? null : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (insertError) {
        console.error('Error creating user profile:', insertError)
        return NextResponse.json({ 
          error: 'Failed to create user profile' 
        }, { status: 500 })
      }

      return NextResponse.json({ 
        message: `User profile created with ${tier} tier`,
        userId,
        newTier: tier
      })
    }
  } catch (error) {
    console.error('Error in set-user-tier:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// GET endpoint to check current user's tier
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/set-user-tier - Request received')
    console.log('Request headers:', Object.fromEntries(request.headers.entries()))

    const supabase = createClient()
    console.log('Supabase client created')

    // Try to get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth result:', { user: user?.id, error: authError?.message })

    if (authError || !user) {
      console.log('Authentication failed:', { authError, hasUser: !!user })
      return NextResponse.json({
        error: 'Authentication required',
        details: 'Please make sure you are logged in',
        authError: authError?.message,
        debug: {
          hasUser: !!user,
          errorMessage: authError?.message,
          errorCode: authError?.status
        }
      }, { status: 401 })
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching user profile:', profileError)
      return NextResponse.json({ 
        error: 'Failed to fetch user profile' 
      }, { status: 500 })
    }

    if (!profile) {
      return NextResponse.json({
        userId: user.id,
        email: user.email,
        tier: 'free', // Default tier
        hasProfile: false,
        message: 'No profile found, using default free tier'
      })
    }

    return NextResponse.json({
      userId: user.id,
      email: user.email,
      tier: profile.tier,
      subscriptionStatus: profile.subscription_status,
      subscriptionStartDate: profile.subscription_start_date,
      subscriptionEndDate: profile.subscription_end_date,
      hasProfile: true,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at
    })
  } catch (error) {
    console.error('Error in get user tier:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
