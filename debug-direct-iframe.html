<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Direct Iframe Embed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
        .debug-info pre {
            max-height: 200px;
            overflow-y: auto;
        }

        /* Chat widget styles */
        #chat-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        #chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        #chat-iframe {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 600px;
            border: none;
            border-radius: 12px;
            /* box-shadow removed */
            z-index: 9999;
            display: none;
        }

        @media (max-width: 480px) {
            #chat-iframe {
                width: 100%;
                height: 100%;
                bottom: 0;
                right: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <h1>Debug Direct Iframe Embed</h1>

    <div class="important">
        <strong>Important:</strong> This page is for debugging the direct iframe embed code. Please open the browser console (F12) to see any errors.
    </div>

    <div class="content">
        <h2>Direct Iframe Embed</h2>
        <p>This test uses a direct iframe embed approach with a custom button:</p>
        <pre>&lt;div id="chat-button"&gt;
    &lt;svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
        &lt;path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"&gt;&lt;/path&gt;
    &lt;/svg&gt;
&lt;/div&gt;

&lt;iframe id="chat-iframe" src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/embed/test-chat-id" allow="microphone"&gt;&lt;/iframe&gt;

&lt;script&gt;
    // Get the elements
    const button = document.getElementById('chat-button');
    const iframe = document.getElementById('chat-iframe');

    // Add click event to button
    button.onclick = function() {
        iframe.style.display = 'block';
        this.style.display = 'none';
    };

    // Add message listener for close events
    window.addEventListener('message', function(event) {
        if (event.data === 'botfusion-chat-close') {
            iframe.style.display = 'none';
            button.style.display = 'flex';
        }
    });
&lt;/script&gt;</pre>
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Direct Iframe Embed -->
    <div id="chat-button">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
    </div>

    <iframe id="chat-iframe" src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/embed/test-chat-id" allow="microphone"></iframe>

    <script>
        // Get the elements
        const button = document.getElementById('chat-button');
        const iframe = document.getElementById('chat-iframe');

        // Add click event to button
        button.onclick = function() {
            iframe.style.display = 'block';
            this.style.display = 'none';
        };

        // Add message listener for close events
        window.addEventListener('message', function(event) {
            if (event.data === 'botfusion-chat-close') {
                iframe.style.display = 'none';
                button.style.display = 'flex';
            }
        });
    </script>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        (function() {
            var oldConsoleLog = console.log;
            var oldConsoleError = console.error;
            var oldConsoleWarn = console.warn;
            var consoleOutput = document.getElementById('console-output');

            function appendToOutput(type, args) {
                var message = Array.from(args).map(arg => {
                    if (typeof arg === 'object') {
                        try {
                            return JSON.stringify(arg);
                        } catch (e) {
                            return String(arg);
                        }
                    }
                    return String(arg);
                }).join(' ');

                var pre = document.createElement('pre');
                pre.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
                pre.textContent = type.toUpperCase() + ': ' + message;
                consoleOutput.appendChild(pre);
            }

            console.log = function() {
                oldConsoleLog.apply(console, arguments);
                appendToOutput('log', arguments);
            };

            console.error = function() {
                oldConsoleError.apply(console, arguments);
                appendToOutput('error', arguments);
            };

            console.warn = function() {
                oldConsoleWarn.apply(console, arguments);
                appendToOutput('warn', arguments);
            };

            // Log any errors
            window.addEventListener('error', function(event) {
                appendToOutput('error', [event.message + ' at ' + event.filename + ':' + event.lineno]);
            });
        })();
    </script>
</body>
</html>
