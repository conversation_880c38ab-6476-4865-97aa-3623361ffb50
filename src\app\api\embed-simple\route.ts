import { readFileSync } from 'fs';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';

// This is a special API route that serves a simple CSP-friendly embed script
// It's designed to work with the strictest Content Security Policy settings
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId') || 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';

    // Read the embed script from the public directory
    const scriptPath = path.join(process.cwd(), 'public', 'simple-csp-embed.js');
    let scriptContent = readFileSync(scriptPath, 'utf8');

    // Replace the chat ID in the script
    scriptContent = scriptContent.replace('b76a3980-9f8e-47cd-ae7d-f02747552c4d', chatId);

    // Get the base URL from the environment or use the current origin
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;
    
    // Replace the base URL in the script
    scriptContent = scriptContent.replace('https://roo-bot-fusion-kgfs.vercel.app', baseUrl);

    // Return the script with proper CORS headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    });
  } catch (error) {
    console.error('Error serving simple CSP embed script:', error);
    return new NextResponse('console.error("Error loading BotFusion Chat Widget");', {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
      },
    });
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
    },
  });
}
