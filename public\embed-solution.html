<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Embed Solution</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0070f3;
            margin-bottom: 1rem;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
            background: white;
        }
        .code {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
            white-space: pre-wrap;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #0070f3;
            padding: 1rem;
            margin: 1rem 0;
        }
        .copy-button {
            background-color: #0070f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        .copy-button:hover {
            background-color: #0051a8;
        }
        .success {
            color: #4caf50;
            margin-left: 10px;
            display: none;
        }
        .demo {
            margin-top: 20px;
            border: 1px dashed #ccc;
            padding: 20px;
            border-radius: 8px;
            position: relative;
        }
        .demo-label {
            position: absolute;
            top: -10px;
            left: 10px;
            background: white;
            padding: 0 8px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>BotFusion Embed Solution</h1>

    <div class="container">
        <div class="card">
            <h2>Complete Embed Solution</h2>
            <p>This is a complete, self-contained solution for embedding the BotFusion chat widget on any website. Just copy and paste this code into your HTML.</p>

            <div class="code" id="embed-code">
&lt;!-- BotFusion Chat Widget --&gt;
&lt;style&gt;
  #botfusion-chat-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  #botfusion-chat-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  #botfusion-chat-iframe {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    display: none;
  }

  @media (max-width: 480px) {
    #botfusion-chat-iframe {
      width: 100%;
      height: 100%;
      bottom: 0;
      right: 0;
      border-radius: 0;
    }
  }
&lt;/style&gt;

&lt;div id="botfusion-chat-button"&gt;
  &lt;svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
    &lt;path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"&gt;&lt;/path&gt;
  &lt;/svg&gt;
&lt;/div&gt;

&lt;iframe id="botfusion-chat-iframe" src="https://roo-bot-fusion-kgfs.vercel.app/embed/YOUR_CHAT_ID" allow="microphone" loading="lazy" importance="high" title="Chat Widget"&gt;&lt;/iframe&gt;

&lt;script&gt;
  // Get the elements
  const button = document.getElementById('botfusion-chat-button');
  const iframe = document.getElementById('botfusion-chat-iframe');

  // Add click event to button
  button.onclick = function() {
    iframe.style.display = 'block';
    this.style.display = 'none';
  };

  // Add message listener for close events
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      iframe.style.display = 'none';
      button.style.display = 'flex';
    }
  });
&lt;/script&gt;
            </div>

            <button class="copy-button" onclick="copyEmbedCode()">Copy Embed Code</button>
            <span class="success" id="copy-success">Copied!</span>

            <div class="note">
                <strong>Note:</strong> Replace <code>b76a3980-9f8e-47cd-ae7d-f02747552c4d</code> with your own chat ID.
            </div>
        </div>

        <div class="card">
            <h2>How to Use</h2>
            <ol>
                <li>Copy the embed code above</li>
                <li>Paste it into your HTML file, just before the closing <code>&lt;/body&gt;</code> tag</li>
                <li>Replace the chat ID with your own</li>
                <li>Customize the colors and styles as needed</li>
            </ol>

            <div class="note">
                <strong>Important:</strong> This solution works with any website and doesn't require any external scripts or API calls. It's completely self-contained.
            </div>
        </div>

        <div class="card">
            <h2>Live Demo</h2>
            <p>Click the chat button in the bottom-right corner to see the widget in action.</p>

            <div class="demo">
                <span class="demo-label">Demo Content</span>
                <h3>Sample Website Content</h3>
                <p>This is a sample of what your website content might look like with the BotFusion chat widget embedded.</p>
                <p>The chat button appears in the bottom-right corner of the page, and when clicked, it opens the chat interface.</p>
            </div>
        </div>
    </div>

    <!-- Script to copy embed code -->
    <script>
        function copyEmbedCode() {
            const codeElement = document.getElementById('embed-code');
            const successElement = document.getElementById('copy-success');

            // Create a temporary textarea element
            const textarea = document.createElement('textarea');
            textarea.value = codeElement.textContent;
            document.body.appendChild(textarea);

            // Select and copy the text
            textarea.select();
            document.execCommand('copy');

            // Remove the temporary textarea
            document.body.removeChild(textarea);

            // Show success message
            successElement.style.display = 'inline';
            setTimeout(() => {
                successElement.style.display = 'none';
            }, 2000);
        }
    </script>

    <!-- BotFusion Chat Widget -->
    <style>
      #botfusion-chat-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        cursor: pointer;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      #botfusion-chat-button:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      }

      #botfusion-chat-iframe {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 400px;
        height: 600px;
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: none;
      }

      @media (max-width: 480px) {
        #botfusion-chat-iframe {
          width: 100%;
          height: 100%;
          bottom: 0;
          right: 0;
          border-radius: 0;
        }
      }
    </style>

    <div id="botfusion-chat-button">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
    </div>

    <iframe id="botfusion-chat-iframe" src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" allow="microphone" loading="lazy" importance="high" title="Chat Widget"></iframe>

    <script>
      // Get the elements
      const button = document.getElementById('botfusion-chat-button');
      const iframe = document.getElementById('botfusion-chat-iframe');

      // Add click event to button
      button.onclick = function() {
        iframe.style.display = 'block';
        this.style.display = 'none';
      };

      // Add message listener for close events
      window.addEventListener('message', function(event) {
        if (event.data === 'botfusion-chat-close') {
          iframe.style.display = 'none';
          button.style.display = 'flex';
        }
      });
    </script>
</body>
</html>
