'use client'

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'
import SpeechToText, { SpeechToTextRef } from '@/components/SpeechToText'
import TextToSpeech from '@/components/TextToSpeech'
import { isSpeechRecognitionSupported } from '@/lib/speech-recognition-utils'
import { SpeechRecognitionDebug, DebugLevel } from '@/lib/speech-recognition-debug'



export default function TestSpeechPage() {
  // State for speech recognition
  const [transcript, setTranscript] = useState('')
  const [isListening, setIsListening] = useState(false)
  const [microphoneActive, setMicrophoneActive] = useState(false)
  const [autoSend, setAutoSend] = useState(true)
  const [testResults, setTestResults] = useState<Array<{test: string, result: 'pass' | 'fail' | 'pending', details: string}>>([])
  const [logs, setLogs] = useState<string[]>([])
  const [selectedVoice, setSelectedVoice] = useState('nova')
  const [ttsText, setTtsText] = useState('This is a test of the text-to-speech functionality.')
  const [iframeUrl, setIframeUrl] = useState('')
  const [debugLevel, setDebugLevel] = useState<DebugLevel>(DebugLevel.INFO)
  const [browserCapabilities, setBrowserCapabilities] = useState<any>(null)

  // Refs
  const speechToTextRef = useRef<SpeechToTextRef>(null)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Add a log entry
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toISOString()}] ${message}`])
  }

  // Clear logs
  const clearLogs = () => {
    setLogs([])
  }

  // Run a test
  const runTest = (testName: string, testFn: () => Promise<{success: boolean, details: string}>) => {
    // Add the test to the results as pending
    setTestResults(prev => [
      ...prev,
      {
        test: testName,
        result: 'pending',
        details: 'Running test...'
      }
    ])

    // Run the test
    testFn()
      .then(result => {
        // Update the test result
        setTestResults(prev => prev.map(test =>
          test.test === testName
            ? {
                test: testName,
                result: result.success ? 'pass' : 'fail',
                details: result.details
              }
            : test
        ))

        // Show a toast
        if (result.success) {
          toast.success(`Test passed: ${testName}`)
        } else {
          toast.error(`Test failed: ${testName}`)
        }
      })
      .catch(error => {
        // Update the test result
        setTestResults(prev => prev.map(test =>
          test.test === testName
            ? {
                test: testName,
                result: 'fail',
                details: `Error: ${error.message}`
              }
            : test
        ))

        // Show a toast
        toast.error(`Test error: ${testName} - ${error.message}`)
      })
  }

  // Test browser support
  const testBrowserSupport = async () => {
    addLog('Testing browser support for speech recognition')
    const supported = isSpeechRecognitionSupported()
    addLog(`Browser support result: ${supported ? 'Supported' : 'Not supported'}`)
    return {
      success: supported,
      details: supported
        ? 'This browser supports speech recognition'
        : 'This browser does not support speech recognition'
    }
  }

  // Test microphone permissions
  const testMicrophonePermissions = async () => {
    addLog('Testing microphone permissions')
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      // Stop the stream immediately
      stream.getTracks().forEach(track => track.stop())
      addLog('Microphone permissions granted')
      return {
        success: true,
        details: 'Microphone permissions granted'
      }
    } catch (error) {
      addLog(`Microphone permissions error: ${error instanceof Error ? error.message : String(error)}`)
      return {
        success: false,
        details: `Microphone permissions denied: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  // Test speech recognition
  const testSpeechRecognition = async () => {
    addLog('Testing speech recognition')

    // First check if the browser supports speech recognition
    if (!isSpeechRecognitionSupported()) {
      addLog('Browser does not support speech recognition')
      return {
        success: false,
        details: 'Browser does not support speech recognition'
      }
    }

    // Then check if we have microphone permissions
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      // Stop the stream immediately
      stream.getTracks().forEach(track => track.stop())
    } catch (error) {
      addLog(`Microphone permissions error: ${error instanceof Error ? error.message : String(error)}`)
      return {
        success: false,
        details: `Microphone permissions denied: ${error instanceof Error ? error.message : String(error)}`
      }
    }

    // Now test speech recognition
    return new Promise<{success: boolean, details: string}>(resolve => {
      // Set a timeout to fail the test if it takes too long
      const timeout = setTimeout(() => {
        addLog('Speech recognition test timed out')
        setIsListening(false)
        resolve({
          success: false,
          details: 'Speech recognition test timed out after 10 seconds'
        })
      }, 10000)

      // Start listening
      addLog('Starting speech recognition')
      setIsListening(true)

      // Set up a listener for transcript changes
      const originalTranscript = transcript
      const checkInterval = setInterval(() => {
        if (transcript !== originalTranscript) {
          // We got a transcript, so the test passed
          clearTimeout(timeout)
          clearInterval(checkInterval)
          addLog(`Speech recognition successful: "${transcript}"`)
          setIsListening(false)
          resolve({
            success: true,
            details: `Speech recognition successful: "${transcript}"`
          })
        }
      }, 500)
    })
  }

  // Test text-to-speech
  const testTextToSpeech = async () => {
    addLog('Testing text-to-speech')

    // Use a simple test message
    const testMessage = 'This is a test of the text-to-speech functionality.'
    setTtsText(testMessage)

    // Wait for a moment to let the TTS component update
    await new Promise(resolve => setTimeout(resolve, 500))

    // The actual test will be manual since we can't programmatically verify audio playback
    addLog('Text-to-speech test complete - please verify audio playback manually')
    return {
      success: true,
      details: 'Text-to-speech test complete - please verify audio playback manually'
    }
  }

  // Run all tests
  const runAllTests = async () => {
    // Clear previous results
    setTestResults([])

    // Run each test in sequence
    await runTest('Browser Support', testBrowserSupport)
    await runTest('Microphone Permissions', testMicrophonePermissions)
    await runTest('Speech Recognition', testSpeechRecognition)
    await runTest('Text-to-Speech', testTextToSpeech)
  }

  // Initialize debug utilities and check browser capabilities
  useEffect(() => {
    // Set debug level
    SpeechRecognitionDebug.setDebugLevel(debugLevel);

    // Check browser capabilities
    const capabilities = SpeechRecognitionDebug.checkBrowserCapabilities();
    setBrowserCapabilities(capabilities);

    // Add to logs
    addLog('Debug initialized with level: ' + DebugLevel[debugLevel]);
    addLog('Browser capabilities checked');

    // Generate iframe URL
    setIframeUrl(`${window.location.origin}/test-speech-iframe`);

    // Set up message listener for iframe communication
    const handleMessage = (event: MessageEvent) => {
      // Make sure the message is from our iframe
      if (event.source === iframeRef.current?.contentWindow) {
        if (event.data && event.data.type === 'iframe-log') {
          addLog(`[IFRAME] ${event.data.message}`);
        } else if (event.data && event.data.type === 'iframe-ready') {
          addLog('[IFRAME] Ready message received');

          // Log iframe capabilities
          if (event.data.capabilities) {
            addLog('[IFRAME] Browser capabilities received');
          }
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [debugLevel]);

  // Handle debug level change
  const handleDebugLevelChange = (level: DebugLevel) => {
    setDebugLevel(level);
    SpeechRecognitionDebug.setDebugLevel(level);
    addLog('Debug level changed to: ' + DebugLevel[level]);
  };

  // Test audio output
  const testAudioOutput = async () => {
    addLog('Testing audio output...');
    const result = await SpeechRecognitionDebug.testAudioOutput();
    addLog(`Audio output test ${result ? 'succeeded' : 'failed'}`);
    toast(result ? 'Audio test played successfully' : 'Audio test failed');
  };

  // Handle transcript changes
  const handleTranscriptChange = (text: string) => {
    setTranscript(text)
    addLog(`Transcript updated: "${text}"`)
  }

  // Handle listening state changes
  const handleListeningChange = (listening: boolean) => {
    setIsListening(listening)
    addLog(`Listening state changed: ${listening ? 'started' : 'stopped'}`)
  }

  // Handle microphone active state changes
  const handleMicrophoneActiveChange = (active: boolean) => {
    setMicrophoneActive(active)
    addLog(`Microphone active state changed: ${active ? 'active' : 'inactive'}`)
  }

  // Handle send button click
  const handleSend = () => {
    addLog(`Message sent: "${transcript}"`)
    toast.success(`Message sent: "${transcript}"`)
    setTranscript('')
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Speech Recognition Test Page</h1>

      <Tabs defaultValue="test">
        <TabsList className="mb-4">
          <TabsTrigger value="test">Test Suite</TabsTrigger>
          <TabsTrigger value="manual">Manual Testing</TabsTrigger>
          <TabsTrigger value="iframe">Iframe Testing</TabsTrigger>
          <TabsTrigger value="debug">Debug</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="test">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Speech Recognition Test Suite</h2>
            <p className="mb-4">Run tests to verify speech recognition functionality.</p>

            <div className="flex gap-4 mb-6">
              <Button onClick={runAllTests}>Run All Tests</Button>
              <Button variant="outline" onClick={() => setTestResults([])}>Clear Results</Button>
            </div>

            <div className="space-y-4">
              {testResults.length === 0 ? (
                <p className="text-gray-500">No test results yet. Click "Run All Tests" to start testing.</p>
              ) : (
                testResults.map((test, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{test.test}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        test.result === 'pass' ? 'bg-green-100 text-green-800' :
                        test.result === 'fail' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {test.result === 'pass' ? 'PASS' :
                         test.result === 'fail' ? 'FAIL' :
                         'PENDING'}
                      </span>
                    </div>
                    <p className="text-sm mt-2">{test.details}</p>
                  </div>
                ))
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="manual">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Manual Testing</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-medium">Speech Recognition</h3>

                <div className="flex items-center gap-4">
                  <SpeechToText
                    onTranscript={handleTranscriptChange}
                    onListeningChange={handleListeningChange}
                    onMicrophoneActiveChange={handleMicrophoneActiveChange}
                    autoSend={autoSend}
                    onSend={handleSend}
                    ref={speechToTextRef}
                  />
                  <span>{isListening ? 'Listening...' : 'Click to speak'}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="auto-send"
                    checked={autoSend}
                    onCheckedChange={setAutoSend}
                  />
                  <Label htmlFor="auto-send">Auto-send on speech recognition</Label>
                </div>

                <div className="flex gap-2">
                  <Input
                    value={transcript}
                    onChange={(e) => setTranscript(e.target.value)}
                    placeholder="Transcript will appear here..."
                    className="flex-1"
                  />
                  <Button onClick={handleSend}>Send</Button>
                </div>

                <div className="mt-4">
                  <p>Microphone state: <span className="font-medium">{microphoneActive ? 'Active' : 'Inactive'}</span></p>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Text-to-Speech</h3>

                <div className="space-y-2">
                  <Label htmlFor="tts-voice">Voice</Label>
                  <select
                    id="tts-voice"
                    value={selectedVoice}
                    onChange={(e) => setSelectedVoice(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="nova">Nova</option>
                    <option value="shimmer">Shimmer</option>
                    <option value="echo">Echo</option>
                    <option value="fable">Fable</option>
                    <option value="onyx">Onyx</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tts-text">Text</Label>
                  <textarea
                    id="tts-text"
                    value={ttsText}
                    onChange={(e) => setTtsText(e.target.value)}
                    className="w-full p-2 border rounded-md h-24"
                    placeholder="Enter text to speak..."
                  />
                </div>

                <TextToSpeech
                  text={ttsText}
                  voice={selectedVoice}
                  autoPlay={false}
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="iframe">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Iframe Testing</h2>
            <p className="mb-4">Test speech recognition in an iframe context.</p>

            {iframeUrl && (
              <>
                <div className="mb-4">
                  <Label htmlFor="iframe-url">Iframe URL</Label>
                  <Input
                    id="iframe-url"
                    value={iframeUrl}
                    readOnly
                    className="mt-1"
                  />
                </div>

                <div className="flex gap-4 mb-4">
                  <Button onClick={() => {
                    if (iframeRef.current?.contentWindow) {
                      iframeRef.current.contentWindow.postMessage({
                        type: 'debug-command',
                        command: 'check-permissions'
                      }, '*');
                      addLog('Sent check-permissions command to iframe');
                    }
                  }}>
                    Check Iframe Permissions
                  </Button>

                  <Button onClick={() => {
                    if (iframeRef.current?.contentWindow) {
                      iframeRef.current.contentWindow.postMessage({
                        type: 'debug-command',
                        command: 'test-audio'
                      }, '*');
                      addLog('Sent test-audio command to iframe');
                    }
                  }}>
                    Test Iframe Audio
                  </Button>

                  <Button onClick={() => {
                    if (iframeRef.current?.contentWindow) {
                      iframeRef.current.contentWindow.postMessage({
                        type: 'debug-command',
                        command: 'set-debug-level',
                        level: debugLevel
                      }, '*');
                      addLog(`Sent set-debug-level command to iframe: ${DebugLevel[debugLevel]}`);
                    }
                  }}>
                    Sync Debug Level
                  </Button>
                </div>

                <div className="border rounded-md p-2 h-[500px]">
                  <iframe
                    ref={iframeRef}
                    src={iframeUrl}
                    className="w-full h-full"
                    allow="microphone; camera"
                  />
                </div>
              </>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="debug">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Debug Tools</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-medium">Debug Settings</h3>

                <div className="space-y-2">
                  <Label htmlFor="debug-level">Debug Level</Label>
                  <select
                    id="debug-level"
                    value={debugLevel}
                    onChange={(e) => handleDebugLevelChange(parseInt(e.target.value) as DebugLevel)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value={DebugLevel.NONE}>None</option>
                    <option value={DebugLevel.ERROR}>Error</option>
                    <option value={DebugLevel.WARN}>Warning</option>
                    <option value={DebugLevel.INFO}>Info</option>
                    <option value={DebugLevel.DEBUG}>Debug</option>
                    <option value={DebugLevel.VERBOSE}>Verbose</option>
                  </select>
                </div>

                <div className="flex gap-4">
                  <Button onClick={() => SpeechRecognitionDebug.checkMicrophonePermissions()}>
                    Check Microphone Permissions
                  </Button>
                  <Button onClick={testAudioOutput}>
                    Test Audio Output
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Browser Capabilities</h3>

                {browserCapabilities ? (
                  <div className="bg-gray-100 p-4 rounded-md h-[300px] overflow-y-auto">
                    <pre className="text-xs">
                      {JSON.stringify(browserCapabilities, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <p className="text-gray-500">No capability data available.</p>
                )}
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="logs">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Test Logs</h2>

            <div className="flex gap-4 mb-4">
              <Button variant="outline" onClick={clearLogs}>Clear Logs</Button>
            </div>

            <div className="bg-gray-100 p-4 rounded-md h-[400px] overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <p className="text-gray-500">No logs yet.</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
