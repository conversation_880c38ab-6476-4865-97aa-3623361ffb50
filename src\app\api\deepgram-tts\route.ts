import { NextRequest, NextResponse } from 'next/server';
import { processTextForTTS, validateTTSText } from '@/lib/tts-utils';
import { buildDeepgramTTSUrl, DEEPGRAM_HEADERS } from '@/lib/tts-config-standard';

// Configure as an edge function
export const runtime = 'edge';

// Maximum text length for Deepgram TTS API (2000 characters)
const MAX_TEXT_LENGTH = 2000;

// Simple in-memory cache for audio (limited to prevent memory issues)
const audioCache = new Map<string, { data: ArrayBuffer; timestamp: number }>();
const CACHE_TTL = 3600000; // 1 hour in milliseconds
const MAX_CACHE_SIZE = 100; // Maximum number of cached items

// Global rate limiting to prevent 429 errors - optimized for performance
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 800; // 800ms between requests (reduced from 1500ms for better performance)

// Helper function to enforce rate limiting
async function enforceRateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = Date.now();
}

// Helper function to get cached audio
function getCachedAudio(text: string, voice: string): ArrayBuffer | null {
  const cacheKey = `${voice}:${text}`;
  const cached = audioCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }

  // Remove expired entry
  if (cached) {
    audioCache.delete(cacheKey);
  }

  return null;
}

// Helper function to cache audio
function cacheAudio(text: string, voice: string, audioBuffer: ArrayBuffer): void {
  const cacheKey = `${voice}:${text}`;

  // Clean up old entries if cache is full
  if (audioCache.size >= MAX_CACHE_SIZE) {
    const oldestKey = audioCache.keys().next().value;
    audioCache.delete(oldestKey);
  }

  audioCache.set(cacheKey, {
    data: audioBuffer,
    timestamp: Date.now()
  });
}

export async function POST(request: NextRequest) {
  try {
    // Get the request body for POST requests
    const body = await request.json();
    const text = body.text;
    const preferredVoice = body.voiceModel;
    const format = body.format || 'mp3';
    const sampleRate = body.sampleRate || 24000;
    const isEmbed = body.embed === true;
    const isDirect = body.direct === true;
    const skipCache = body.skipCache === true;

    // Validate text is provided
    if (!text) {
      return NextResponse.json(
        {
          error: 'Missing parameter',
          message: 'Text parameter is required'
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Process text and get appropriate voice model
    const { processedText, voiceModel, language } = processTextForTTS(text || '', preferredVoice || undefined);

    // Validate processed text
    const validation = validateTTSText(processedText);
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: validation.error,
          message: validation.message
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Check cache if not skipping
    if (!skipCache) {
      const cachedAudio = getCachedAudio(processedText, voiceModel);
      if (cachedAudio) {
        return new NextResponse(cachedAudio, {
          headers: {
            'Content-Type': format === 'linear16' ? 'audio/wav' : 'audio/mpeg',
            'Content-Length': cachedAudio.byteLength.toString(),
            'X-Cache': 'HIT',
            'X-Language': language,
            'Cache-Control': 'public, max-age=86400',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
            'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type, X-Cache, X-Language',
            'Access-Control-Max-Age': '86400',
            'Cross-Origin-Resource-Policy': 'cross-origin',
            'Cross-Origin-Embedder-Policy': 'unsafe-none',
            'Cross-Origin-Opener-Policy': 'unsafe-none',
            'Accept-Ranges': 'bytes',
            'Timing-Allow-Origin': '*',
            'X-Content-Type-Options': 'nosniff'
          }
        });
      }
    }

    // Check if API key is available
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      return NextResponse.json(
        {
          error: 'Configuration error',
          message: 'Deepgram API key is not configured. Please add your Deepgram API key to the environment variables.'
        },
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Prepare the request payload according to Deepgram's API requirements
    const payload = {
      text: processedText
    };

    // Enforce rate limiting to prevent 429 errors
    await enforceRateLimit();

    // Build the URL with standardized configuration
    const apiUrl = buildDeepgramTTSUrl(voiceModel, format as 'linear16' | 'mp3');

    // Call Deepgram API with retry logic for 429 errors
    let response: Response;
    let retryCount = 0;
    const maxRetries = 2;

    while (retryCount <= maxRetries) {
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${deepgramApiKey}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Accept': format === 'linear16' ? 'audio/wav' : 'audio/mpeg'
        },
        body: JSON.stringify(payload)
      });

      // If successful or not a rate limit error, break out of retry loop
      if (response.ok || response.status !== 429) {
        break;
      }

      // If rate limited and we have retries left, wait and try again
      if (response.status === 429 && retryCount < maxRetries) {
        const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
        console.log(`Rate limited (429), retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries + 1})`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryCount++;
      } else {
        break;
      }
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Deepgram TTS API error: ${response.status} - ${errorText}`);

      return NextResponse.json(
        {
          error: 'Failed to generate speech',
          message: `Deepgram API request failed: ${response.status}`,
          status: response.status
        },
        {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Get the audio data
    const audioBuffer = await response.arrayBuffer();

    // Check if we received valid audio data
    if (audioBuffer.byteLength === 0) {
      return NextResponse.json(
        {
          error: 'Empty response',
          message: 'Received empty audio data from Deepgram'
        },
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Create comprehensive headers
    const headers: HeadersInit = {
      'Content-Type': format === 'linear16' ? 'audio/wav' : 'audio/mpeg',
      'Content-Length': audioBuffer.byteLength.toString(),
      'X-Language': language,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
      'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type, X-Language',
      'Access-Control-Max-Age': '86400',
      'Cross-Origin-Resource-Policy': 'cross-origin',
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
      'Accept-Ranges': 'bytes',
      'Timing-Allow-Origin': '*',
      'X-Content-Type-Options': 'nosniff'
    };

    // After successful API call, cache the audio
    if (!skipCache) {
      cacheAudio(processedText, voiceModel, audioBuffer);
    }

    return new NextResponse(audioBuffer, { headers });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred while processing the TTS request.';
    console.error(`TTS API internal error: ${errorMessage}`, error);

    return NextResponse.json(
      {
        error: 'Failed to generate speech',
        message: errorMessage
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the text and voice from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const text = searchParams.get('text');
    const preferredVoice = searchParams.get('voice');
    const isEmbed = searchParams.get('embed') === 'true';
    const isDirect = searchParams.get('direct') === 'true';
    const skipCache = searchParams.get('skipCache') === 'true';

    // Validate text is provided
    if (!text) {
      return NextResponse.json(
        {
          error: 'Missing parameter',
          message: 'Text parameter is required'
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Process text and get appropriate voice model
    const { processedText, voiceModel, language } = processTextForTTS(text || '', preferredVoice || undefined);

    // Validate processed text
    const validation = validateTTSText(processedText);
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: validation.error,
          message: validation.message
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Simple rate limiting: basic protection without Redis dependency
    // In production, consider implementing more sophisticated rate limiting

    // Check cache if not skipping
    if (!skipCache) {
      const cachedAudio = getCachedAudio(processedText, voiceModel);
      if (cachedAudio) {
        return new NextResponse(cachedAudio, {
          headers: {
            'Content-Type': 'audio/mpeg',
            'Content-Length': cachedAudio.byteLength.toString(),
            'X-Cache': 'HIT',
            'X-Language': language,
            'Cache-Control': 'public, max-age=86400',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
            'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type, X-Cache, X-Language',
            'Access-Control-Max-Age': '86400',
            'Cross-Origin-Resource-Policy': 'cross-origin',
            'Cross-Origin-Embedder-Policy': 'unsafe-none',
            'Cross-Origin-Opener-Policy': 'unsafe-none',
            'Accept-Ranges': 'bytes',
            'Timing-Allow-Origin': '*',
            'X-Content-Type-Options': 'nosniff'
          }
        });
      }
    }

    // Check if API key is available
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      return NextResponse.json(
        {
          error: 'Configuration error',
          message: 'Deepgram API key is not configured. Please add your Deepgram API key to the environment variables.'
        },
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Prepare the request payload according to Deepgram's API requirements
    const payload = {
      text: processedText
    };

    // Enforce rate limiting to prevent 429 errors
    await enforceRateLimit();

    // Call Deepgram API with retry logic for 429 errors
    let response: Response;
    let retryCount = 0;
    const maxRetries = 2;

    while (retryCount <= maxRetries) {
      // Call Deepgram API with standardized configuration
      response = await fetch(buildDeepgramTTSUrl(voiceModel, 'mp3'), {
        method: 'POST',
        headers: {
          ...DEEPGRAM_HEADERS,
          'Authorization': `Token ${deepgramApiKey}`,
          'Accept': 'audio/mpeg'
        },
        body: JSON.stringify(payload)
      });

      // If successful or not a rate limit error, break out of retry loop
      if (response.ok || response.status !== 429) {
        break;
      }

      // If rate limited and we have retries left, wait and try again
      if (response.status === 429 && retryCount < maxRetries) {
        const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
        console.log(`Rate limited (429), retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries + 1})`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryCount++;
      } else {
        break;
      }
    }

    if (!response.ok) {
      // Try to get error details from the response
      const errorText = await response.text();
      let errorMessage = `Deepgram API request failed: ${response.status}`;
      let errorDetails = '';
      let errorCode = 'DEEPGRAM_API_ERROR';

      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.message || errorData.error || errorMessage;
        errorDetails = JSON.stringify(errorData);

        // Extract specific error codes if available
        if (errorData.error_code) {
          errorCode = errorData.error_code;
        } else if (response.status === 401) {
          errorCode = 'UNAUTHORIZED';
        } else if (response.status === 403) {
          errorCode = 'FORBIDDEN';
        } else if (response.status === 429) {
          errorCode = 'RATE_LIMIT_EXCEEDED';
        } else if (response.status >= 500) {
          errorCode = 'SERVER_ERROR';
        }
      } catch (e) {
        // If JSON parsing fails, use the raw text if available
        if (errorText) errorMessage = errorText;
      }

      console.error(`Deepgram TTS API error: [${errorCode}] ${errorMessage}`);

      // Create a structured error response
      return NextResponse.json(
        {
          error: 'Failed to generate speech',
          error_code: errorCode,
          message: errorMessage,
          details: errorDetails || undefined,
          status: response.status,
          timestamp: new Date().toISOString()
        },
        {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'X-Error-Code': errorCode
          }
        }
      );
    }

    // Get the audio data
    const audioBuffer = await response.arrayBuffer();

    // Check if we received valid audio data
    if (audioBuffer.byteLength === 0) {
      return NextResponse.json(
        {
          error: 'Empty response',
          message: 'Received empty audio data from Deepgram'
        },
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Create comprehensive headers with enhanced security settings for all contexts
    const headers: HeadersInit = {
      'Content-Type': 'audio/mpeg',
      'Content-Length': audioBuffer.byteLength.toString(),
      'X-Language': language,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
      'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type, X-Language',
      'Access-Control-Max-Age': '86400',
      'Cross-Origin-Resource-Policy': 'cross-origin',
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
      'Accept-Ranges': 'bytes',
      'Timing-Allow-Origin': '*',
      'X-Content-Type-Options': 'nosniff'
    };

    // Explicitly remove X-Frame-Options header to ensure it's not set
    // This is critical for iframe embedding to work properly
    // Using delete operator on headers object
    if ('X-Frame-Options' in headers) {
      delete headers['X-Frame-Options'];
    }

    // For embedded contexts, add a special header
    if (isEmbed) {
      headers['X-Embed-Context'] = 'true';
    }

    // For direct contexts, add a special header
    if (isDirect) {
      headers['X-Direct-Context'] = 'true';
    }

    // After successful API call, cache the audio
    if (!skipCache) {
      cacheAudio(processedText, voiceModel, audioBuffer);
    }

    return new NextResponse(audioBuffer, { headers });
  } catch (error: unknown) {
    // Extract error message safely
    const errorMessage = error instanceof Error
      ? error.message
      : 'An unexpected error occurred while processing the TTS request.';

    // Extract error name if available
    const errorName = error instanceof Error ? error.name : 'UnknownError';

    // Generate an error code based on the error name
    const errorCode = `INTERNAL_${errorName.toUpperCase().replace(/[^A-Z0-9_]/g, '_')}`;

    // Log the error for debugging
    console.error(`TTS API internal error: [${errorCode}] ${errorMessage}`, error);

    // Include stack trace in development but not in production
    const errorStack = error instanceof Error && process.env.NODE_ENV === 'development'
      ? error.stack
      : undefined;

    // Generic error response with more details
    return NextResponse.json(
      {
        error: 'Failed to generate speech',
        error_code: errorCode,
        message: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'X-Error-Code': errorCode
        }
      }
    );
  }
}

// Handle OPTIONS request for CORS preflight
export async function OPTIONS(request: NextRequest) {
  // Check if this is an embedded context request
  const searchParams = request.nextUrl.searchParams;
  const isEmbed = searchParams.get('embed') === 'true';
  const isDirect = searchParams.get('direct') === 'true';

  // Create comprehensive headers for CORS
  const headers: HeadersInit = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma, Expires, Range, X-Requested-With, Accept',
    'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type',
    'Access-Control-Max-Age': '86400',
    'Access-Control-Allow-Credentials': 'true',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Accept-Ranges': 'bytes',
    'Timing-Allow-Origin': '*',
    'Cross-Origin-Resource-Policy': 'cross-origin',
    'Cross-Origin-Embedder-Policy': 'unsafe-none',
    'Cross-Origin-Opener-Policy': 'unsafe-none',
    'X-Content-Type-Options': 'nosniff',
    'Content-Security-Policy': "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self';"
  };

  // Explicitly remove X-Frame-Options header to ensure it's not set
  // This is critical for iframe embedding to work properly
  // Using delete operator on headers object
  if ('X-Frame-Options' in headers) {
    delete headers['X-Frame-Options'];
  }

  // For embedded contexts, add a special header
  if (isEmbed) {
    headers['X-Embed-Context'] = 'true';
  }

  // For direct contexts, add a special header
  if (isDirect) {
    headers['X-Direct-Context'] = 'true';
  }

  return new NextResponse(null, {
    status: 200,
    headers
  });
}
