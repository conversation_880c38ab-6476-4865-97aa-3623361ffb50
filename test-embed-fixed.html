<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #f0f0f0;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 4px 4px 4px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Fixed Embed Test</h1>
    
    <div class="note">
        <strong>Important:</strong> This page tests both embed code methods after the fixes.
        Please open your browser's developer console (F12 or right-click > Inspect > Console) 
        to check for any errors during the script loading process.
    </div>
    
    <div class="tabs">
        <div class="tab active" data-tab="script">Script Embed</div>
        <div class="tab" data-tab="csp">CSP-Friendly Embed</div>
    </div>
    
    <div class="tab-content active" id="script-tab">
        <h2>Script Embed Test</h2>
        <p>This tab tests the standard script embed code. The chat button should appear in the bottom-right corner.</p>
        
        <!-- BotFusion Chat Widget -->
        <script id="botfusion-chat-widget" 
            data-chat-id="1" 
            data-primary-color="#3b82f6" 
            data-origin="https://roo-bot-fusion-kgfs-gox0lkn8u-tellivisions-projects.vercel.app" 
            src="https://roo-bot-fusion-kgfs-gox0lkn8u-tellivisions-projects.vercel.app/api/embed-script">
        </script>
    </div>
    
    <div class="tab-content" id="csp-tab">
        <h2>CSP-Friendly Embed Test</h2>
        <p>This tab tests the CSP-friendly embed code. The chat button should appear in the bottom-right corner.</p>
        
        <!-- BotFusion Chat Widget - CSP-Friendly Version -->
        <script nonce="test-nonce" src="https://roo-bot-fusion-kgfs-gox0lkn8u-tellivisions-projects.vercel.app/api/csp-embed-script?chatId=1"></script>
    </div>
    
    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget and check for any errors in the console.</p>
        <p>Scroll down to see more content...</p>
        
        <div style="height: 500px;"></div>
        
        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
    </div>
    
    <!-- Error Tracking -->
    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });
        
        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all scripts and resources');
            
            // Log all iframes on the page
            const iframes = document.querySelectorAll('iframe');
            console.log(`Found ${iframes.length} iframes on the page:`);
            iframes.forEach((iframe, index) => {
                console.log(`Iframe #${index + 1}:`, {
                    id: iframe.id,
                    src: iframe.src,
                    width: iframe.style.width,
                    height: iframe.style.height
                });
            });
        });
        
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    // Hide all tab content
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // Show the corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId + '-tab').classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
