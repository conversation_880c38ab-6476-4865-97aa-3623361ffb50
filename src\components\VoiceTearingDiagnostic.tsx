'use client';

import React, { useState } from 'react';

interface DiagnosticResult {
  temperature: number;
  clampedTemperature: number;
  originalText: string;
  processedText: string;
  processed: boolean;
  tokensUsed: number;
  analysis: {
    issues: string[];
    score: number;
    riskLevel: string;
  };
  potentialIssues: boolean;
  error?: string;
}

interface DiagnosticResponse {
  originalText: string;
  results: DiagnosticResult[];
  summary: {
    totalTemperaturesTested: number;
    highRiskTemperatures: any[];
    mediumRiskTemperatures: any[];
    safestTemperature: any;
    riskiestTemperature: any;
  };
  recommendations: string[];
}

export default function VoiceTearingDiagnostic() {
  const [testText, setTestText] = useState('Hello, this is a test message with the number 14 and some creative content to analyze.');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<DiagnosticResponse | null>(null);
  const [selectedVoice, setSelectedVoice] = useState('aura-2-thalia-en');
  const [isTestingAudio, setIsTestingAudio] = useState(false);
  const [audioTestResults, setAudioTestResults] = useState<any[]>([]);

  const voices = [
    'aura-2-thalia-en',
    'aura-2-luna-en',
    'aura-2-stella-en',
    'aura-2-athena-en',
    'aura-2-hera-en',
    'aura-2-orion-en'
  ];

  const runTextAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/debug-natural-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText,
          temperatures: [0.1, 0.3, 0.5, 0.7, 0.8, 0.9, 1.0]
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Analysis failed:', error);
      alert('Analysis failed. Check console for details.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const testAudioAtTemperature = async (temperature: number, processedText: string) => {
    setIsTestingAudio(true);
    try {
      // Test with natural TTS endpoint
      const response = await fetch('/api/natural-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: processedText,
          voiceModel: selectedVoice,
          format: 'mp3',
          enableNaturalSpeech: true,
          naturalSpeechTemperature: temperature
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Play the audio
      const audio = new Audio(audioUrl);
      audio.play();

      // Add to test results
      setAudioTestResults(prev => [...prev, {
        temperature,
        voice: selectedVoice,
        audioUrl,
        timestamp: new Date().toISOString(),
        textLength: processedText.length
      }]);

    } catch (error) {
      console.error('Audio test failed:', error);
      alert(`Audio test failed for temperature ${temperature}`);
    } finally {
      setIsTestingAudio(false);
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          🔍 Voice Tearing Diagnostic Tool
        </h1>
        <p className="text-gray-600 mb-6">
          This tool analyzes how different creativity/temperature settings affect text generation 
          and helps identify the cause of voice tearing in TTS output.
        </p>

        {/* Test Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test Text
            </label>
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="Enter text to analyze..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Voice for Audio Testing
            </label>
            <select
              value={selectedVoice}
              onChange={(e) => setSelectedVoice(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {voices.map(voice => (
                <option key={voice} value={voice}>{voice}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 mb-6">
          <button
            onClick={runTextAnalysis}
            disabled={isAnalyzing || !testText.trim()}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze Text Generation'}
          </button>
        </div>

        {/* Results */}
        {results && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">📊 Analysis Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Temperatures Tested:</span>
                  <div>{results.summary.totalTemperaturesTested}</div>
                </div>
                <div>
                  <span className="font-medium">High Risk:</span>
                  <div className="text-red-600">{results.summary.highRiskTemperatures.length}</div>
                </div>
                <div>
                  <span className="font-medium">Safest Temperature:</span>
                  <div className="text-green-600">{results.summary.safestTemperature.temperature}</div>
                </div>
                <div>
                  <span className="font-medium">Riskiest Temperature:</span>
                  <div className="text-red-600">{results.summary.riskiestTemperature.temperature}</div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">💡 Recommendations</h3>
              <ul className="space-y-2">
                {results.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span className="text-sm text-gray-700">{rec}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Detailed Results */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800">🔬 Detailed Results</h3>
              {results.results.map((result, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center gap-4">
                      <span className="font-semibold text-lg">Temperature: {result.temperature}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(result.analysis.riskLevel)}`}>
                        {result.analysis.riskLevel.toUpperCase()} RISK
                      </span>
                      <span className="text-sm text-gray-600">Score: {result.analysis.score}/100</span>
                    </div>
                    <button
                      onClick={() => testAudioAtTemperature(result.temperature, result.processedText)}
                      disabled={isTestingAudio}
                      className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50"
                    >
                      🔊 Test Audio
                    </button>
                  </div>

                  {result.analysis.issues.length > 0 && (
                    <div className="mb-3">
                      <span className="font-medium text-red-600">Issues Found:</span>
                      <ul className="mt-1 space-y-1">
                        {result.analysis.issues.map((issue, issueIndex) => (
                          <li key={issueIndex} className="text-sm text-red-600 ml-4">
                            • {issue}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Original Text:</span>
                      <div className="mt-1 p-2 bg-gray-100 rounded text-xs">{result.originalText}</div>
                    </div>
                    <div>
                      <span className="font-medium">Processed Text:</span>
                      <div className="mt-1 p-2 bg-gray-100 rounded text-xs">{result.processedText}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Audio Test Results */}
        {audioTestResults.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">🎵 Audio Test Results</h3>
            <div className="space-y-2">
              {audioTestResults.map((test, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-4">
                    <span className="font-medium">Temp: {test.temperature}</span>
                    <span className="text-sm text-gray-600">Voice: {test.voice}</span>
                    <span className="text-sm text-gray-600">Length: {test.textLength} chars</span>
                  </div>
                  <audio controls className="h-8">
                    <source src={test.audioUrl} type="audio/mpeg" />
                  </audio>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
