-- Migration: Add voice selection and natural speech processing columns to chat_interfaces table
-- Date: 2025-01-15
-- Description: Adds support for voice selection and natural speech processing features

-- Add voice_model column for voice selection feature
-- Supports 6 Deepgram Aura voices: thalia, asteria, helena (female) | arcas, apollo, zeus (male)
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS voice_model TEXT DEFAULT 'thalia';

-- Add natural speech processing columns
-- enable_natural_speech: Toggle for natural speech processing
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS enable_natural_speech BOOLEAN DEFAULT true;

-- natural_speech_model: AI model for processing (gpt-4o-mini, gpt-4o, gpt-3.5-turbo)
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS natural_speech_model TEXT DEFAULT 'gpt-4o-mini';

-- natural_speech_temperature: Creativity level (0.0-1.0)
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS natural_speech_temperature DECIMAL(3,2) DEFAULT 0.7;

-- natural_speech_max_tokens: Maximum response length (100-1000)
ALTER TABLE chat_interfaces 
ADD COLUMN IF NOT EXISTS natural_speech_max_tokens INTEGER DEFAULT 500;

-- Add comments to document the new columns
COMMENT ON COLUMN chat_interfaces.voice_model IS 'Voice selection for TTS: thalia, asteria, helena, arcas, apollo, zeus';
COMMENT ON COLUMN chat_interfaces.enable_natural_speech IS 'Enable natural speech processing via GPT models';
COMMENT ON COLUMN chat_interfaces.natural_speech_model IS 'AI model for natural speech processing';
COMMENT ON COLUMN chat_interfaces.natural_speech_temperature IS 'Creativity level for natural speech (0.0-1.0)';
COMMENT ON COLUMN chat_interfaces.natural_speech_max_tokens IS 'Maximum tokens for natural speech response (100-1000)';

-- Create index for voice_model for faster queries
CREATE INDEX IF NOT EXISTS idx_chat_interfaces_voice_model ON chat_interfaces(voice_model);

-- Create index for natural speech settings for analytics
CREATE INDEX IF NOT EXISTS idx_chat_interfaces_natural_speech ON chat_interfaces(enable_natural_speech, natural_speech_model);
