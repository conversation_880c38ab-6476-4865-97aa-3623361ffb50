'use client'

import { motion } from 'framer-motion'
import { User } from '@supabase/supabase-js'

export default function UserProfile({ user }: { user: User | null }) {
  if (!user) return null

  // Extract username from email (everything before @)
  const username = user.email?.split('@')[0] || 'User'

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.3 }}
      className="flex flex-col items-center space-y-2"
    >
      {/* User avatar circle with first letter of username */}
      <div className="w-12 h-12 rounded-full bg-neon-blue/20 border border-neon-blue/50 flex items-center justify-center text-neon-blue font-bold text-lg mb-2">
        {username.charAt(0).toUpperCase()}
      </div>

      {/* Username */}
      <div className="text-center">
        <div className="text-base font-medium text-white">{username}</div>
      </div>
    </motion.div>
  )
}