'use client'

import { motion } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card } from '@/components/ui/card'
import { toast } from 'sonner'
import { UserTier, getTierDisplayName, getUserProfile } from '@/lib/tiers'

interface UserProfileData {
  tier: UserTier
  subscription_status: string
  subscription_start_date?: string
  subscription_end_date?: string
}

export default function UserProfile({ user }: { user: User | null }) {
  const [userProfile, setUserProfile] = useState<UserProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [showTierManagement, setShowTierManagement] = useState(false)

  if (!user) return null

  // Extract username from email (everything before @)
  const username = user.email?.split('@')[0] || 'User'
  const isAdmin = user.email === '<EMAIL>'

  useEffect(() => {
    fetchUserProfile()
  }, [user])

  const fetchUserProfile = async () => {
    try {
      setLoading(true)
      const profile = await getUserProfile(user.id)
      setUserProfile(profile)
    } catch (error) {
      console.error('Error fetching user profile:', error)
      // Default to free tier if no profile exists
      setUserProfile({
        tier: 'free',
        subscription_status: 'active'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateTier = async (newTier: UserTier) => {
    try {
      setUpdating(true)

      const response = await fetch('/api/admin/set-user-tier', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          tier: newTier
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update tier')
      }

      toast.success(data.message)
      fetchUserProfile() // Refresh profile
    } catch (error) {
      console.error('Error updating tier:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update tier')
    } finally {
      setUpdating(false)
    }
  }

  const getTierBadgeColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-500'
      case 'standard': return 'bg-blue-500'
      case 'pro': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.3 }}
      className="flex flex-col space-y-4"
    >
      {/* User avatar circle with first letter of username */}
      <div className="flex flex-col items-center space-y-2">
        <div className="w-12 h-12 rounded-full bg-neon-blue/20 border border-neon-blue/50 flex items-center justify-center text-neon-blue font-bold text-lg">
          {username.charAt(0).toUpperCase()}
        </div>

        {/* Username */}
        <div className="text-center">
          <div className="text-base font-medium text-white">{username}</div>
          <div className="text-xs text-gray-400">{user.email}</div>
        </div>

        {/* Tier Badge */}
        {!loading && userProfile && (
          <Badge className={`${getTierBadgeColor(userProfile.tier)} text-white`}>
            {getTierDisplayName(userProfile.tier)}
          </Badge>
        )}
      </div>

      {/* Tier Management for Admin */}
      {isAdmin && (
        <div className="space-y-2">
          <Button
            onClick={() => setShowTierManagement(!showTierManagement)}
            className="w-full text-xs bg-purple-600 hover:bg-purple-700 text-white"
          >
            {showTierManagement ? 'Hide' : 'Manage'} Tier
          </Button>

          {showTierManagement && !loading && userProfile && (
            <Card className="bg-white/10 border-gray-600 p-3">
              <div className="space-y-2">
                <div className="text-xs text-white font-medium">Current Tier:</div>
                <Select
                  value={userProfile.tier}
                  onValueChange={(value) => updateTier(value as UserTier)}
                  disabled={updating}
                >
                  <SelectTrigger className="w-full bg-white/20 border-gray-600 text-white text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">
                      <div className="flex items-center gap-2">
                        <span>🆓</span>
                        <span>Free</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="standard">
                      <div className="flex items-center gap-2">
                        <span>⭐</span>
                        <span>Standard</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="pro">
                      <div className="flex items-center gap-2">
                        <span>💎</span>
                        <span>Pro</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>

                <div className="text-xs text-gray-400">
                  Status: {userProfile.subscription_status}
                </div>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Admin Panel Link */}
      {isAdmin && (
        <Button
          onClick={() => window.open('/admin/tiers', '_blank')}
          className="w-full text-xs bg-blue-600 hover:bg-blue-700 text-white"
        >
          🎯 Admin Panel
        </Button>
      )}
    </motion.div>
  )
}