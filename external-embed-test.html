<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>External BotFusion Embed Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0070f3;
            margin-bottom: 1rem;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
            background: white;
        }
        .code {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
        .iframe-container {
            width: 100%;
            height: 500px;
            border: 1px solid #eaeaea;
            border-radius: 10px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #0070f3;
            padding: 1rem;
            margin: 1rem 0;
        }
        .log {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .error { color: #d32f2f; }
        .warn { color: #f57c00; }
        .info { color: #0288d1; }
    </style>
</head>
<body>
    <h1>External BotFusion Embed Test</h1>
    
    <div class="container">
        <div class="card">
            <h2>Method 1: Direct Script Embed</h2>
            <p>This method adds a floating chat button to your website that opens the chat in a popup.</p>
            
            <div class="code">
&lt;script 
    src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
    data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
    data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
    async 
    defer
&gt;&lt;/script&gt;
            </div>
            
            <div class="note">
                <strong>Note:</strong> The chat button should appear in the bottom-right corner of this page.
            </div>
        </div>
        
        <div class="card">
            <h2>Method 2: Iframe Embed</h2>
            <p>This method embeds the chat directly in your page using an iframe.</p>
            
            <div class="iframe-container">
                <iframe 
                    src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                    allow="microphone" 
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                    loading="lazy"
                    importance="high"
                    referrerpolicy="origin"
                    fetchpriority="high"
                    title="BotFusion Chat"
                ></iframe>
            </div>
        </div>

        <div class="card">
            <h2>Console Output</h2>
            <div id="console-output" class="log"></div>
        </div>
    </div>

    <!-- Script to test embedding -->
    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        
        // Store original console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods
        console.log = function() {
            // Call original method
            originalConsole.log.apply(console, arguments);
            
            // Add to our display
            const logElement = document.createElement('div');
            logElement.className = 'log-entry';
            logElement.textContent = Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.error = function() {
            originalConsole.error.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry error';
            logElement.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.warn = function() {
            originalConsole.warn.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry warn';
            logElement.textContent = 'WARNING: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.info = function() {
            originalConsole.info.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry info';
            logElement.textContent = 'INFO: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        // Log script loading
        console.info('Page loaded, waiting for BotFusion script to initialize...');

        // Check if script loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.BotFusionChat) {
                    console.info('BotFusion script loaded successfully!');
                } else {
                    console.error('BotFusion script not found after 3 seconds');
                    
                    // Try to load the script manually
                    console.info('Attempting to load script manually...');
                    
                    const script = document.createElement('script');
                    script.src = 'https://roo-bot-fusion-kgfs.vercel.app/api/embed-script';
                    script.dataset.chatId = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';
                    script.dataset.baseUrl = 'https://roo-bot-fusion-kgfs.vercel.app';
                    script.async = true;
                    script.defer = true;
                    
                    script.onload = function() {
                        console.info('Script loaded manually!');
                    };
                    
                    script.onerror = function(error) {
                        console.error('Error loading script manually:', error);
                    };
                    
                    document.body.appendChild(script);
                }
            }, 3000);
        });
    </script>

    <!-- Direct Script Embed -->
    <script 
        src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
        data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
        data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
        async 
        defer
    ></script>
</body>
</html>
