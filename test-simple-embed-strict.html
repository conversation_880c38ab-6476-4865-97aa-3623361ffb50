<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Embed with Strict CSP</title>
    <!-- This CSP only allows unsafe-inline scripts and the BotFusion domain -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app; style-src 'unsafe-inline'; frame-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app; connect-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Simple Embed with Strict CSP</h1>

    <div class="important">
        <strong>Important:</strong> This page has a very strict Content Security Policy:
        <pre>default-src 'none';
script-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app;
style-src 'unsafe-inline';
frame-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app;
connect-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app</pre>
        This blocks all resources except:
        <ul>
            <li>Inline scripts</li>
            <li>Scripts from the BotFusion domain</li>
            <li>Inline styles</li>
            <li>Frames from the BotFusion domain</li>
            <li>Network connections to the BotFusion domain</li>
        </ul>
    </div>

    <div class="content">
        <h2>Simple Embed Test with Strict CSP</h2>
        <p>This test demonstrates the simple embed code working with a very strict CSP setting.</p>
        <p>The chat button should appear in the bottom-right corner of the page.</p>
        <p>Please check the browser console for any errors.</p>
    </div>

    <div class="content">
        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
    </div>

    <!-- Simple Embed Code -->
    <iframe src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/simple-embed.html?chatId=b76a3980-9f8e-47cd-ae7d-f02747552c4d&baseUrl=https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app"
            style="position: fixed; bottom: 20px; right: 20px; width: 70px; height: 70px; border: none; z-index: 9999;"
            scrolling="no"
            frameborder="0"
            allow="microphone"></iframe>
</body>
</html>
