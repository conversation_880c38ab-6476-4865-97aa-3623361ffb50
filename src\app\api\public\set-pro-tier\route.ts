import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({ 
        error: 'Email is required' 
      }, { status: 400 })
    }

    console.log('Setting Pro tier for email:', email)

    // Use service client for admin operations
    const supabase = createServiceClient()

    // First, get the user ID from auth.users
    const { data: authUser, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.error('Error fetching users:', authError)
      return NextResponse.json({ 
        error: 'Failed to fetch users',
        details: authError.message
      }, { status: 500 })
    }

    const user = authUser.users.find(u => u.email === email)
    
    if (!user) {
      return NextResponse.json({ 
        error: 'User not found',
        details: `No user found with email: ${email}`
      }, { status: 404 })
    }

    console.log('Found user:', user.id, user.email)

    // Check if user profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('id, tier')
      .eq('id', user.id)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching user profile:', fetchError)
      return NextResponse.json({ 
        error: 'Failed to fetch user profile',
        details: fetchError.message
      }, { status: 500 })
    }

    const now = new Date().toISOString()
    const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()

    if (existingProfile) {
      // Update existing profile
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ 
          tier: 'pro',
          subscription_status: 'active',
          subscription_start_date: now,
          subscription_end_date: oneYearFromNow,
          updated_at: now
        })
        .eq('id', user.id)

      if (updateError) {
        console.error('Error updating user profile:', updateError)
        return NextResponse.json({ 
          error: 'Failed to update user tier',
          details: updateError.message
        }, { status: 500 })
      }

      console.log('Updated user profile to Pro tier')

      return NextResponse.json({ 
        message: `User tier updated from ${existingProfile.tier} to pro`,
        userId: user.id,
        email: user.email,
        previousTier: existingProfile.tier,
        newTier: 'pro'
      })
    } else {
      // Create new profile
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert({
          id: user.id,
          tier: 'pro',
          subscription_status: 'active',
          subscription_start_date: now,
          subscription_end_date: oneYearFromNow,
          created_at: now,
          updated_at: now
        })

      if (insertError) {
        console.error('Error creating user profile:', insertError)
        return NextResponse.json({ 
          error: 'Failed to create user profile',
          details: insertError.message
        }, { status: 500 })
      }

      console.log('Created new user profile with Pro tier')

      return NextResponse.json({ 
        message: `User profile created with pro tier`,
        userId: user.id,
        email: user.email,
        newTier: 'pro'
      })
    }
  } catch (error) {
    console.error('Error in set-pro-tier:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check if the service is working
export async function GET() {
  return NextResponse.json({
    message: 'Pro tier setting service is active',
    usage: 'POST with { "email": "<EMAIL>" } to set Pro tier'
  })
}
