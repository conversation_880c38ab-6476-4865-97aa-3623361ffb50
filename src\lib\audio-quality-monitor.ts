/**
 * Audio Quality Monitoring Utilities
 * Helps detect potential voice tearing and audio artifacts in TTS output
 */

export interface AudioQualityMetrics {
  textLength: number;
  temperature: number;
  voiceModel: string;
  processingTime: number;
  audioSize: number;
  potentialIssues: string[];
  qualityScore: number;
  timestamp: string;
}

export interface TextAnalysisResult {
  hasUnusualPunctuation: boolean;
  hasExcessiveCapitalization: boolean;
  hasUnusualCharacters: boolean;
  hasLongSentences: boolean;
  hasRepeatedWords: boolean;
  hasInconsistentSpacing: boolean;
  riskScore: number;
  issues: string[];
}

/**
 * Analyze text for potential TTS quality issues
 */
export function analyzeTextQuality(text: string): TextAnalysisResult {
  const issues: string[] = [];
  let riskScore = 0;

  // Check for unusual punctuation patterns
  const unusualPunctuation = /[!]{2,}|[?]{2,}|[.]{3,}|[,]{2,}/g;
  const punctuationMatches = text.match(unusualPunctuation);
  const hasUnusualPunctuation = !!punctuationMatches;
  if (hasUnusualPunctuation) {
    issues.push(`Unusual punctuation: ${punctuationMatches!.join(', ')}`);
    riskScore += 15;
  }

  // Check for excessive capitalization
  const excessiveCaps = /[A-Z]{3,}/g;
  const capsMatches = text.match(excessiveCaps);
  const hasExcessiveCapitalization = !!capsMatches;
  if (hasExcessiveCapitalization) {
    issues.push(`Excessive caps: ${capsMatches!.join(', ')}`);
    riskScore += 20;
  }

  // Check for unusual characters
  const unusualChars = /[^\w\s.,!?;:'"()-]/g;
  const charMatches = text.match(unusualChars);
  const hasUnusualCharacters = !!charMatches;
  if (hasUnusualCharacters) {
    issues.push(`Unusual characters: ${charMatches!.join(', ')}`);
    riskScore += 25;
  }

  // Check for very long sentences (>150 chars)
  const sentences = text.split(/[.!?]+/);
  const longSentences = sentences.filter(s => s.trim().length > 150);
  const hasLongSentences = longSentences.length > 0;
  if (hasLongSentences) {
    issues.push(`${longSentences.length} long sentences may affect prosody`);
    riskScore += 10 * longSentences.length;
  }

  // Check for repeated words or phrases
  const words = text.toLowerCase().split(/\s+/);
  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    if (word.length > 3) {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  });
  const repeatedWords = Object.entries(wordCounts).filter(([_, count]) => count > 3);
  const hasRepeatedWords = repeatedWords.length > 0;
  if (hasRepeatedWords) {
    issues.push(`Repeated words: ${repeatedWords.map(([word, count]) => `${word}(${count})`).join(', ')}`);
    riskScore += 5 * repeatedWords.length;
  }

  // Check for inconsistent spacing
  const hasInconsistentSpacing = /\s{2,}/.test(text);
  if (hasInconsistentSpacing) {
    issues.push('Inconsistent spacing detected');
    riskScore += 5;
  }

  return {
    hasUnusualPunctuation,
    hasExcessiveCapitalization,
    hasUnusualCharacters,
    hasLongSentences,
    hasRepeatedWords,
    hasInconsistentSpacing,
    riskScore,
    issues
  };
}

/**
 * Monitor audio quality metrics for TTS requests
 */
export function monitorAudioQuality(
  text: string,
  temperature: number,
  voiceModel: string,
  processingTime: number,
  audioSize: number,
  processedText?: string
): AudioQualityMetrics {
  const textAnalysis = analyzeTextQuality(processedText || text);
  const potentialIssues: string[] = [];
  let qualityScore = 100;

  // Temperature-based risk assessment
  if (temperature > 0.8) {
    potentialIssues.push(`High temperature (${temperature}) may cause creative text issues`);
    qualityScore -= 15;
  }

  // Text quality issues
  if (textAnalysis.riskScore > 0) {
    potentialIssues.push(...textAnalysis.issues);
    qualityScore -= Math.min(textAnalysis.riskScore, 50);
  }

  // Processing time anomalies
  const expectedProcessingTime = text.length * 0.1; // Rough estimate: 0.1ms per character
  if (processingTime > expectedProcessingTime * 3) {
    potentialIssues.push(`Slow processing time: ${processingTime}ms (expected ~${expectedProcessingTime}ms)`);
    qualityScore -= 10;
  }

  // Audio size anomalies
  const expectedAudioSize = text.length * 100; // Rough estimate: 100 bytes per character
  if (audioSize < expectedAudioSize * 0.5) {
    potentialIssues.push(`Unusually small audio file: ${audioSize} bytes (expected ~${expectedAudioSize})`);
    qualityScore -= 20;
  } else if (audioSize > expectedAudioSize * 3) {
    potentialIssues.push(`Unusually large audio file: ${audioSize} bytes (expected ~${expectedAudioSize})`);
    qualityScore -= 10;
  }

  // Text length vs audio size ratio check
  const bytesPerChar = audioSize / text.length;
  if (bytesPerChar < 50) {
    potentialIssues.push(`Low audio quality suspected: ${bytesPerChar.toFixed(1)} bytes/char`);
    qualityScore -= 15;
  }

  return {
    textLength: text.length,
    temperature,
    voiceModel,
    processingTime,
    audioSize,
    potentialIssues,
    qualityScore: Math.max(0, qualityScore),
    timestamp: new Date().toISOString()
  };
}

/**
 * Log audio quality metrics for monitoring
 */
export function logAudioQuality(metrics: AudioQualityMetrics): void {
  const riskLevel = metrics.qualityScore > 80 ? 'LOW' : metrics.qualityScore > 60 ? 'MEDIUM' : 'HIGH';
  
  console.log(`🎵 Audio Quality Monitor - Risk Level: ${riskLevel}`);
  console.log(`📊 Quality Score: ${metrics.qualityScore}/100`);
  console.log(`🌡️ Temperature: ${metrics.temperature}`);
  console.log(`🎤 Voice: ${metrics.voiceModel}`);
  console.log(`📝 Text Length: ${metrics.textLength} chars`);
  console.log(`⏱️ Processing Time: ${metrics.processingTime}ms`);
  console.log(`💾 Audio Size: ${metrics.audioSize} bytes`);
  
  if (metrics.potentialIssues.length > 0) {
    console.log(`⚠️ Potential Issues (${metrics.potentialIssues.length}):`);
    metrics.potentialIssues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }

  // Log warning for high-risk scenarios
  if (riskLevel === 'HIGH') {
    console.warn(`🚨 HIGH RISK: Voice tearing likely with current settings`);
  } else if (riskLevel === 'MEDIUM') {
    console.warn(`⚠️ MEDIUM RISK: Monitor audio quality closely`);
  }
}

/**
 * Get recommendations based on quality metrics
 */
export function getQualityRecommendations(metrics: AudioQualityMetrics): string[] {
  const recommendations: string[] = [];

  if (metrics.temperature > 0.8) {
    recommendations.push(`Reduce temperature from ${metrics.temperature} to 0.7 or lower to improve text quality`);
  }

  if (metrics.qualityScore < 60) {
    recommendations.push('Consider using a different voice model or reducing creativity settings');
  }

  if (metrics.potentialIssues.some(issue => issue.includes('punctuation'))) {
    recommendations.push('Add text preprocessing to normalize punctuation before TTS');
  }

  if (metrics.potentialIssues.some(issue => issue.includes('Repeated words'))) {
    recommendations.push('Implement text post-processing to reduce word repetition');
  }

  if (metrics.potentialIssues.some(issue => issue.includes('long sentences'))) {
    recommendations.push('Break long sentences into shorter chunks for better prosody');
  }

  if (recommendations.length === 0) {
    recommendations.push('Audio quality appears good with current settings');
  }

  return recommendations;
}
