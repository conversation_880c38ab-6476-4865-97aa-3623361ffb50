import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // Get parameters from the query string
    const chatId = request.nextUrl.searchParams.get('chatId') || 'default';
    const bypass = request.nextUrl.searchParams.get('bypass') || '';
    const primaryColor = request.nextUrl.searchParams.get('primaryColor') || '#3b82f6';
    const debug = request.nextUrl.searchParams.get('debug') === 'true';

    // Get the base URL from the environment or use the current origin
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;
    
    // Read the mobile-optimized embed script from the public directory
    const scriptPath = path.join(process.cwd(), 'public', 'mobile-optimized-embed.js');
    let scriptContent = readFileSync(scriptPath, 'utf8');

    // Replace configuration placeholders in the script
    scriptContent = scriptContent.replace(
      /window\.BOTFUSION_CHAT_ID \|\| 'default'/g,
      `'${chatId}'`
    );
    
    scriptContent = scriptContent.replace(
      /window\.BOTFUSION_BASE_URL \|\| 'https:\/\/roo-bot-fusion-kgfs\.vercel\.app'/g,
      `'${baseUrl}'`
    );
    
    scriptContent = scriptContent.replace(
      /window\.BOTFUSION_PRIMARY_COLOR \|\| '#3b82f6'/g,
      `'${primaryColor}'`
    );
    
    scriptContent = scriptContent.replace(
      /window\.BOTFUSION_DEBUG \|\| false/g,
      debug.toString()
    );

    // Add bypass parameter to iframe URLs if provided
    if (bypass) {
      scriptContent = scriptContent.replace(
        /iframe\.src = `\${config\.baseUrl}\/embed\/\${config\.chatId}\?/g,
        `iframe.src = \`\${config.baseUrl}/embed/\${config.chatId}?x-vercel-protection-bypass=${bypass}&`
      );
    }

    // Return the script with proper headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        // Add version header for debugging
        'X-BotFusion-Widget-Version': '2.0.0-mobile-optimized',
        'X-BotFusion-Mobile-Fix': 'true'
      }
    });

  } catch (error) {
    console.error('Error serving mobile-optimized embed script:', error);
    
    // Return a fallback script that shows an error message
    const fallbackScript = `
console.error('BotFusion Mobile Widget: Failed to load script');
console.error('Error details:', ${JSON.stringify(error instanceof Error ? error.message : 'Unknown error')});

// Create a simple error notification
(function() {
  var errorDiv = document.createElement('div');
  errorDiv.style.cssText = 'position:fixed;bottom:20px;right:20px;background:#ff4444;color:white;padding:10px;border-radius:8px;z-index:9999;font-family:sans-serif;font-size:12px;max-width:300px;';
  errorDiv.innerHTML = 'BotFusion Chat Widget failed to load. Please check your internet connection and try again.';
  document.body.appendChild(errorDiv);
  
  // Auto-remove after 10 seconds
  setTimeout(function() {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 10000);
})();
`;

    return new NextResponse(fallbackScript, {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'X-BotFusion-Error': 'true'
      }
    });
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
      'Access-Control-Max-Age': '86400',
    }
  });
}
