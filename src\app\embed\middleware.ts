import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// This middleware specifically handles the embed routes
export function middleware(request: NextRequest) {
  // Create a response with the proper headers
  const response = NextResponse.next()

  // Remove X-Frame-Options header completely
  response.headers.delete('X-Frame-Options')

  // Set comprehensive CORS headers
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept')
  response.headers.set('Access-Control-Allow-Credentials', 'false')
  response.headers.set('Access-Control-Max-Age', '86400')

  // Set a permissive Content-Security-Policy that allows embedding, audio, and Deepgram STT
  response.headers.set('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "media-src 'self' data: https: blob:; " +
    "connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; " +
    "frame-src *; " +
    "frame-ancestors *; " +
    "font-src 'self' data:;"
  )

  // Add cross-origin headers
  response.headers.set('Cross-Origin-Embedder-Policy', 'unsafe-none')
  response.headers.set('Cross-Origin-Opener-Policy', 'unsafe-none')
  response.headers.set('Cross-Origin-Resource-Policy', 'cross-origin')

  return response
}

// Only run this middleware on embed routes
export const config = {
  matcher: ['/embed/:path*'],
}
