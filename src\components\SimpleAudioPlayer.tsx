'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { MAX_TTS_TEXT_LENGTH, splitTextIntoChunks, validateTTSText } from '@/lib/tts-utils';

// Add fetch to Window interface for our polyfill
declare global {
  interface Window {
    fetch: typeof fetch;
    globalAudioManager?: {
      stopAllAudio: () => void;
      currentAudioElement?: HTMLAudioElement;
    };
  }
}

// Global Audio Manager - Singleton pattern to prevent multiple audio instances
const createGlobalAudioManager = () => {
  if (typeof window === 'undefined') return null;

  if (!window.globalAudioManager) {
    window.globalAudioManager = {
      currentAudioElement: undefined,
      stopAllAudio: () => {
        console.log('GLOBAL AUDIO MANAGER: Stopping all audio');

        // Stop the current tracked audio element
        if (window.globalAudioManager?.currentAudioElement) {
          try {
            const audio = window.globalAudioManager.currentAudioElement;
            audio.pause();
            audio.currentTime = 0;
            audio.removeAttribute('src');
            audio.src = '';
            audio.load();
            console.log('GLOBAL AUDIO MANAGER: Stopped tracked audio element');
          } catch (e) {
            console.log('GLOBAL AUDIO MANAGER: Error stopping tracked audio:', e);
          }
        }

        // Stop all audio elements in DOM as backup
        const allAudioElements = document.querySelectorAll('audio');
        allAudioElements.forEach((audio, index) => {
          try {
            if (!audio.paused) {
              audio.pause();
              audio.currentTime = 0;
              console.log(`GLOBAL AUDIO MANAGER: Stopped DOM audio element ${index}`);
            }
            audio.removeAttribute('src');
            audio.src = '';
            audio.load();
          } catch (e) {
            console.log(`GLOBAL AUDIO MANAGER: Error stopping DOM audio element ${index}:`, e);
          }
        });

        // Clear the current audio reference
        window.globalAudioManager!.currentAudioElement = undefined;
      }
    };
  }

  return window.globalAudioManager;
};

interface SimpleAudioPlayerProps {
  text: string;
  voice?: string;
  autoPlay?: boolean;
  onError?: (error: string) => void;
  onAudioReady?: (messageId?: string) => void;
  messageId?: string; // Add messageId prop
  enableNaturalSpeech?: boolean; // Add natural speech processing option
}

const SimpleAudioPlayer: React.FC<SimpleAudioPlayerProps> = ({
  text,
  voice = 'thalia', // Changed to female voice
  autoPlay = false,
  onError,
  onAudioReady,
  messageId: providedMessageId, // Accept messageId prop
  enableNaturalSpeech = true, // Enable natural speech processing by default
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentChunkIndex, setCurrentChunkIndex] = useState(0);
  const [textChunks, setTextChunks] = useState<string[]>([]);

  // Simplified state tracking
  const isPlayingRef = useRef<boolean>(false);
  // Track audio ready callbacks by message ID instead of a single boolean
  const audioReadyCalledRef = useRef<{[messageId: string]: boolean}>({});
  // Store all timeouts for proper cleanup
  const timeoutsRef = useRef<{[key: string]: NodeJS.Timeout}>({});
  // Circuit breaker to prevent infinite loops
  const retryCountRef = useRef<number>(0);
  const MAX_RETRIES = 3;


  // Store event listeners in refs to properly remove them later
  const errorListenerRef = useRef<((event: Event) => void) | null>(null);
  const endedListenerRef = useRef<(() => void) | null>(null);

  // Store the last processed text to prevent duplicate processing
  const lastProcessedTextRef = useRef<string>('');
  const lastProcessedTimestampRef = useRef<number>(0);

  // Track currently processing message to prevent duplicate processing
  const currentlyProcessingRef = useRef<string | null>(null);

  // Generate a unique ID for each text to track it, but prefer provided messageId
  const getMessageId = useCallback((text: string) => {
    // Use provided messageId if available, otherwise generate one
    return providedMessageId || `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }, [providedMessageId]);

  // Function to set a timeout with automatic tracking for cleanup
  const setManagedTimeout = useCallback((callback: () => void, delay: number, timeoutKey: string) => {
    // Clear any existing timeout with this key
    if (timeoutsRef.current[timeoutKey]) {
      clearTimeout(timeoutsRef.current[timeoutKey]);
      delete timeoutsRef.current[timeoutKey];
    }

    // Set the new timeout and store it in the ref
    const timeoutId = setTimeout(() => {
      // Remove the timeout from the ref when it completes
      delete timeoutsRef.current[timeoutKey];

      // Call the callback
      callback();
    }, delay);

    // Store the timeout ID in the ref
    timeoutsRef.current[timeoutKey] = timeoutId;

    return timeoutId;
  }, []);

  // Function to clear all timeouts
  const clearAllTimeouts = useCallback(() => {
    Object.values(timeoutsRef.current).forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    timeoutsRef.current = {};
  }, []);

  // Process text into chunks when it changes
  useEffect(() => {
    // Clear all existing timeouts to prevent race conditions
    clearAllTimeouts();

    // Only skip if the text is identical AND processed very recently (within 1 second)
    // This prevents skipping legitimate repeated messages that should be played
    const now = Date.now();
    const timeSinceLastProcess = now - lastProcessedTimestampRef.current;
    const isDuplicate = text === lastProcessedTextRef.current && timeSinceLastProcess < 1000;

    // Generate a unique message ID for this text
    const messageId = getMessageId(text || '');
    console.log(`Processing message with ID: ${messageId}`);

    // Check if this message is already being processed
    if (currentlyProcessingRef.current === messageId) {
      console.log(`Message ${messageId} is already being processed, skipping duplicate`);
      return;
    }

    // Reset audio ready state for this new message
    if (audioReadyCalledRef.current[messageId] === undefined) {
      audioReadyCalledRef.current[messageId] = false;
    }

    if (isDuplicate) {
      console.log('Skipping duplicate text processing:', text?.substring(0, 30) + '...');
      console.log(`Time since last process: ${timeSinceLastProcess}ms`);

      // Even when skipping, ensure we call onAudioReady to reveal the message
      setManagedTimeout(() => {
        if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
          console.log(`Calling onAudioReady for message ${messageId} despite skipping to ensure UI updates`);
          onAudioReady(messageId);
          audioReadyCalledRef.current[messageId] = true;
        }
      }, 500, `skip-delay-${messageId}`);

      return;
    }

    // Store the current text as the last processed text with timestamp
    lastProcessedTextRef.current = text || '';
    lastProcessedTimestampRef.current = now;

    // Mark this message as currently being processed
    currentlyProcessingRef.current = messageId;

    if (text) {
      console.log('Processing new text for TTS:', text?.substring(0, 30) + '...');

      // Validate the text
      const validation = validateTTSText(text);

      if (!validation.valid) {
        // If text is too long, split it into chunks
        if (validation.error === 'Text is too long for TTS processing') {
          const chunks = splitTextIntoChunks(text);
          setTextChunks(chunks);
          setCurrentChunkIndex(0);
        } else {
          // For other validation errors, report the error
          if (onError) {
            onError(validation.message || validation.error || 'Invalid text for TTS processing');
          }
          setTextChunks([]);

          // Call onAudioReady even on error to ensure UI updates
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
            console.log(`Calling onAudioReady for message ${messageId} after validation error`);
            onAudioReady(messageId);
            audioReadyCalledRef.current[messageId] = true;
          }
        }
      } else {
        // Text is valid and within length limits, process as a single chunk
        setTextChunks([text]);
        setCurrentChunkIndex(0);
      }
    } else {
      // If no text is provided, ensure we still call onAudioReady
      if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
        console.log(`Calling onAudioReady for empty message ${messageId}`);
        onAudioReady(messageId);
        audioReadyCalledRef.current[messageId] = true;
      }
    }

    // Clean up old message states after a delay
    setManagedTimeout(() => {
      // Clean up message IDs older than 5 minutes to prevent memory leaks
      const now = Date.now();
      const fiveMinutesAgo = now - 5 * 60 * 1000;

      Object.keys(audioReadyCalledRef.current).forEach(id => {
        // We don't have timestamps for each ID, so we extract it from the ID if possible
        const timestampMatch = id.match(/msg_(\d+)_/);
        if (timestampMatch && parseInt(timestampMatch[1]) < fiveMinutesAgo) {
          delete audioReadyCalledRef.current[id];
        }
      });
    }, 10 * 60 * 1000, `cleanup-${messageId}`); // Clean up after 10 minutes
  }, [text, onError, getMessageId, setManagedTimeout, clearAllTimeouts]);

  // Handle audio ended event to play the next chunk
  const handleAudioEnded = useCallback(() => {
    console.log('Audio playback ended');

    // Reset playing state when audio ends
    isPlayingRef.current = false;

    // Generate a message ID based on the current text
    const messageId = getMessageId(text || '');

    if (currentChunkIndex < textChunks.length - 1) {
      console.log(`Moving to next chunk: ${currentChunkIndex + 1} of ${textChunks.length}`);
      setCurrentChunkIndex(prevIndex => prevIndex + 1);
    } else {
      console.log('All chunks played, finalizing playback');

      // Add a small delay before finalizing to ensure smooth transitions
      setManagedTimeout(() => {
        console.log(`Finalizing playback for message ${messageId}`);
        // Ensure playing state is false after delay
        isPlayingRef.current = false;
        // Clear the currently processing flag
        currentlyProcessingRef.current = null;
      }, 300, `finalize-${messageId}`);
    }
  }, [currentChunkIndex, textChunks.length, text, getMessageId, setManagedTimeout]);

  // Track if the component is mounted to prevent state updates after unmounting
  const isMountedRef = useRef(true);

  // Set up the mounted ref and cleanup
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;

      // Clean up all timeouts
      clearAllTimeouts();

      // Clean up audio element if it exists
      if (audioRef.current) {
        // Remove event listeners
        if (errorListenerRef.current) {
          audioRef.current.removeEventListener('error', errorListenerRef.current);
          errorListenerRef.current = null;
        }
        if (endedListenerRef.current) {
          audioRef.current.removeEventListener('ended', endedListenerRef.current);
          endedListenerRef.current = null;
        }

        // Clear other event handlers
        audioRef.current.onloadeddata = null;
        audioRef.current.oncanplaythrough = null;
        audioRef.current.onplay = null;

        // Pause and reset
        try {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
        } catch (e) {
          console.log('Error resetting audio element during cleanup:', e);
        }
      }
    };
  }, [clearAllTimeouts]);

  // Track the last audio URL to prevent duplicate requests
  const lastAudioUrlRef = useRef<string>('');

  // Ensure fetch is available in all environments
  useEffect(() => {
    // This is a safety check for environments where fetch might not be available
    if (typeof window !== 'undefined' && !window.fetch) {
      console.warn('Fetch API not available, using XMLHttpRequest as fallback');

      // Simple fetch polyfill using XMLHttpRequest
      window.fetch = (url, options = {}) => {
        return new Promise<Response>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open(options.method || 'GET', url.toString(), true);

          // Set headers
          if (options.headers) {
            const headers = new Headers(options.headers as HeadersInit);
            headers.forEach((value, name) => {
              xhr.setRequestHeader(name, value);
            });
          }

          xhr.onload = () => {
            const responseHeaders = new Headers(xhr.getAllResponseHeaders().split('\r\n').reduce((result: Record<string, string>, current) => {
              const [name, value] = current.split(': ');
              if (name) result[name.trim()] = value ? value.trim() : '';
              return result;
            }, {}));

            const blob = new Blob([xhr.response], { type: responseHeaders.get('content-type') || 'application/octet-stream' });

            // Create a basic Response-like object implementing required properties and methods
            const response: Response = {
              ok: xhr.status >= 200 && xhr.status < 300,
              status: xhr.status,
              statusText: xhr.statusText,
              headers: responseHeaders,
              // Body and body methods
              // Attempt to use a ReadableStream if possible, or null
              body: typeof ReadableStream !== 'undefined' && xhr.response instanceof ArrayBuffer ? new ReadableStream({
                  start(controller) {
                      controller.enqueue(new Uint8Array(xhr.response));
                      controller.close();
                  }
              }) : null,
              bodyUsed: false, // Assuming body is not used immediately
              arrayBuffer: () => Promise.resolve(xhr.response),
              blob: () => Promise.resolve(blob),
              json: () => Promise.resolve(JSON.parse(xhr.responseText)),
              text: () => Promise.resolve(xhr.responseText),
              formData: () => Promise.reject(new Error('formData not implemented')), // Not strictly needed for this use case
              // Other required properties
              redirected: false,
              type: 'default',
              url: url.toString(), // Use the original URL or response URL if available
              // Clone method (simplified - may not support all features like streams)
              clone: (): Response => {
                 // Return a new Response-like object with the same properties
                 const clonedResponse: Response = {
                     ...response,
                     // For simplicity, the cloned body methods resolve with the same data
                     arrayBuffer: () => Promise.resolve(xhr.response),
                     blob: () => Promise.resolve(new Blob([xhr.response], { type: responseHeaders.get('content-type') || 'application/octet-stream' })),
                     json: () => Promise.resolve(JSON.parse(xhr.responseText)),
                     text: () => Promise.resolve(xhr.responseText),
                     formData: () => Promise.reject(new Error('formData not implemented')), // Not strictly needed for this use case
                     // Add the required 'bytes' method
                     bytes: () => Promise.resolve(new Uint8Array(xhr.response as ArrayBuffer)),
                     // A recursive clone for the cloned response
                     clone: (): Response => clonedResponse // Simple self-referential clone
                 };
                 return clonedResponse;
              },
            };
            // Resolve the promise with the created Response object
            resolve(response);
          };

          xhr.onerror = () => {
            reject(new Error('Network request failed'));
          };

          xhr.responseType = 'arraybuffer';
          // Use a more specific type assertion or check
          if (options.body !== undefined && options.body !== null) {
             // Attempt to send if body is a valid type for send
             if (options.body instanceof Blob || options.body instanceof ArrayBuffer || ArrayBuffer.isView(options.body) || typeof options.body === 'string' || options.body instanceof FormData || options.body instanceof URLSearchParams) {
                xhr.send(options.body as XMLHttpRequestBodyInit);
             } else if (options.body instanceof Document) {
                 xhr.send(options.body);
             } else {
                console.error('Unsupported body type for XMLHttpRequest send:', options.body);
                reject(new TypeError('Unsupported body type'));
             }
          } else {
             xhr.send();
          }
        });
      };
    }
  }, []);

  // Process the current chunk
  useEffect(() => {
    // Skip processing if there are no chunks or the index is out of bounds
    if (textChunks.length === 0 || currentChunkIndex >= textChunks.length) {
      // Even if there's no text, ensure we call onAudioReady to update the UI
      // Only call if onAudioReady exists and it hasn't been called for a relevant message ID yet
      const messageId = getMessageId(text || '');
      if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
        console.log(`Calling onAudioReady for empty/out of bounds message ${messageId}`);
        onAudioReady(messageId);
        audioReadyCalledRef.current[messageId] = true;
      }
      return;
    }

    const currentChunk = textChunks[currentChunkIndex];

    if (!currentChunk) {
      // Even if there's no chunk, ensure we call onAudioReady to update the UI
      const messageId = getMessageId(text || '');
      if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
        console.log(`Calling onAudioReady for empty chunk message ${messageId}`);
        onAudioReady(messageId);
        audioReadyCalledRef.current[messageId] = true;
      }
      return;
    }

    // IMMEDIATE GLOBAL AUDIO STOP using Global Audio Manager
    const globalAudioManager = createGlobalAudioManager();
    if (globalAudioManager) {
      globalAudioManager.stopAllAudio();
    }

    // Reset playing state to ensure we can play again
    isPlayingRef.current = false;

    // Set loading state
    setIsLoading(true);

    // Store the current chunk index to compare later
    const currentIndexSnapshot = currentChunkIndex;

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      const newAudio = document.createElement('audio');
      newAudio.controls = true;
      newAudio.style.display = process.env.NODE_ENV === 'development' ? 'block' : 'none';
      newAudio.crossOrigin = 'anonymous';

      // Append to the DOM
      const container = document.getElementById('audio-container');
      if (container) {
        container.appendChild(newAudio);
      } else {
        // Create a container if it doesn't exist
        const newContainer = document.createElement('div');
        newContainer.id = 'audio-container';
        newContainer.style.display = 'none';
        document.body.appendChild(newContainer);
        newContainer.appendChild(newAudio);
      }

      // Set the ref
      audioRef.current = newAudio;
    }

    const setupAudio = async () => {
      try {
        // Skip if component unmounted or chunk index changed
        if (!isMountedRef.current || currentIndexSnapshot !== currentChunkIndex) {
          return;
        }

        // AGGRESSIVE CLEANUP: Stop all existing audio elements before setting up new audio
        console.log('Stopping all existing audio elements before new audio setup');
        const stopAllAudioElements = () => {
          // Stop all audio elements in the audio container
          const container = document.getElementById('audio-container');
          if (container) {
            const audioElements = container.querySelectorAll('audio');
            audioElements.forEach((audio) => {
              try {
                if (!audio.paused) {
                  audio.pause();
                  audio.currentTime = 0;
                  console.log('Stopped existing audio element in container');
                }
                // Clear source to stop any ongoing requests
                audio.removeAttribute('src');
                audio.load();
              } catch (e) {
                console.log('Error stopping audio element in container:', e);
              }
            });
          }

          // Also stop any audio elements that might be elsewhere in the DOM
          const allAudioElements = document.querySelectorAll('audio');
          allAudioElements.forEach((audio) => {
            try {
              if (!audio.paused) {
                audio.pause();
                audio.currentTime = 0;
                console.log('Stopped audio element found in DOM');
              }
            } catch (e) {
              console.log('Error stopping DOM audio element:', e);
            }
          });
        };

        // Stop all existing audio before setting up new audio
        stopAllAudioElements();

        // Reset retry counter for new audio setup
        retryCountRef.current = 0;

        // Determine if we're in an iframe/embed context
        let isEmbedded = false;
        try {
          // This will throw an error if we're in a cross-origin iframe
          isEmbedded = window.self !== window.top;
        } catch (error) {
          // If we get an error, we're definitely in a cross-origin iframe
          console.log('Cross-origin iframe detected, using special audio handling');
          isEmbedded = true;
        }

        // Always check if the audio element is available
        if (!audioRef.current) {
          console.error('Audio element reference is not available, creating a new one');
          // Create a new audio element as a fallback
          const newAudio = document.createElement('audio');
          newAudio.controls = true;
          newAudio.style.display = 'none';
          newAudio.crossOrigin = 'anonymous';
          document.body.appendChild(newAudio);
          audioRef.current = newAudio;
        }

        // Create a URL for the TTS API (natural or standard)
        const endpoint = enableNaturalSpeech ? '/api/natural-tts' : '/api/deepgram-tts';
        const ttsUrl = new URL(endpoint, window.location.origin);
        ttsUrl.searchParams.append('text', currentChunk);
        ttsUrl.searchParams.append('voice', voice);
        ttsUrl.searchParams.append('t', Date.now().toString());

        // Add natural speech processing parameter
        if (enableNaturalSpeech) {
          ttsUrl.searchParams.append('enableNaturalSpeech', 'true');
          ttsUrl.searchParams.append('streamResponse', 'false'); // Use buffered response for SimpleAudioPlayer
        }

        // Add embedding context information
        ttsUrl.searchParams.append('embed', isEmbedded ? 'true' : 'false');

        // For direct embedding contexts, add a special parameter
        const isWidgetOrEmbed = window.location.pathname.includes('/widget/') ||
                               window.location.pathname.includes('/embed/');
        if (isWidgetOrEmbed) {
          ttsUrl.searchParams.append('direct', 'true');

          // Add referrer information if available
          try {
            if (document.referrer) {
              const referrerUrl = new URL(document.referrer);
              ttsUrl.searchParams.append('referrer', referrerUrl.origin);
            }
          } catch (error) {
            console.warn('Error parsing referrer:', error);
          }
        }

        // Add a random parameter to prevent caching
        ttsUrl.searchParams.append('r', Math.random().toString());

        // Generate a unique URL for each request to prevent caching issues
        const urlString = ttsUrl.toString() + '&nocache=' + Date.now();

        // Reset the lastAudioUrlRef to ensure we don't get stuck in a loop
        lastAudioUrlRef.current = '';

        // Use the existing audio element
        const audioElement = audioRef.current;

        // Register this audio element with the global manager
        const globalAudioManager = createGlobalAudioManager();
        if (globalAudioManager) {
          globalAudioManager.currentAudioElement = audioElement;
          console.log('GLOBAL AUDIO MANAGER: Registered new audio element');
        }

        // Clean up previous event listeners
        if (errorListenerRef.current) {
          audioElement.removeEventListener('error', errorListenerRef.current);
        }
        if (endedListenerRef.current) {
          audioElement.removeEventListener('ended', endedListenerRef.current);
        }

        // Create new error listener
        const errorListener = (event: Event) => {
          // Skip if component unmounted
          if (!isMountedRef.current) return;

          console.error('Audio error:', event);
          setIsLoading(false);
          isPlayingRef.current = false;

          // Circuit breaker: prevent infinite loops
          retryCountRef.current++;
          if (retryCountRef.current > MAX_RETRIES) {
            console.log(`Max retries (${MAX_RETRIES}) reached, stopping audio attempts`);
            // Call onAudioReady to ensure UI updates even when audio fails
            const errorMsgId = getMessageId(currentChunk || '');
            if (onAudioReady && !audioReadyCalledRef.current[errorMsgId]) {
              console.log(`Calling onAudioReady for message ${errorMsgId} after max retries`);
              onAudioReady(errorMsgId);
              audioReadyCalledRef.current[errorMsgId] = true;
            }
            return; // Stop the retry loop
          }

          // Try the echo TTS API as fallback
          const echoUrl = new URL('/api/echo-tts', window.location.origin);
          echoUrl.searchParams.append('text', currentChunk);
          echoUrl.searchParams.append('voice', voice);
          echoUrl.searchParams.append('t', Date.now().toString());

          // Add embedding context information
          echoUrl.searchParams.append('embed', isEmbedded ? 'true' : 'false');

          // For direct embedding contexts, add a special parameter
          const isWidgetOrEmbed = window.location.pathname.includes('/widget/') ||
                                 window.location.pathname.includes('/embed/');
          if (isWidgetOrEmbed) {
            echoUrl.searchParams.append('direct', 'true');

            // Add referrer information if available
            try {
              if (document.referrer) {
                const referrerUrl = new URL(document.referrer);
                echoUrl.searchParams.append('referrer', referrerUrl.origin);
              }
            } catch (error) {
              console.warn('Error parsing referrer:', error);
            }
          }

          // Add a random parameter to prevent caching
          echoUrl.searchParams.append('r', Math.random().toString());

          console.log('Trying fallback TTS API:', echoUrl.toString());

          // Check if we're in an iframe embed context
          let isIframeEmbed = false;
          try {
            // This will throw an error if we're in a cross-origin iframe
            isIframeEmbed = window.self !== window.top &&
                           (window.location.pathname.includes('/widget/') ||
                            window.location.pathname.includes('/embed/'));
          } catch (error) {
            // If we get an error, we're definitely in a cross-origin iframe
            console.log('Cross-origin iframe detected, using special audio handling');
            isIframeEmbed = true;
          }

          // Use direct URL approach for CSP compliance - no blob URLs
          console.log('Using direct URL fallback for CSP compliance');
          audioElement.src = echoUrl.toString();
          audioElement.load();

          // Try to play the audio
          if (autoPlay || currentChunkIndex > 0) {
            audioElement.play().catch(fallbackError => {
              // Skip if component unmounted
              if (!isMountedRef.current) return;
              console.error('Fallback audio playback failed:', fallbackError);
              // Ensure we call onAudioReady even on error
              // Generate a message ID for the current chunk
              const fallbackMsgId = getMessageId(currentChunk || '');
              if (onAudioReady && !audioReadyCalledRef.current[fallbackMsgId]) {
                console.log(`Calling onAudioReady for message ${fallbackMsgId} after fallback error`);
                onAudioReady(fallbackMsgId);
                audioReadyCalledRef.current[fallbackMsgId] = true;
              }
            });
          }
        };

        // Store the error listener for cleanup
        errorListenerRef.current = errorListener;
        audioElement.addEventListener('error', errorListener);

        // Add ended listener
        audioElement.addEventListener('ended', handleAudioEnded);
        endedListenerRef.current = handleAudioEnded;

        // Clear all existing event listeners
        audioElement.onloadeddata = null;
        audioElement.oncanplaythrough = null;
        audioElement.onplay = null;

        // Track playback completion
        let playbackCompleted = false;

        // Add play event listener to track when audio starts playing
        audioElement.onplay = () => {
          console.log('Audio playback started');
          isPlayingRef.current = true;
        };

        // Generate a message ID for the current chunk
        const messageId = getMessageId(currentChunk || '');

        // Add loaded data listener
        audioElement.onloadeddata = () => {
          // Skip if component unmounted
          if (!isMountedRef.current) return;

          console.log('Audio data loaded successfully');
          setIsLoading(false);

          // Notify that audio is ready (only if not already called for this message)
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
            console.log(`Calling onAudioReady for message ${messageId} after audio loaded`);
            onAudioReady(messageId);
            audioReadyCalledRef.current[messageId] = true;
          }
        };

        // Add can play through listener for more reliable playback
        audioElement.oncanplaythrough = () => {
          // Skip if component unmounted
          if (!isMountedRef.current) return;

          console.log('Audio can play through, attempting playback...');

          // Generate a message ID for the current chunk
          const messageId = getMessageId(currentChunk || '');

          // Call onAudioReady only here, when the audio is truly ready to play the current chunk
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
             console.log(`Calling onAudioReady for message ${messageId} after canplaythrough`);
             onAudioReady(messageId);
             audioReadyCalledRef.current[messageId] = true;
          }

          // Ensure audio plays when loaded if autoPlay is true
          if (autoPlay || currentChunkIndex > 0) {
            // Set a small timeout to ensure the audio element is ready
            setManagedTimeout(() => {
              if (audioElement && !audioElement.paused) {
                console.log('Audio is already playing, skipping play() call');
                return;
              }

              console.log('Auto-playing audio after it loaded...');

              // Always attempt autoplay - let the browser decide if it's allowed
              console.log('Attempting audio autoplay...');

              // Create a promise to handle the play attempt
              const playPromise = audioElement.play();

              if (playPromise !== undefined) {
                playPromise.then(() => {
                  console.log('Audio autoplay started successfully');
                }).catch(error => {
                  console.log('Audio autoplay was blocked by browser policy:', error.name, error.message);

                  // If autoplay fails, try to enable it after any user interaction
                  if (error.name === 'NotAllowedError') {
                    console.log('Setting up audio to play after next user interaction...');

                    const enableAudioOnInteraction = () => {
                      console.log('User interaction detected, playing queued audio...');
                      audioElement.play().then(() => {
                        console.log('Audio played successfully after user interaction');
                      }).catch(retryError => {
                        console.log('Audio still failed after user interaction:', retryError);
                      });

                      // Remove listeners after successful play attempt
                      document.removeEventListener('click', enableAudioOnInteraction);
                      document.removeEventListener('touchstart', enableAudioOnInteraction);
                      document.removeEventListener('keydown', enableAudioOnInteraction);
                    };

                    // Add temporary listeners for this specific audio
                    document.addEventListener('click', enableAudioOnInteraction, { once: true, passive: true });
                    document.addEventListener('touchstart', enableAudioOnInteraction, { once: true, passive: true });
                    document.addEventListener('keydown', enableAudioOnInteraction, { once: true, passive: true });
                  }
                });
              }

              // Always call onAudioReady regardless of play success/failure
              if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
                console.log(`Calling onAudioReady for message ${messageId} after canplaythrough`);
                onAudioReady(messageId);
                audioReadyCalledRef.current[messageId] = true;
              }
            }, 100, `play-delay-${messageId}`);
          } else {
            // Force autoplay attempt even if condition failed - this is a fallback
            console.log(`Autoplay condition failed (autoPlay: ${autoPlay}, currentChunkIndex: ${currentChunkIndex}), attempting force play for message ${messageId}`);

            setManagedTimeout(() => {
              if (audioElement && !audioElement.paused) {
                console.log('Audio is already playing, skipping force play');
                return;
              }

              console.log('Force playing audio...');
              const playPromise = audioElement.play();

              if (playPromise !== undefined) {
                playPromise.then(() => {
                  console.log('Force autoplay started successfully');
                }).catch(error => {
                  console.log('Force autoplay was blocked:', error.name, error.message);
                });
              }
            }, 50, `force-play-${messageId}`);
          }
        };

        // Clear any previous blob URL
        if (audioElement.src && audioElement.src.startsWith('blob:')) {
          URL.revokeObjectURL(audioElement.src);
        }

        console.log('Setting audio source to:', urlString.substring(0, 100) + '...');

        // Make sure the audio element is paused and reset before setting a new source
        try {
          audioElement.pause();
          audioElement.currentTime = 0;
        } catch (e) {
          console.log('Error resetting audio element:', e);
        }

        // Check if we're in an environment where direct audio loading might fail
        const isIframeEmbed = window.self !== window.top &&
                             (window.location.pathname.includes('/widget/') ||
                              window.location.pathname.includes('/embed/'));

        // Always use direct URL streaming - no blob URLs to avoid CSP issues
        console.log('Using direct URL streaming for CSP compliance');
        audioElement.src = urlString;

        // Load the audio - this triggers the onloadeddata and oncanplaythrough events
        console.log('Loading audio...');
        audioElement.load();

        // If not auto-playing, still call onAudioReady after a timeout
        if (!autoPlay && currentChunkIndex === 0) {
          // Generate a message ID for the current chunk
          const timeoutMsgId = getMessageId(currentChunk || '');

          setManagedTimeout(() => {
            if (onAudioReady && !audioReadyCalledRef.current[timeoutMsgId]) {
              console.log(`Calling onAudioReady for message ${timeoutMsgId} (no autoplay) after timeout`);
              onAudioReady(timeoutMsgId);
              audioReadyCalledRef.current[timeoutMsgId] = true;
            }
          }, 1000, `no-autoplay-${timeoutMsgId}`);
        }
      } catch (error: any) {
        // Skip if component unmounted
        if (!isMountedRef.current) return;

        console.error('Error setting up audio:', error);
        setIsLoading(false);
        isPlayingRef.current = false;

        if (onError) {
          onError(error.message || 'Failed to generate speech');
        }

        // Ensure we call onAudioReady even on error
        // Generate a message ID for the current chunk
        const errorMsgId = getMessageId(currentChunk || '');
        if (onAudioReady && !audioReadyCalledRef.current[errorMsgId]) {
          console.log(`Calling onAudioReady for message ${errorMsgId} after setup error`);
          onAudioReady(errorMsgId);
          audioReadyCalledRef.current[errorMsgId] = true;
        }
      }
    };

    // Call the function to set up the audio
    setupAudio();

    // Cleanup function
    return () => {
      // Reset state flags on cleanup
      isPlayingRef.current = false;

      // Clean up the audio element
      if (audioRef.current) {
        try {
          // Stop playback first
          audioRef.current.pause();

          // Clear all event handlers
          audioRef.current.onloadeddata = null;
          audioRef.current.oncanplaythrough = null;
          audioRef.current.onplay = null;
          audioRef.current.onpause = null;
          audioRef.current.onended = null;
          audioRef.current.onerror = null;

          // Remove event listeners using stored references
          if (errorListenerRef.current) {
            audioRef.current.removeEventListener('error', errorListenerRef.current);
            errorListenerRef.current = null;
          }

          if (endedListenerRef.current) {
            audioRef.current.removeEventListener('ended', endedListenerRef.current);
            endedListenerRef.current = null;
          }

          // Revoke object URL if it exists
          if (audioRef.current.src && audioRef.current.src.startsWith('blob:')) {
            URL.revokeObjectURL(audioRef.current.src);
          }

          // Clear source properly
          audioRef.current.removeAttribute('src');
          audioRef.current.load(); // Force browser to recognize the source change
        } catch (e) {
          console.error('Error cleaning up audio element:', e);
        }
      }

      // Call onAudioReady if it hasn't been called yet to ensure UI updates
      if (isMountedRef.current && onAudioReady && text) {
        // Generate a message ID for the current text
        const cleanupMsgId = getMessageId(text);
        if (!audioReadyCalledRef.current[cleanupMsgId]) {
          console.log(`Calling onAudioReady for message ${cleanupMsgId} during cleanup to ensure UI updates`);
          onAudioReady(cleanupMsgId);
          audioReadyCalledRef.current[cleanupMsgId] = true;
        }
      }

      // Clear all timeouts
      clearAllTimeouts();
    };
  }, [textChunks, currentChunkIndex, voice, autoPlay, onError, handleAudioEnded, text, getMessageId, clearAllTimeouts]);

  // Function to manually play audio (for debugging)
  const playAudio = () => {
    if (audioRef.current) {
      console.log('Manually playing audio...');
      audioRef.current.play().catch(error => {
        console.error('Error manually playing audio:', error);
      });
    }
  };

  // Function to create a test audio element with a simple tone
  const playTestTone = () => {
    try {
      // Create a new audio context
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      const audioContext = new AudioContext();

      // Create an oscillator (sine wave)
      const oscillator = audioContext.createOscillator();
      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // 440 Hz = A4

      // Create a gain node to control volume
      const gainNode = audioContext.createGain();
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime); // 10% volume

      // Connect oscillator to gain node and gain node to output
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Start and stop the oscillator after 0.5 seconds
      oscillator.start();
      setTimeout(() => {
        oscillator.stop();
        console.log('Test tone played successfully');
      }, 500);

      console.log('Playing test tone...');
    } catch (error) {
      console.error('Error playing test tone:', error);
    }
  };

  // Create a container div for the audio element if it doesn't exist
  useEffect(() => {
    if (typeof document !== 'undefined' && !document.getElementById('audio-container')) {
      const container = document.createElement('div');
      container.id = 'audio-container';
      container.style.display = 'none';
      document.body.appendChild(container);
    }
  }, []);



  return (
    <div id="simple-audio-player">
      {/* We don't render the audio element here anymore, it's created dynamically */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          <button
            onClick={playAudio}
            style={{
              padding: '5px 10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Force Play Audio
          </button>
          <button
            onClick={playTestTone}
            style={{
              padding: '5px 10px',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Test Audio System
          </button>
        </div>
      )}
    </div>
  );
};

export default SimpleAudioPlayer;
