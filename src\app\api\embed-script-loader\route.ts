import { NextResponse } from 'next/server';

// This is a special API route that serves the embed script loader
// It's designed to be accessible from any origin
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET() {
  try {
    // Create the embed script loader
    const scriptContent = `
      // BotFusion Chat Widget Loader
      (function() {
        // Store the init parameters
        var initParams = null;

        // Define the init function
        window.bf = function(action, params) {
          if (action === 'init') {
            initParams = params;
            initChatWidget();
          }
        };

        // Initialize the chat widget
        function initChatWidget() {
          if (!initParams || !initParams.chatId) {
            console.error('BotFusion Chat: Missing required parameters');
            return;
          }

          // Create the chat button
          const button = document.createElement('div');
          button.setAttribute('role', 'button');
          button.setAttribute('aria-label', 'Open chat');
          button.setAttribute('tabindex', '0');

          // Apply styles to the button
          button.style.position = 'fixed';
          button.style.bottom = '20px';
          button.style.right = '20px';
          button.style.width = '60px';
          button.style.height = '60px';
          button.style.borderRadius = '50%';
          button.style.backgroundColor = initParams.primaryColor || '#3b82f6';
          button.style.color = 'white';
          button.style.border = 'none';
          button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
          button.style.cursor = 'pointer';
          button.style.zIndex = '9999';
          button.style.display = 'flex';
          button.style.alignItems = 'center';
          button.style.justifyContent = 'center';
          button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

          // Add the chat icon
          button.innerHTML = \`
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          \`;

          // Create the chat iframe
          const iframe = document.createElement('iframe');

          // Build the URL with parameters
          const origin = window.location.origin.includes('localhost') ?
            '${process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'}' :
            '${process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'}';

          let url = origin + "/embed/" + initParams.chatId + "?";
          url += "primaryColor=" + encodeURIComponent(initParams.primaryColor || '#3b82f6');
          url += "&userBubbleColor=" + encodeURIComponent(initParams.userBubbleColor || '#ffffff');
          url += "&botBubbleColor=" + encodeURIComponent(initParams.botBubbleColor || '#3b82f6');
          url += "&userTextColor=" + encodeURIComponent(initParams.userTextColor || '#000000');
          url += "&botTextColor=" + encodeURIComponent(initParams.botTextColor || '#ffffff');
          url += "&logoUrl=" + encodeURIComponent(initParams.logoUrl || '');
          url += "&darkMode=" + (initParams.darkMode ? 'true' : 'false');
          url += "&t=" + new Date().getTime(); // Cache busting

          // Set iframe properties
          iframe.src = url;
          iframe.style.position = 'fixed';
          iframe.style.bottom = '20px';
          iframe.style.right = '20px';
          iframe.style.width = '400px';
          iframe.style.height = '600px';
          iframe.style.border = 'none';
          iframe.style.borderRadius = '12px';
          iframe.style.boxShadow = 'none';
          iframe.style.zIndex = '9999';
          iframe.style.display = 'none';
          iframe.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
          iframe.style.opacity = '0';
          iframe.setAttribute('allow', 'microphone');
          iframe.setAttribute('title', 'Chat Widget');

          // Add hover effect to button
          button.addEventListener('mouseover', function() {
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
          });

          button.addEventListener('mouseout', function() {
            button.style.transform = 'scale(1)';
            button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
          });

          // Toggle chat window when button is clicked
          button.addEventListener('click', function() {
            iframe.style.display = 'block';
            button.style.display = 'none';

            // Add animation
            iframe.style.transform = 'translateY(20px)';
            iframe.style.opacity = '0';

            setTimeout(function() {
              iframe.style.transform = 'translateY(0)';
              iframe.style.opacity = '1';
            }, 10);
          });

          // Add message listener to handle close button in iframe
          window.addEventListener('message', function(event) {
            if (event.data === 'botfusion-chat-close') {
              // Add closing animation
              iframe.style.transform = 'translateY(20px)';
              iframe.style.opacity = '0';

              setTimeout(function() {
                iframe.style.display = 'none';
                button.style.display = 'flex';
              }, 300);
            }
          });

          // Append elements to the document
          document.body.appendChild(iframe);
          document.body.appendChild(button);

          console.log('BotFusion Chat: Widget initialized successfully');
        }
      })();
    `;

    // Return the script with proper CORS headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    });
  } catch (error) {
    console.error('Error serving embed script loader:', error);
    return new NextResponse('console.error("Error loading BotFusion Chat Widget");', {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
      },
    });
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
    },
  });
}
