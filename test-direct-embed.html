<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Embed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
        .debug-info pre {
            max-height: 200px;
            overflow-y: auto;
        }

        /* Chat widget styles */
        #chat-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        #chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        #chat-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 600px;
            border: none;
            border-radius: 12px;
            /* box-shadow removed */
            z-index: 9999;
            display: none;
            background-color: white;
            overflow: hidden;
        }

        #chat-header {
            background-color: #3b82f6;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        #chat-title {
            font-weight: bold;
            font-size: 16px;
        }

        #chat-close {
            cursor: pointer;
            font-size: 20px;
        }

        #chat-messages {
            padding: 15px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 18px;
        }

        .user-message {
            background-color: #3b82f6;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .bot-message {
            background-color: #f1f5f9;
            color: #333;
            border-bottom-left-radius: 5px;
        }

        #chat-input-container {
            padding: 15px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            position: absolute;
            bottom: 0;
            width: calc(100% - 30px);
            background-color: white;
        }

        #chat-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #e5e7eb;
            border-radius: 20px;
            outline: none;
        }

        #chat-send {
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 15px;
            margin-left: 10px;
            cursor: pointer;
        }

        @media (max-width: 480px) {
            #chat-container {
                width: 100%;
                height: 100%;
                bottom: 0;
                right: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <h1>Test Direct Embed</h1>

    <div class="important">
        <strong>Important:</strong> This page is for testing a direct embed approach without using iframes. Please open the browser console (F12) to see any errors.
    </div>

    <div class="content">
        <h2>Direct Embed Approach</h2>
        <p>This test uses a direct embed approach without iframes:</p>
        <pre>&lt;div id="chat-button"&gt;
    &lt;svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
        &lt;path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"&gt;&lt;/path&gt;
    &lt;/svg&gt;
&lt;/div&gt;

&lt;div id="chat-container"&gt;
    &lt;div id="chat-header"&gt;
        &lt;div id="chat-title"&gt;BotFusion Chat&lt;/div&gt;
        &lt;div id="chat-close"&gt;×&lt;/div&gt;
    &lt;/div&gt;
    &lt;div id="chat-messages"&gt;&lt;/div&gt;
    &lt;div id="chat-input-container"&gt;
        &lt;input type="text" id="chat-input" placeholder="Type your message..."&gt;
        &lt;button id="chat-send"&gt;Send&lt;/button&gt;
    &lt;/div&gt;
&lt;/div&gt;</pre>
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Direct Embed -->
    <div id="chat-button">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
    </div>

    <div id="chat-container">
        <div id="chat-header">
            <div id="chat-title">BotFusion Chat</div>
            <div id="chat-close">×</div>
        </div>
        <div id="chat-messages"></div>
        <div id="chat-input-container">
            <input type="text" id="chat-input" placeholder="Type your message...">
            <button id="chat-send">Send</button>
        </div>
    </div>

    <script>
        // Get the elements
        const button = document.getElementById('chat-button');
        const container = document.getElementById('chat-container');
        const closeButton = document.getElementById('chat-close');
        const input = document.getElementById('chat-input');
        const sendButton = document.getElementById('chat-send');
        const messages = document.getElementById('chat-messages');

        // Add click event to button
        button.onclick = function() {
            container.style.display = 'block';
            this.style.display = 'none';

            // Add welcome message if no messages yet
            if (messages.children.length === 0) {
                addMessage('Hello! How can I help you today?', 'bot');
            }
        };

        // Add click event to close button
        closeButton.onclick = function() {
            container.style.display = 'none';
            button.style.display = 'flex';
        };

        // Add click event to send button
        sendButton.onclick = sendMessage;

        // Add keypress event to input
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Function to send a message
        function sendMessage() {
            const text = input.value.trim();
            if (text) {
                addMessage(text, 'user');
                input.value = '';

                // Simulate bot response after a delay
                setTimeout(() => {
                    addMessage('This is a simulated response. In a real implementation, this would connect to your backend API.', 'bot');
                }, 1000);
            }
        }

        // Function to add a message to the chat
        function addMessage(text, type) {
            const message = document.createElement('div');
            message.className = `message ${type}-message`;
            message.textContent = text;
            messages.appendChild(message);
            messages.scrollTop = messages.scrollHeight;
        }
    </script>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        (function() {
            var oldConsoleLog = console.log;
            var oldConsoleError = console.error;
            var oldConsoleWarn = console.warn;
            var consoleOutput = document.getElementById('console-output');

            function appendToOutput(type, args) {
                var message = Array.from(args).map(arg => {
                    if (typeof arg === 'object') {
                        try {
                            return JSON.stringify(arg);
                        } catch (e) {
                            return String(arg);
                        }
                    }
                    return String(arg);
                }).join(' ');

                var pre = document.createElement('pre');
                pre.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
                pre.textContent = type.toUpperCase() + ': ' + message;
                consoleOutput.appendChild(pre);
            }

            console.log = function() {
                oldConsoleLog.apply(console, arguments);
                appendToOutput('log', arguments);
            };

            console.error = function() {
                oldConsoleError.apply(console, arguments);
                appendToOutput('error', arguments);
            };

            console.warn = function() {
                oldConsoleWarn.apply(console, arguments);
                appendToOutput('warn', arguments);
            };

            // Log any errors
            window.addEventListener('error', function(event) {
                appendToOutput('error', [event.message + ' at ' + event.filename + ':' + event.lineno]);
            });
        })();
    </script>
</body>
</html>
