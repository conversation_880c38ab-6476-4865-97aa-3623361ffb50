<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Final Solution</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0070f3;
            margin-bottom: 1rem;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
            background: white;
        }
        .code {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
            white-space: pre-wrap;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #0070f3;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <h1>BotFusion Final Solution</h1>
    
    <div class="container">
        <div class="card">
            <h2>Direct Script Embed</h2>
            <p>This is the final solution that works with any Content Security Policy.</p>
            
            <div class="code">
&lt;script&gt;
// BotFusion Chat Widget
(function() {
  // Create the button element
  const button = document.createElement('div');
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#3b82f6';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '9999';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
  
  // Add the chat icon
  button.innerHTML = '&lt;svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;"&gt;&lt;path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"&gt;&lt;/path&gt;&lt;/svg&gt;';
  
  // Create the iframe element
  const iframe = document.createElement('iframe');
  iframe.src = 'https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = 'none !important';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'none';
  iframe.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
  iframe.style.opacity = '0';
  iframe.setAttribute('allow', 'microphone');
  iframe.setAttribute('title', 'Chat Widget');
  
  // Add hover effect to button
  button.addEventListener('mouseover', function() {
    button.style.transform = 'scale(1.05)';
    button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  });
  
  button.addEventListener('mouseout', function() {
    button.style.transform = 'scale(1)';
    button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  });
  
  // Toggle chat window when button is clicked
  button.addEventListener('click', function() {
    iframe.style.display = 'block';
    button.style.display = 'none';
    
    // Add animation
    iframe.style.transform = 'translateY(20px)';
    iframe.style.opacity = '0';
    
    setTimeout(function() {
      iframe.style.transform = 'translateY(0)';
      iframe.style.opacity = '1';
    }, 10);
  });
  
  // Add message listener to handle close button in iframe
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframe
    if (event.data === 'botfusion-chat-close') {
      // Add closing animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';
      
      setTimeout(function() {
        iframe.style.display = 'none';
        button.style.display = 'flex';
      }, 300);
    }
  });
  
  // Append elements to the document
  document.body.appendChild(iframe);
  document.body.appendChild(button);
  
  // Expose the BotFusionChat object to the window
  window.BotFusionChat = {
    open: function() {
      iframe.style.display = 'block';
      button.style.display = 'none';
      
      // Add animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';
      
      setTimeout(function() {
        iframe.style.transform = 'translateY(0)';
        iframe.style.opacity = '1';
      }, 10);
    },
    close: function() {
      // Add closing animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';
      
      setTimeout(function() {
        iframe.style.display = 'none';
        button.style.display = 'flex';
      }, 300);
    }
  };
})();
&lt;/script&gt;
            </div>
            
            <div class="note">
                <strong>Note:</strong> The chat button should appear in the bottom-right corner of this page.
            </div>
        </div>
    </div>

    <!-- Direct Script Embed -->
    <script>
    // BotFusion Chat Widget
    (function() {
      // Create the button element
      const button = document.createElement('div');
      button.style.position = 'fixed';
      button.style.bottom = '20px';
      button.style.right = '20px';
      button.style.width = '60px';
      button.style.height = '60px';
      button.style.borderRadius = '50%';
      button.style.backgroundColor = '#3b82f6';
      button.style.color = 'white';
      button.style.border = 'none';
      button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      button.style.cursor = 'pointer';
      button.style.zIndex = '9999';
      button.style.display = 'flex';
      button.style.alignItems = 'center';
      button.style.justifyContent = 'center';
      button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
      
      // Add the chat icon
      button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';
      
      // Create the iframe element
      const iframe = document.createElement('iframe');
      iframe.src = 'https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d';
      iframe.style.position = 'fixed';
      iframe.style.bottom = '20px';
      iframe.style.right = '20px';
      iframe.style.width = '400px';
      iframe.style.height = '600px';
      iframe.style.border = 'none';
      iframe.style.borderRadius = '12px';
      iframe.style.boxShadow = 'none !important';
      iframe.style.zIndex = '9999';
      iframe.style.display = 'none';
      iframe.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
      iframe.style.opacity = '0';
      iframe.setAttribute('allow', 'microphone');
      iframe.setAttribute('title', 'Chat Widget');
      
      // Add hover effect to button
      button.addEventListener('mouseover', function() {
        button.style.transform = 'scale(1.05)';
        button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
      });
      
      button.addEventListener('mouseout', function() {
        button.style.transform = 'scale(1)';
        button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      });
      
      // Toggle chat window when button is clicked
      button.addEventListener('click', function() {
        iframe.style.display = 'block';
        button.style.display = 'none';
        
        // Add animation
        iframe.style.transform = 'translateY(20px)';
        iframe.style.opacity = '0';
        
        setTimeout(function() {
          iframe.style.transform = 'translateY(0)';
          iframe.style.opacity = '1';
        }, 10);
      });
      
      // Add message listener to handle close button in iframe
      window.addEventListener('message', function(event) {
        // Check if the message is from our iframe
        if (event.data === 'botfusion-chat-close') {
          // Add closing animation
          iframe.style.transform = 'translateY(20px)';
          iframe.style.opacity = '0';
          
          setTimeout(function() {
            iframe.style.display = 'none';
            button.style.display = 'flex';
          }, 300);
        }
      });
      
      // Append elements to the document
      document.body.appendChild(iframe);
      document.body.appendChild(button);
      
      // Expose the BotFusionChat object to the window
      window.BotFusionChat = {
        open: function() {
          iframe.style.display = 'block';
          button.style.display = 'none';
          
          // Add animation
          iframe.style.transform = 'translateY(20px)';
          iframe.style.opacity = '0';
          
          setTimeout(function() {
            iframe.style.transform = 'translateY(0)';
            iframe.style.opacity = '1';
          }, 10);
        },
        close: function() {
          // Add closing animation
          iframe.style.transform = 'translateY(20px)';
          iframe.style.opacity = '0';
          
          setTimeout(function() {
            iframe.style.display = 'none';
            button.style.display = 'flex';
          }, 300);
        }
      };
    })();
    </script>
</body>
</html>
