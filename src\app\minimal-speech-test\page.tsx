'use client'

import React, { useRef, useState } from 'react';

export default function MinimalSpeechTest() {
  const [transcript, setTranscript] = useState('');
  const [listening, setListening] = useState(false);
  const recognitionRef = useRef<any>(null);

  const startRecognition = () => {
    if (typeof window === 'undefined') return;
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('SpeechRecognition not supported in this browser.');
      return;
    }
    if (recognitionRef.current) {
      recognitionRef.current.abort();
      recognitionRef.current = null;
    }
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US';
    recognition.continuous = false;
    recognition.interimResults = true;
    recognition.onstart = () => console.log('onstart');
    recognition.onaudiostart = () => console.log('onaudiostart');
    recognition.onspeechstart = () => console.log('onspeechstart');
    recognition.onsoundstart = () => console.log('onsoundstart');
    recognition.onsoundend = () => console.log('onsoundend');
    recognition.onaudioend = () => console.log('onaudioend');
    recognition.onend = () => { console.log('onend'); setListening(false); };
    recognition.onresult = (event: any) => {
      console.log('onresult', event);
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        finalTranscript += event.results[i][0].transcript;
      }
      setTranscript(finalTranscript);
    };
    recognition.onerror = (event: any) => {
      console.error('onerror', event);
      setListening(false);
    };
    recognition.onnomatch = (event: any) => console.log('onnomatch', event);
    recognitionRef.current = recognition;
    setListening(true);
    recognition.start();
  };

  const stopRecognition = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setListening(false);
    }
  };

  return (
    <div style={{ maxWidth: 600, margin: '2rem auto', padding: 24, border: '1px solid #ccc', borderRadius: 8 }}>
      <h1>Minimal Speech Recognition Test</h1>
      <button onClick={listening ? stopRecognition : startRecognition} style={{ padding: 12, marginBottom: 16 }}>
        {listening ? 'Stop Listening' : 'Start Listening'}
      </button>
      <div style={{ marginTop: 16 }}>
        <strong>Transcript:</strong>
        <div style={{ minHeight: 40, border: '1px solid #eee', padding: 8, marginTop: 8 }}>{transcript}</div>
      </div>
    </div>
  );
} 