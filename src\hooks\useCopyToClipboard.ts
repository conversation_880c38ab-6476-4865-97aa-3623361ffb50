import { useState } from 'react';
import { toast } from 'sonner';

export function useCopyToClipboard(resetDelay = 2000) {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = async (text: string, successMessage?: string) => {
    if (!navigator.clipboard) {
      toast.error('Clipboard API not available in your browser');
      return false;
    }

    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);
      
      if (successMessage) {
        toast.success(successMessage);
      }
      
      // Reset the copied state after a delay
      setTimeout(() => {
        setIsCopied(false);
      }, resetDelay);
      
      return true;
    } catch (error) {
      console.error('Failed to copy text: ', error);
      toast.error('Failed to copy to clipboard');
      return false;
    }
  };

  return { isCopied, copyToClipboard };
}
