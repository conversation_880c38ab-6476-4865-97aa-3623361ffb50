// <PERSON>ript to test the TTS API endpoints directly
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Deployed URL
const DEPLOYED_URL = 'https://roo-bot-fusion-kgfs-bg671eiav-tellivisions-projects.vercel.app';

// Test text
const TEST_TEXT = 'This is a test of the Deepgram Text-to-Speech API. If you can hear this message, the API is working correctly.';

// Test all TTS endpoints
async function testTTSEndpoints() {
  console.log(`Testing TTS endpoints on ${DEPLOYED_URL}...`);

  const endpoints = [
    '/api/deepgram-tts',
    '/api/fallback-tts',
    '/api/echo-tts'
  ];

  for (const endpoint of endpoints) {
    console.log(`\nTesting endpoint: ${endpoint}`);

    try {
      // Create the URL with query parameters
      const ttsUrl = new URL(endpoint, DEPLOYED_URL);
      ttsUrl.searchParams.append('text', TEST_TEXT);
      ttsUrl.searchParams.append('voice', 'nova');
      ttsUrl.searchParams.append('t', Date.now().toString());

      console.log(`Calling TTS API: ${ttsUrl.toString()}`);

      // Make the request
      const response = await fetch(ttsUrl.toString(), {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      });

      console.log(`Response status: ${response.status} ${response.statusText}`);
      console.log(`Response headers:`, response.headers.raw());

      if (!response.ok) {
        // Try to get error details from the response
        const contentType = response.headers.get('content-type') || '';

        if (contentType.includes('application/json')) {
          const errorData = await response.json();
          console.error(`Error: ${JSON.stringify(errorData, null, 2)}`);
        } else {
          const errorText = await response.text();

          if (errorText.includes('<!doctype') || errorText.includes('<html')) {
            console.error('Received HTML response instead of audio. This might indicate authentication is required.');
            // Save the HTML response for inspection
            const htmlFilePath = path.join(__dirname, `${endpoint.replace(/\//g, '-')}-error.html`);
            fs.writeFileSync(htmlFilePath, errorText);
            console.log(`HTML response saved to: ${htmlFilePath}`);
          } else {
            console.error(`Error text: ${errorText}`);
          }
        }

        console.error(`Failed to get audio from ${endpoint}`);
        continue;
      }

      // Get audio data as buffer
      const audioBuffer = await response.buffer();

      if (audioBuffer.length === 0) {
        console.error(`Received empty audio data from ${endpoint}`);
        continue;
      }

      console.log(`Received audio data: ${audioBuffer.length} bytes`);

      // Save the audio file
      const outputPath = path.join(__dirname, `${endpoint.replace(/\//g, '-')}.mp3`);
      fs.writeFileSync(outputPath, audioBuffer);
      console.log(`Audio file saved to: ${outputPath}`);

      console.log(`Successfully tested ${endpoint}`);
    } catch (error) {
      console.error(`Error testing ${endpoint}:`, error.message);
    }
  }

  console.log('\nTTS endpoint testing completed');
}

// Run the test
testTTSEndpoints().catch(console.error);
