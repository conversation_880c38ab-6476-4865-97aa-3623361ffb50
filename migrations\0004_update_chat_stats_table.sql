-- Add chat_interface_id column to chat_stats table
ALTER TABLE IF EXISTS chat_stats 
ADD COLUMN IF NOT EXISTS chat_interface_id UUID REFERENCES chat_interfaces(id) ON DELETE CASCADE;

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_chat_stats_chat_interface_id ON chat_stats(chat_interface_id);

-- Add last_message_at column to track when the last message was received
ALTER TABLE IF EXISTS chat_stats
ADD COLUMN IF NOT EXISTS last_message_at TIMESTAMP WITH TIME ZONE;

-- Update the chat_stats table with data from chat_messages
-- This will populate the chat_stats table with the correct data
INSERT INTO chat_stats (chat_interface_id, session_id, message_count, last_message_at, created_at, updated_at)
SELECT 
  chat_interface_id,
  session_id,
  COUNT(*) as message_count,
  MAX(created_at) as last_message_at,
  MIN(created_at) as created_at,
  NOW() as updated_at
FROM 
  chat_messages
GROUP BY 
  chat_interface_id, session_id
ON CONFLICT (id) DO UPDATE
SET 
  message_count = EXCLUDED.message_count,
  last_message_at = EXCLUDED.last_message_at,
  updated_at = NOW();
