import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId')
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
    }

    // Get the origin for the API
    const origin = process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'

    // Get the chat interface details from the database
    const supabase = createServiceClient()
    const { data, error } = await supabase
      .from('chat_interfaces')
      .select('*')
      .eq('id', chatId)
      .single()

    if (error) {
      console.error('Error fetching chat interface:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!data) {
      return NextResponse.json({ error: 'Chat interface not found' }, { status: 404 })
    }

    // Generate the mobile-optimized embed code (NEW - FIXES MOBILE ISSUES)
    const mobileOptimizedEmbedCode = `<!-- BotFusion Mobile-Optimized Chat Widget -->
<!-- FIXES: Mobile scrolling, touch events, button blocking, navigation issues -->
<!-- UNIVERSAL COMPATIBILITY: WordPress, Durable.co, Shopify, custom sites -->
<script nonce="REPLACE_WITH_YOUR_NONCE" type="text/javascript">
// Set configuration before loading the widget
window.BOTFUSION_CHAT_ID = '${chatId}';
window.BOTFUSION_BASE_URL = '${origin}';
window.BOTFUSION_PRIMARY_COLOR = '${data.primary_color || '#3b82f6'}';
window.BOTFUSION_DEBUG = false; // Set to true for debugging

// Load the mobile-optimized widget
(function() {
    var script = document.createElement('script');
    script.src = '${origin}/mobile-optimized-embed.js?v=2.0&bypass=${bypassSecret}';
    script.async = true;
    script.onload = function() {
        console.log('BotFusion Mobile Widget loaded successfully');
    };
    script.onerror = function() {
        console.error('Failed to load BotFusion Mobile Widget');
    };
    document.head.appendChild(script);
})();
</script>

<!-- MOBILE OPTIMIZATIONS INCLUDED:
✅ Prevents parent page scroll blocking
✅ Fixes touch event conflicts
✅ Resolves button interaction issues
✅ Maintains navigation functionality
✅ Universal CMS compatibility
✅ 85vh modal instead of fullscreen
✅ Touch-through zones for parent interaction
✅ Proper event delegation

PLATFORM COMPATIBILITY:
✅ WordPress (all themes)
✅ Durable.co
✅ Shopify
✅ Squarespace
✅ Wix
✅ Elementor
✅ Beaver Builder
✅ Divi
✅ Custom HTML sites
✅ All mobile devices (89% of users)

INSTALLATION INSTRUCTIONS:
1. Copy the entire script above
2. Paste it before the closing </body> tag
3. Replace REPLACE_WITH_YOUR_NONCE with your actual nonce (if using CSP)
4. Test on mobile devices to verify scrolling works
5. No additional configuration needed!
-->`;

    // Generate the standard embed code (LEGACY - MOBILE ISSUES)
    const standardEmbedCode = `<!-- BotFusion Chat Widget - LEGACY VERSION -->
<!-- ⚠️  WARNING: This version has mobile compatibility issues -->
<!-- ⚠️  Use Mobile-Optimized version above for better mobile experience -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" type="text/javascript">
(function(w, d, s, o, f, js, fjs) {
    w['BotFusion-Widget'] = o;
    w[o] = w[o] || function() {
        (w[o].q = w[o].q || []).push(arguments);
    };
    js = d.createElement(s);
    fjs = d.getElementsByTagName(s)[0];
    js.id = o;
    js.src = f;
    js.async = 1;
    fjs.parentNode.insertBefore(js, fjs);
}(window, document, 'script', 'bf', '${origin}/api/embed-script'));

bf('init', {
    chatId: "${chatId}",
    position: 'right',
    primaryColor: "${data.primary_color || '#3b82f6'}",
    userBubbleColor: "${data.user_bubble_color || '#ffffff'}",
    botBubbleColor: "${data.bot_bubble_color || '#3b82f6'}",
    userTextColor: "${data.user_text_color || '#000000'}",
    botTextColor: "${data.bot_text_color || '#ffffff'}",
    logoUrl: "${data.logo_url || ''}",
    darkMode: ${data.dark_mode || false},
    greeting: "${data.welcome_message || 'Hello! How can I help you today?'}"
});
</script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' ${origin};
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  connect-src 'self' ${origin};
  frame-src 'self' ${origin};
  frame-ancestors 'self' ${origin};
">
-->`;

    // Generate the CSP-friendly embed code
    const cspEmbedCode = `<!-- BotFusion Chat Widget - CSP-Friendly Version -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" src="${origin}/api/csp-embed-script?chatId=${chatId}&x-vercel-protection-bypass=${bypassSecret}"></script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' ${origin};
  frame-src 'self' ${origin};
  frame-ancestors 'self' ${origin};
">
-->`;

    // Generate the HTML-only embed code for sites with strict CSP
    const htmlOnlyEmbedCode = `<!-- BotFusion Chat Widget - Direct Embed (CSP-Friendly) -->
<!-- This version uses the main embed interface with built-in toggle functionality -->
<iframe
  src="${origin}/embed/${chatId}?x-vercel-protection-bypass=${bypassSecret}"
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; z-index: 9999; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
  allow="microphone; autoplay"
  frameborder="0"
  title="Chat Widget"
  loading="lazy"
></iframe>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  frame-src ${origin};
">
-->`;

    // Generate the pure HTML embed code for sites with the strictest CSP
    const pureHtmlEmbedCode = `<!-- BotFusion Chat Widget - Direct Embed (Strictest CSP) -->
<!-- This version uses the main embed interface with built-in toggle functionality -->
<iframe
  src="${origin}/embed/${chatId}?x-vercel-protection-bypass=${bypassSecret}"
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; z-index: 9999; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
  allow="microphone; autoplay"
  frameborder="0"
  title="Chat Widget"
  loading="lazy"
></iframe>

<!-- This version works with ANY CSP policy and has built-in toggle functionality -->`;

    // Generate the simple embed code (most compatible)
    const simpleEmbedCode = `<!-- BotFusion Chat Widget - Simple Version (works with strict CSP) -->
<!-- Option 1: Direct DOM Embed (No iframes, most compatible) -->
<script>
(function() {
  // Create the CSS styles
  var styles = document.createElement('style');
  styles.textContent = \`
    #botfusion-chat-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    #botfusion-chat-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    #botfusion-chat-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 400px;
      height: 600px;
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      display: none;
      background-color: white;
      overflow: hidden;
    }

    #botfusion-chat-header {
      background-color: #3b82f6;
      color: white;
      padding: 10px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    #botfusion-chat-title {
      font-weight: bold;
      font-size: 16px;
    }

    #botfusion-chat-close {
      cursor: pointer;
      font-size: 20px;
    }

    #botfusion-chat-iframe {
      width: 100%;
      height: calc(100% - 40px);
      border: none;
    }

    @media (max-width: 480px) {
      #botfusion-chat-container {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
      }
    }
  \`;
  document.head.appendChild(styles);

  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Create the chat container
  var container = document.createElement('div');
  container.id = 'botfusion-chat-container';
  container.innerHTML = \`
    <div id="botfusion-chat-header">
      <div id="botfusion-chat-title">BotFusion Chat</div>
      <div id="botfusion-chat-close">×</div>
    </div>
    <iframe id="botfusion-chat-iframe" src="${origin}/embed/${chatId}" allow="microphone"></iframe>
  \`;

  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(container);

  // Get the elements
  var closeButton = document.getElementById('botfusion-chat-close');

  // Add click event to button
  button.onclick = function() {
    container.style.display = 'block';
    this.style.display = 'none';
  };

  // Add click event to close button
  closeButton.onclick = function() {
    container.style.display = 'none';
    button.style.display = 'flex';
  };

  // Add message listener for close events
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      container.style.display = 'none';
      button.style.display = 'flex';
    }
  });
})();
</script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  frame-src ${origin};
  style-src 'unsafe-inline';
  script-src 'unsafe-inline';
">
-->`;

    // Get the bypass secret for iframe embedding
    const bypassSecret = process.env.VERCEL_AUTOMATION_BYPASS_SECRET || 'Q3W0KiXNSiZt6HCFH0e1zYfT2MwpCR2F';

    // Generate the direct iframe embed code
    const directIframeEmbedCode = `<!-- BotFusion Direct Iframe Embed -->
<!-- This version shows the chat interface directly without a button -->
<iframe
  src="${origin}/embed/${chatId}?x-vercel-protection-bypass=${bypassSecret}"
  style="width: 400px; height: 600px; border: none; border-radius: 12px; box-shadow: none !important;"
  allow="microphone; autoplay"
  title="BotFusion Chat"
></iframe>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  frame-src ${origin};
">
-->`;

    // Return all embed codes
    return NextResponse.json({
      mobileOptimizedEmbedCode, // NEW - Mobile-optimized version (recommended)
      standardEmbedCode,        // LEGACY - Has mobile issues
      cspEmbedCode,
      htmlOnlyEmbedCode,
      pureHtmlEmbedCode,
      simpleEmbedCode,
      directIframeEmbedCode
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      }
    })
  } catch (error) {
    console.error('Error generating embed code:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        }
      }
    )
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
      'Access-Control-Max-Age': '86400',
    }
  })
}
