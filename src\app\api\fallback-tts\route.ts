import { NextRequest, NextResponse } from 'next/server';

// Configure as an edge function
export const runtime = 'edge';

// Maximum text length for the fallback TTS API
const MAX_TEXT_LENGTH = 1000;

// List of free TTS APIs to try
const FREE_TTS_APIS = [
  // Skip VoiceRSS as it requires a valid API key

  // Skip Google Translate TTS as it has CORS restrictions

  // Use our reliable echo-tts endpoint as the primary fallback
  {
    name: 'Local Echo',
    url: '/api/echo-tts',
    params: (text: string, voice: string) => ({
      text,
      voice,
      t: Date.now().toString() // Add timestamp to prevent caching
    })
  }
];

export async function GET(request: NextRequest) {
  console.log('Fallback TTS API: Request received');

  try {
    // Get the text and voice from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const text = searchParams.get('text');
    const voice = searchParams.get('voice') || 'nova';

    // Log request details (truncate text for privacy)
    const truncatedText = text && text.length > 50 ? `${text.substring(0, 50)}...` : text;
    console.log(`Fallback TTS API: Processing request with voice=${voice}, text="${truncatedText}"`);
    console.log(`Text length: ${text ? text.length : 0} characters`);

    // Validate text is provided
    if (!text) {
      console.warn('Fallback TTS API: No text provided');
      return NextResponse.json(
        {
          error: 'Missing parameter',
          message: 'Text parameter is required'
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Validate text length
    if (text.length > MAX_TEXT_LENGTH) {
      console.warn(`Fallback TTS API: Text too long (${text.length} chars). Max allowed is ${MAX_TEXT_LENGTH}.`);
      return NextResponse.json(
        {
          error: 'Text is too long for TTS processing',
          message: `Maximum allowed length is ${MAX_TEXT_LENGTH} characters, but received ${text.length} characters.`,
          maxLength: MAX_TEXT_LENGTH,
          actualLength: text.length
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    console.log('Fallback TTS API: Using Echo TTS API directly');

    // Create a URL to the echo-tts endpoint
    const echoUrl = new URL('/api/echo-tts', request.nextUrl.origin);
    echoUrl.searchParams.append('text', text);
    echoUrl.searchParams.append('voice', voice);
    echoUrl.searchParams.append('t', Date.now().toString());

    // Make the request to the echo-tts endpoint
    const response = await fetch(echoUrl.toString(), {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      throw new Error(`Echo TTS API returned status ${response.status}`);
    }

    // Get audio data
    const audioBuffer = await response.arrayBuffer();

    // Check if we received valid audio data
    if (audioBuffer.byteLength === 0) {
      throw new Error(`Echo TTS API returned empty audio data`);
    }

    console.log(`Fallback TTS API: Echo TTS successful, returning audio data (${audioBuffer.byteLength} bytes)`);

    // Return the audio as a response with appropriate headers
    return new NextResponse(audioBuffer, {
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Accept-Ranges': 'bytes',
        'Timing-Allow-Origin': '*',
        'X-Content-Type-Options': 'nosniff'
      },
    });
  } catch (error: any) {
    // Log the detailed error
    console.error('Fallback TTS API: Error generating speech:', error);

    // Create a simple WAV beep sound as a last resort
    // WAV format is more universally supported than MP3 in browsers
    const wavBytes = new Uint8Array([
      // "RIFF" chunk descriptor
      0x52, 0x49, 0x46, 0x46, // "RIFF" in ASCII
      0x34, 0x00, 0x00, 0x00, // Chunk size (36 + data size = 52)
      0x57, 0x41, 0x56, 0x45, // "WAVE" in ASCII

      // "fmt " sub-chunk
      0x66, 0x6d, 0x74, 0x20, // "fmt " in ASCII
      0x10, 0x00, 0x00, 0x00, // Sub-chunk size (16 bytes)
      0x01, 0x00,             // Audio format (1 = PCM)
      0x01, 0x00,             // Number of channels (1 = mono)
      0x44, 0xac, 0x00, 0x00, // Sample rate (44100 Hz)
      0x88, 0x58, 0x01, 0x00, // Byte rate (44100 * 1 * 2)
      0x02, 0x00,             // Block align (NumChannels * BitsPerSample/8)
      0x10, 0x00,             // Bits per sample (16)

      // "data" sub-chunk
      0x64, 0x61, 0x74, 0x61, // "data" in ASCII
      0x10, 0x00, 0x00, 0x00, // Sub-chunk size (16 bytes of data)

      // Actual audio data - a simple beep (8 samples of 16-bit PCM)
      0x00, 0x00, 0xFF, 0x7F, // Sample 1 and 2
      0x00, 0x00, 0xFF, 0x7F, // Sample 3 and 4
      0x00, 0x00, 0xFF, 0x7F, // Sample 5 and 6
      0x00, 0x00, 0xFF, 0x7F  // Sample 7 and 8
    ]);

    console.log(`Fallback TTS API: Returning direct beep sound as last resort (${wavBytes.buffer.byteLength} bytes)`);

    // Return the audio as a response with appropriate headers
    return new NextResponse(wavBytes.buffer, {
      headers: {
        'Content-Type': 'audio/wav',
        'Content-Length': wavBytes.buffer.byteLength.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Accept-Ranges': 'bytes',
        'Timing-Allow-Origin': '*',
        'X-Content-Type-Options': 'nosniff'
      }
    });
  }
}

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  console.log('Fallback TTS API: OPTIONS request received (CORS preflight)');
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma, Expires',
      'Access-Control-Max-Age': '86400',
      'Access-Control-Allow-Credentials': 'true',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}
