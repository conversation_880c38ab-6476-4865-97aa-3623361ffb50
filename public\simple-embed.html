<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Chat Widget</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        
        #chat-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        #chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }
        
        #chat-iframe {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 600px;
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            display: none;
        }
        
        @media (max-width: 480px) {
            #chat-iframe {
                width: 100%;
                height: 100%;
                bottom: 0;
                right: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div id="chat-button">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
    </div>
    
    <iframe id="chat-iframe" allow="microphone"></iframe>
    
    <script>
        // Get the chat ID from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const chatId = urlParams.get('chatId') || 'default';
        const baseUrl = urlParams.get('baseUrl') || window.location.origin;
        
        // Get the elements
        const button = document.getElementById('chat-button');
        const iframe = document.getElementById('chat-iframe');
        
        // Add click event to button
        button.onclick = function() {
            iframe.style.display = 'block';
            this.style.display = 'none';
            
            // Only set the src when the iframe is first opened
            if (!iframe.src) {
                iframe.src = baseUrl + '/embed/' + chatId;
            }
        };
        
        // Add message listener for close events
        window.addEventListener('message', function(event) {
            if (event.data === 'botfusion-chat-close') {
                iframe.style.display = 'none';
                button.style.display = 'flex';
            }
        });
    </script>
</body>
</html>
