import { NextRequest, NextResponse } from 'next/server';

// This is a special API route that serves a simple CSP-friendly embed script
// It's designed to work with the strictest Content Security Policy settings
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId') || 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';

    // Get the base URL from the environment or use the current origin
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;

    // Create the script content directly
    const scriptContent = `
/**
 * BotFusion Ultra-Simple CSP-friendly embed script
 * This script is designed to work with the strictest Content Security Policy settings
 * Version: 3.0.0
 */

(function() {
  // Create the button element
  const button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.setAttribute('role', 'button');
  button.setAttribute('aria-label', 'Open chat');
  button.setAttribute('tabindex', '0');

  // Style the button
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#3b82f6';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '9999';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

  // Add the chat icon
  button.innerHTML = \`
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
    </svg>
  \`;

  // Create the iframe element
  const iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.src = '${baseUrl}/embed/${chatId}';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = 'none';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'none';
  iframe.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
  iframe.style.opacity = '0';
  iframe.setAttribute('allow', 'microphone');
  iframe.setAttribute('title', 'Chat Widget');
  iframe.setAttribute('loading', 'lazy');
  iframe.setAttribute('importance', 'high');
  iframe.setAttribute('referrerpolicy', 'origin');
  iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox');

  // Add hover effect to button
  button.addEventListener('mouseover', function() {
    button.style.transform = 'scale(1.05)';
    button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  });

  button.addEventListener('mouseout', function() {
    button.style.transform = 'scale(1)';
    button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  });

  // Toggle chat window when button is clicked
  button.addEventListener('click', function() {
    iframe.style.display = 'block';
    button.style.display = 'none';

    // Add animation
    iframe.style.transform = 'translateY(20px)';
    iframe.style.opacity = '0';

    setTimeout(function() {
      iframe.style.transform = 'translateY(0)';
      iframe.style.opacity = '1';
    }, 10);
  });

  // Add message listener to handle close button in iframe
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframe
    if (event.data === 'botfusion-chat-close') {
      // Add closing animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';

      setTimeout(function() {
        iframe.style.display = 'none';
        button.style.display = 'flex';
      }, 300);
    }
  });

  // Append elements to the document
  document.body.appendChild(iframe);
  document.body.appendChild(button);

  // Expose the BotFusionChat object to the window
  window.BotFusionChat = {
    open: function() {
      iframe.style.display = 'block';
      button.style.display = 'none';

      // Add animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';

      setTimeout(function() {
        iframe.style.transform = 'translateY(0)';
        iframe.style.opacity = '1';
      }, 10);
    },
    close: function() {
      // Add closing animation
      iframe.style.transform = 'translateY(20px)';
      iframe.style.opacity = '0';

      setTimeout(function() {
        iframe.style.display = 'none';
        button.style.display = 'flex';
      }, 300);
    }
  };

  console.log('BotFusion Chat: Widget initialized successfully');
})();`;

    // Return the script with proper CORS headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    });
  } catch (error) {
    console.error('Error serving simple CSP embed script:', error);
    return new NextResponse('console.error("Error loading BotFusion Chat Widget");', {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
      },
    });
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'Content-Security-Policy': "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;",
    },
  });
}
