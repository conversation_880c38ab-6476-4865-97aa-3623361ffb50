'use client';

import { useState, useEffect, useRef } from 'react';
import SimpleAudioPlayer from '@/components/SimpleAudioPlayer';

export default function TTSTestPage() {
  const [text, setText] = useState('This is a test of the Deepgram Text-to-Speech API. If you can hear this message, the API is working correctly.');
  const [voice, setVoice] = useState('nova');
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [endpoint, setEndpoint] = useState('deepgram-tts');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const logRef = useRef<HTMLDivElement>(null);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString().split('T')[1].split('.')[0]} - ${message}`]);
  };

  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);

  const handlePlay = async () => {
    if (!text) {
      setError('Please enter some text to convert to speech.');
      return;
    }

    setError(null);
    setIsPlaying(true);
    addLog(`Starting TTS with endpoint: ${endpoint}`);

    try {
      // Create a timestamp to prevent caching
      const timestamp = Date.now();

      // Create the URL with query parameters
      const ttsUrl = new URL(`/api/${endpoint}`, window.location.origin);
      ttsUrl.searchParams.append('text', text);
      ttsUrl.searchParams.append('voice', voice);
      ttsUrl.searchParams.append('t', timestamp.toString());

      addLog(`Calling TTS API: ${ttsUrl.toString()}`);

      // Make the request
      const response = await fetch(ttsUrl.toString(), {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      });

      if (!response.ok) {
        // Try to get error details from the response
        const responseClone = response.clone();
        const errorText = await responseClone.text();
        let errorMessage = `API request failed: ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) {
          // If JSON parsing fails, use the raw text if available
          if (errorText) {
            if (errorText.includes('<!doctype') || errorText.includes('<html')) {
              errorMessage = 'Received HTML response instead of audio. This might indicate authentication is required.';
              addLog('Received HTML response - authentication issue');
            } else {
              errorMessage = errorText;
            }
          }
        }

        throw new Error(errorMessage);
      }

      // Get audio data as blob
      const audioData = await response.arrayBuffer();

      if (audioData.byteLength === 0) {
        throw new Error('Received empty audio data from the server');
      }

      // Determine content type based on endpoint
      let contentType = 'audio/wav';
      if (endpoint === 'deepgram-tts') {
        contentType = 'audio/mpeg';
      }

      const audioBlob = new Blob([audioData], { type: contentType });

      addLog(`Received audio data: ${audioBlob.size} bytes`);

      // Create audio element and play
      const audioUrl = URL.createObjectURL(audioBlob);
      addLog(`Created audio URL from blob: ${audioUrl}`);

      const audio = new Audio();

      // Set crossOrigin to anonymous to help with CORS issues
      audio.crossOrigin = 'anonymous';

      // Set up event listeners before setting the source
      audio.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
        addLog('Audio playback completed');
      };

      audio.onerror = (e) => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);

        // Try to get more detailed error information
        const errorCode = audio.error ? audio.error.code : 'unknown';
        const errorMessage = audio.error ? audio.error.message : 'Unknown error';

        const errorDetails = `Audio error: Code ${errorCode}, Message: ${errorMessage}`;
        setError(errorDetails);
        addLog(errorDetails);

        console.error('Audio playback error:', {
          error: e,
          code: errorCode,
          message: errorMessage,
          audioElement: audio
        });
      };

      audio.onloadeddata = () => {
        addLog('Audio data loaded successfully');
      };

      // Now set the source
      audio.src = audioUrl;

      addLog('Starting audio playback');
      try {
        await audio.play();
      } catch (playError) {
        setIsPlaying(false);

        // Check if this is a DOMException related to autoplay policy
        if (playError.name === 'NotAllowedError') {
          const message = 'Autoplay blocked by browser. Please interact with the page first (click anywhere) and try again.';
          setError(message);
          addLog(message);
        } else {
          setError(`Failed to play audio: ${playError.message || 'Unknown error'}`);
          addLog(`Play error: ${playError.message || 'Unknown error'}`);
        }

        console.error('Error playing audio:', playError);
      }
    } catch (error: any) {
      setIsPlaying(false);
      setError(error.message || 'An error occurred while generating speech');
      addLog(`Error: ${error.message || 'Unknown error'}`);
    }
  };

  const handleFallbackTest = () => {
    setEndpoint('fallback-tts');
    addLog('Switched to fallback-tts endpoint');
  };

  const handleDeepgramTest = () => {
    setEndpoint('deepgram-tts');
    addLog('Switched to deepgram-tts endpoint');
  };

  const handleEchoTest = () => {
    setEndpoint('echo-tts');
    addLog('Switched to echo-tts endpoint (last resort fallback)');
  };

  const toggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    addLog(`${showAdvanced ? 'Hiding' : 'Showing'} advanced options`);
  };

  return (
    <div className="container mx-auto p-4 max-w-3xl">
      <h1 className="text-2xl font-bold mb-4">Deepgram TTS Test Page</h1>

      <div className="mb-4">
        <label className="block mb-2">Text to convert to speech:</label>
        <textarea
          className="w-full p-2 border rounded"
          rows={4}
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
      </div>

      <div className="mb-4">
        <label className="block mb-2">Voice:</label>
        <select
          className="p-2 border rounded"
          value={voice}
          onChange={(e) => setVoice(e.target.value)}
        >
          <option value="alloy">Alloy</option>
          <option value="echo">Echo</option>
          <option value="fable">Fable</option>
          <option value="onyx">Onyx</option>
          <option value="nova">Nova</option>
          <option value="shimmer">Shimmer</option>
        </select>
      </div>

      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <label className="block">Endpoint:</label>
          <button
            className="text-sm text-blue-500 hover:underline"
            onClick={toggleAdvanced}
          >
            {showAdvanced ? 'Hide Advanced Options' : 'Show Advanced Options'}
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            className={`px-4 py-2 rounded ${endpoint === 'deepgram-tts' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={handleDeepgramTest}
          >
            deepgram-tts
          </button>
          {showAdvanced && (
            <>
              <button
                className={`px-4 py-2 rounded ${endpoint === 'fallback-tts' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                onClick={handleFallbackTest}
              >
                fallback-tts
              </button>
              <button
                className={`px-4 py-2 rounded ${endpoint === 'echo-tts' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                onClick={handleEchoTest}
              >
                echo-tts
              </button>
            </>
          )}
        </div>
        {showAdvanced && (
          <div className="mt-2 text-xs text-gray-500">
            <p>- deepgram-tts: Primary TTS endpoint (Deepgram TTS via edge function)</p>
            <p>- fallback-tts: Fallback TTS using external services</p>
            <p>- echo-tts: Last resort fallback (returns a simple beep sound)</p>
          </div>
        )}
      </div>

      <div className="mb-4">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-400"
          onClick={handlePlay}
          disabled={isPlaying}
        >
          {isPlaying ? 'Playing...' : 'Generate Speech'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">SimpleAudioPlayer Component Test</h2>
        <SimpleAudioPlayer
          text={text}
          voice={voice as any}
        />
      </div>

      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">Logs</h2>
        <div
          ref={logRef}
          className="h-64 overflow-y-auto p-2 bg-gray-100 font-mono text-sm"
        >
          {logs.map((log, index) => (
            <div key={index} className="mb-1">{log}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
