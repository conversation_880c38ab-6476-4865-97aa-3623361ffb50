'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { SpeechToTextRef } from '../../../components/SpeechToText'
import UnifiedSpeechToText from '../../../components/UnifiedSpeechToText'
import SpeechToTextProvider from '../../../contexts/SpeechToTextContext'
import TextToSpeech, { type DeepgramVoice } from '../../../components/TextToSpeech'
import { STTProvider } from '../../../types/deepgram-stt'

interface ChatInterface {
  id: string
  name: string
  description: string
  webhookurl: string
  primary_color?: string
  use_gradient_header?: boolean
  gradient_start_color?: string
  gradient_end_color?: string
  gradient_direction?: string
  user_bubble_color?: string
  bot_bubble_color?: string
  user_text_color?: string
  bot_text_color?: string
  logo_url?: string
  dark_mode?: boolean
  show_name?: boolean
  use_black_outline?: boolean
  show_powered_by?: boolean
  powered_by_text?: string
  powered_by_url?: string
  powered_by_text_color?: string
  welcome_message?: string
  enable_natural_speech?: boolean
  natural_speech_model?: string
  natural_speech_temperature?: number
  natural_speech_max_tokens?: number
  voice_model?: string
}

export default function ChatPage() {
  const params = useParams()
  const id = params?.id as string

  const [chatInterface, setChatInterface] = useState<ChatInterface | null>(null)
  const [loading, setLoading] = useState(true)
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<Array<{
    id: string
    content: string
    sender: 'user' | 'bot'
    timestamp: string
    hidden?: boolean
  }>>([])
  const [sending, setSending] = useState(false)
  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false)
  const [messageWasVoiceInput, setMessageWasVoiceInput] = useState(false) // Track if current message was created via voice
  const [lastBotMessage, setLastBotMessage] = useState('')
  const [pendingBotMessageId, setPendingBotMessageId] = useState<string | null>(null)

  // Use refs to track state changes and prevent infinite loops
  const audioReadyCalledRef = useRef<{[messageId: string]: boolean}>({})
  const speechToTextRef = useRef<SpeechToTextRef>(null)

  // Store all timeouts for proper cleanup
  const timeoutsRef = useRef<{[key: string]: NodeJS.Timeout}>({});

  // Function to set a timeout with automatic tracking for cleanup
  const setManagedTimeout = useCallback((callback: () => void, delay: number, timeoutKey: string) => {
    // Clear any existing timeout with this key
    if (timeoutsRef.current[timeoutKey]) {
      clearTimeout(timeoutsRef.current[timeoutKey]);
      delete timeoutsRef.current[timeoutKey];
    }

    // Set the new timeout and store it in the ref
    const timeoutId = setTimeout(() => {
      // Remove the timeout from the ref when it completes
      delete timeoutsRef.current[timeoutKey];

      // Call the callback
      callback();
    }, delay);

    // Store the timeout ID in the ref
    timeoutsRef.current[timeoutKey] = timeoutId;

    return timeoutId;
  }, []);

  // Function to clear all timeouts
  const clearAllTimeouts = useCallback(() => {
    Object.values(timeoutsRef.current).forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    timeoutsRef.current = {};
  }, []);

  // Create a memoized audio ready callback
  const handleAudioReady = useCallback((providedMessageId?: string) => {
    // Use the provided message ID if available, otherwise use the pending bot message ID
    const messageId = providedMessageId || pendingBotMessageId || 'unknown';
    console.log(`Audio ready callback received for message: ${messageId}`);

    if (audioReadyCalledRef.current[messageId]) {
      console.log('Audio ready callback already called for message:', messageId);
      return;
    }

    // Mark this message as handled
    audioReadyCalledRef.current[messageId] = true;

    // When audio is ready, reveal the hidden message
    if (pendingBotMessageId && (messageId === pendingBotMessageId || messageId === 'unknown')) {
      console.log('Audio ready, revealing message:', pendingBotMessageId);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === pendingBotMessageId
            ? { ...msg, hidden: false }
            : msg
        )
      );
      setPendingBotMessageId(null);
    }

    // Only reset microphone state after a delay to ensure audio has time to play
    // This prevents resetting too early which can cut off audio
    setManagedTimeout(() => {
      console.log('Resetting microphone active state after audio is ready (with delay)');

      // Use the ref method if available, otherwise fall back to the state setter
      if (speechToTextRef.current) {
        speechToTextRef.current.resetMicrophoneActive();
      } else {
        setIsMicrophoneActive(false);
      }

      // Clean up the ref after a while to prevent memory leaks
      setManagedTimeout(() => {
        if (messageId !== 'unknown' && audioReadyCalledRef.current[messageId]) {
          delete audioReadyCalledRef.current[messageId];
        }
      }, 5000, `cleanup-${messageId}`);
    }, 1000, `reset-mic-${messageId}`);
  }, [pendingBotMessageId, setMessages, setIsMicrophoneActive, setManagedTimeout]);

  useEffect(() => {
    const fetchChatInterface = async () => {
      try {
        // Check if we're in an iframe (embedded) or direct
        const isEmbedded = window.self !== window.top;
        const searchParams = new URLSearchParams(window.location.search);
        const embedParam = searchParams.get('embed') === 'true' || isEmbedded;

        // Construct the API URL with appropriate parameters
        const apiUrl = `/api/chat/${id}${embedParam ? '?embed=true' : ''}`;

        // Set up headers based on context
        const headers: HeadersInit = {};
        if (embedParam) {
          headers['X-Embed-Request'] = 'true';
          headers['Authorization'] = 'Bearer embed';
        }

        console.log(`Fetching chat interface from ${apiUrl} with headers:`, headers);

        // Add cache-busting parameter to prevent caching issues
        const cacheBustUrl = `${apiUrl}${apiUrl.includes('?') ? '&' : '?'}t=${Date.now()}`;
        const response = await fetch(cacheBustUrl, {
          headers,
          cache: 'no-store',
          credentials: 'same-origin'
        });

        if (!response.ok) {
          console.error(`Failed to load chat interface: ${response.status}`, response);
          throw new Error(`Failed to load chat interface: ${response.status}`);
        }

        const data = await response.json();
        console.log('Chat interface data received:', data);
        setChatInterface(data);

        // If there's a welcome message, add it to the messages
        if (data.welcome_message) {
          setMessages([{
            id: 'welcome-message',
            content: data.welcome_message,
            sender: 'bot',
            timestamp: new Date().toISOString()
          }]);
        }
      } catch (error) {
        console.error('Error fetching chat interface:', error);
        toast.error('Failed to load chat interface');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchChatInterface();
    }
  }, [id])

  // Backup timer to ensure message is revealed even if TTS fails
  useEffect(() => {
    if (!pendingBotMessageId) return;

    console.log('Setting backup timer to reveal message');

    // Use managed timeout for the backup timer
    setManagedTimeout(() => {
      console.log('Backup timer triggered, revealing message:', pendingBotMessageId);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === pendingBotMessageId
            ? { ...msg, hidden: false }
            : msg
        )
      );
      setPendingBotMessageId(null);

      // Use the ref method if available, otherwise fall back to the state setter
      if (speechToTextRef.current) {
        speechToTextRef.current.resetMicrophoneActive();
      } else {
        setIsMicrophoneActive(false);
      }
    }, 5000, `backup-timer-${pendingBotMessageId}`); // 5 second backup timer

    // No need for explicit cleanup as clearAllTimeouts will handle it
  }, [pendingBotMessageId, setManagedTimeout, setMessages, setIsMicrophoneActive]);

  // Cleanup all timeouts when component unmounts
  useEffect(() => {
    // Store ref in a variable to avoid React warning
    const speechToTextRefCurrent = speechToTextRef.current;

    return () => {
      console.log('Cleaning up all timeouts on unmount');
      clearAllTimeouts();

      // Also reset microphone state
      if (speechToTextRefCurrent) {
        try {
          speechToTextRefCurrent.resetMicrophoneActive();
        } catch (error) {
          console.error('Error resetting microphone state during cleanup:', error);
          // Fallback to state setter
          setIsMicrophoneActive(false);
        }
      } else {
        setIsMicrophoneActive(false);
      }
    };
  }, [clearAllTimeouts, setIsMicrophoneActive]);

  // Track the last message sent to prevent duplicates
  const lastSentMessageRef = useRef<string>('');
  const lastSentTimeRef = useRef<number>(0);

  // Ref for auto-scrolling to bottom
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const sendMessage = async (voiceTranscript?: string) => {
    const startTime = Date.now();
    console.log('🚀 [PERFORMANCE] Frontend sendMessage started at:', new Date().toISOString());

    const messageToSend = voiceTranscript || message;
    console.log('[DEBUG] sendMessage called, message state:', `"${message}"`, 'voiceTranscript:', `"${voiceTranscript || 'none'}"`, 'using:', `"${messageToSend}"`);
    if (!messageToSend.trim() || !chatInterface) {
      console.log('[DEBUG] sendMessage early return - message empty or no chatInterface');
      return;
    }

    // Check for duplicate messages sent within a short time window (1.5 seconds)
    const currentTime = Date.now();
    const timeSinceLastMessage = currentTime - lastSentTimeRef.current;

    if (messageToSend === lastSentMessageRef.current && timeSinceLastMessage < 1500) {
      console.log('Preventing duplicate message send:', messageToSend);
      console.log(`Time since last message: ${timeSinceLastMessage}ms`);
      return;
    }

    // Update last sent message tracking
    lastSentMessageRef.current = messageToSend;
    lastSentTimeRef.current = currentTime;

    // Add user message to chat
    const userMessage = {
      id: currentTime.toString(),
      content: messageToSend,
      sender: 'user' as const,
      timestamp: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    // Only clear message state if this wasn't a voice transcript
    if (!voiceTranscript) {
      setMessage('')
    }
    setSending(true)

    // Add typing indicator
    const typingIndicator = {
      id: 'typing-indicator',
      content: '•••',
      sender: 'bot' as const,
      timestamp: new Date().toISOString()
    }
    setMessages(prev => [...prev, typingIndicator])

    try {
      console.log('Sending message to webhook via proxy:', chatInterface.webhookurl);

      // Prepare the request body for our proxy
      const requestBody = {
        webhookUrl: chatInterface.webhookurl,
        message: userMessage.content,
        chatId: id,
        sessionId: localStorage.getItem('chatSessionId') || Date.now().toString()
      };

      console.log('Request body:', requestBody);

      // Send message to our proxy API instead of directly to the webhook
      const apiStartTime = Date.now();
      console.log('🚀 [PERFORMANCE] API call started at:', new Date().toISOString());

      const response = await fetch('/api/webhook-proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const apiTime = Date.now() - apiStartTime;
      console.log('🚀 [PERFORMANCE] API call took:', apiTime, 'ms');

      console.log('Webhook response status:', response.status);

      if (!response.ok) {
        console.error('Webhook response not OK:', response.status, response.statusText);
        throw new Error(`Failed to send message: ${response.status} ${response.statusText}`)
      }

      // Get response from webhook
      const responseData = await response.json();
      console.log('Webhook response data:', responseData);

      // Create bot message with unique ID
      const botMessageId = (Date.now() + 1).toString();

      // Extract the bot response from the webhook response
      // The webhook-proxy returns the response in the 'response' field
      const botMessageContent = responseData.response ||
                               (responseData.data && (responseData.data.response || responseData.data.output || responseData.data.text || responseData.data.message)) ||
                               'Sorry, I couldn&apos;t process your request.';

      // Create the bot message object
      const botMessage = {
        id: botMessageId,
        content: botMessageContent,
        sender: 'bot' as const,
        timestamp: new Date().toISOString(),
        // Always show the message immediately - we'll handle TTS separately
        hidden: false
      }

      console.log('Created bot message:', botMessage);
      console.log('Microphone active:', isMicrophoneActive);

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing-indicator'))

      // Store the message ID so we can unhide it when audio is ready
      if (isMicrophoneActive) {
        setPendingBotMessageId(botMessageId);

        // Set a backup timer to reveal the message after 5 seconds
        // This ensures the message is shown even if TTS fails
        setManagedTimeout(() => {
          console.log('Backup timer triggered, revealing message:', botMessageId);
          setMessages(prev =>
            prev.map(msg =>
              msg.id === botMessageId
                ? { ...msg, hidden: false }
                : msg
            )
          );
          setPendingBotMessageId(null);
        }, 5000, `send-backup-timer-${botMessageId}`);
      }

      // OPTIMIZATION: Reduced delay for faster response display
      setManagedTimeout(() => {
        setMessages(prev => [...prev, botMessage])
        // Save the bot message for TTS
        setLastBotMessage(botMessageContent)
      }, 100, `add-bot-message-${botMessageId}`) // Reduced from 500ms to 100ms

      // Store session ID for future messages
      if (!localStorage.getItem('chatSessionId')) {
        localStorage.setItem('chatSessionId', Date.now().toString())
      }

    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Failed to send message')

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing-indicator'))

      // Add error message from bot
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, there was an error processing your message. Please try again later.',
        sender: 'bot' as const,
        timestamp: new Date().toISOString()
      }

      setManagedTimeout(() => {
        setMessages(prev => [...prev, errorMessage])
      }, 500, 'error-message')
    } finally {
      setSending(false)
      const totalTime = Date.now() - startTime;
      console.log('🚀 [PERFORMANCE] Total sendMessage took:', totalTime, 'ms');
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neon-blue"></div>
      </div>
    )
  }

  if (!chatInterface) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-6 max-w-md w-full text-center">
          <h2 className="text-xl font-bold text-red-500">Chat Not Found</h2>
          <p className="mt-2 text-gray-400">This chat interface doesn&apos;t exist or has been removed.</p>
        </Card>
      </div>
    )
  }

  // Get custom colors and settings from chat interface
  const primaryColor = chatInterface.primary_color || '#3b82f6';
  const useGradientHeader = chatInterface.use_gradient_header || false;
  const gradientStartColor = chatInterface.gradient_start_color || '#3b82f6';
  const gradientEndColor = chatInterface.gradient_end_color || '#9333ea';
  const gradientDirection = chatInterface.gradient_direction || 'to right';
  const userBubbleColor = chatInterface.user_bubble_color || '#ffffff';
  const botBubbleColor = chatInterface.bot_bubble_color || '#3b82f6';
  const userTextColor = chatInterface.user_text_color || '#000000';
  const botTextColor = chatInterface.bot_text_color || '#ffffff';
  const logoUrl = chatInterface.logo_url || '';
  const isDarkMode = chatInterface.dark_mode || false;
  const showName = chatInterface.show_name !== false; // Default to true if not specified
  // Handle case where columns might not exist in the database yet
  const showPoweredBy = chatInterface.show_powered_by !== undefined ? chatInterface.show_powered_by : true;
  const poweredByText = chatInterface.powered_by_text || 'Powered by BotFusion';
  const poweredByUrl = chatInterface.powered_by_url || 'https://botfusion.io';
  const poweredByTextColor = chatInterface.powered_by_text_color || primaryColor;
  const useBlackOutline = chatInterface.use_black_outline || false;

  // Determine background colors based on dark mode
  const bgColor = isDarkMode ? 'bg-gray-900' : 'bg-white';
  const chatAreaBg = isDarkMode ? 'bg-gray-800' : 'bg-gray-50';
  const inputBgColor = isDarkMode ? 'bg-gray-800' : 'bg-white';
  const inputBorderColor = isDarkMode ? 'border-gray-700' : 'border-gray-300';

  return (
    <SpeechToTextProvider provider={STTProvider.AUTO}>
      <div
        className={`chat-interface flex flex-col h-screen max-h-screen ${bgColor} ${useBlackOutline ? 'border-2 border-black' : ''}`}
        style={{
          borderWidth: useBlackOutline ? '2px' : '0',
          borderColor: useBlackOutline ? 'black' : 'transparent',
          borderStyle: useBlackOutline ? 'solid' : 'none'
        }}
      >
      {/* Chat header */}
      <div
        style={useGradientHeader
          ? { background: `linear-gradient(${gradientDirection}, ${gradientStartColor}, ${gradientEndColor})` }
          : { backgroundColor: primaryColor }
        }
        className="p-4 shadow-md"
      >
        <div className="flex items-center gap-3">
          {logoUrl && (
            <div className="relative h-12 w-12">
              <Image
                src={logoUrl}
                alt={`${chatInterface.name} Logo`}
                fill
                sizes="48px"
                className="object-contain"
                unoptimized={logoUrl.startsWith('data:') || logoUrl.includes('blob:')}
              />
            </div>
          )}
          {showName && (
            <h1 className="text-xl font-medium text-white">{chatInterface.name}</h1>
          )}
        </div>
      </div>

      {/* Chat messages */}
      <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${chatAreaBg}`}>
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className={isDarkMode ? "text-gray-400" : "text-gray-500"}>Send a message to start chatting</p>
          </div>
        ) : (
          messages.map(msg => (
            <div
              key={msg.id}
              className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 shadow-sm ${
                  msg.sender === 'user'
                    ? 'border border-gray-200'
                    : ''
                }`}
                style={{
                  backgroundColor: msg.sender === 'user' ? userBubbleColor : botBubbleColor,
                  color: msg.sender === 'user' ? userTextColor : botTextColor
                }}
              >
                {msg.id === 'typing-indicator' ? (
                  <div className="flex space-x-1 items-center">
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                ) : msg.hidden ? (
                  <div className="flex space-x-1 items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                ) : (
                  <>
                    <p className="whitespace-pre-wrap font-normal text-base leading-relaxed" style={{ color: msg.sender === 'user' ? userTextColor : botTextColor }}>{msg.content}</p>
                    <span
                      className="text-xs mt-1 block"
                      style={{
                        color: msg.sender === 'user'
                          ? (userTextColor === '#000000' ? '#666666' : userTextColor)
                          : (botTextColor === '#ffffff' ? 'rgba(255, 255, 255, 0.8)' : botTextColor)
                      }}
                    >
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </span>
                  </>
                )}
              </div>
            </div>
          ))
        )}
        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${inputBgColor} shadow-inner`}>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            // If user submits via form (typing), this is NOT a voice input
            // But don't reset if this was triggered by voice auto-send
            if (!isMicrophoneActive && !messageWasVoiceInput) {
              setMessageWasVoiceInput(false);
              console.log('Form submitted via typing, TTS disabled for bot response');
            } else if (messageWasVoiceInput) {
              console.log('Form submitted via voice auto-send, TTS enabled for bot response');
            }
            sendMessage()
          }}
          className="flex gap-2"
        >
          <div className="flex-1 relative">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message..."
              className={`w-full min-h-[50px] max-h-[150px] ${inputBgColor} ${inputBorderColor} focus:border-opacity-100`}
              style={{
                borderColor: primaryColor,
                "--tw-ring-color": primaryColor,
                color: isDarkMode ? "white" : "black"
              } as React.CSSProperties}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  // If user presses Enter (typing), this is NOT a voice input
                  // But don't reset if this was set by voice input
                  if (!isMicrophoneActive && !messageWasVoiceInput) {
                    setMessageWasVoiceInput(false);
                    console.log('Enter key pressed (typing), TTS disabled for bot response');
                  } else if (messageWasVoiceInput) {
                    console.log('Enter key pressed after voice input, TTS enabled for bot response');
                  }
                  sendMessage()
                }
              }}
            />
          </div>
          <UnifiedSpeechToText
            ref={speechToTextRef}
            onTranscript={(text: string) => {
              // Only update text input if auto-send is disabled
              // For auto-send, transcript goes directly to chat without showing in input
              console.log('[DEBUG] Voice transcript received:', text, 'Auto-send enabled, skipping text input update');
            }}
            disabled={sending}
            color={primaryColor}
            onMicrophoneActiveChange={(active) => {
              setIsMicrophoneActive(active);
              if (active) {
                setMessageWasVoiceInput(true);
                console.log('Voice input detected, will enable TTS for next bot response');

                // IMMEDIATE GLOBAL AUDIO STOP using Global Audio Manager
                if (typeof window !== 'undefined' && window.globalAudioManager) {
                  window.globalAudioManager.stopAllAudio();
                }

                setLastBotMessage('');
              }
            }}
            autoSend={true}
            onSend={sendMessage}
          />
          <Button
            type="submit"
            disabled={sending || !message.trim()}
            className="text-white shadow-sm hover:opacity-90"
            style={{
              backgroundColor: primaryColor
            }}
          >
            {sending ? (
              <span className="animate-pulse">Sending...</span>
            ) : (
              <div className="flex items-center gap-1">
                <span>Send</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </div>
            )}
          </Button>
        </form>

        {/* Text-to-Speech component */}
        <TextToSpeech
          text={lastBotMessage}
          autoPlay={messageWasVoiceInput}
          voice={(chatInterface.voice_model || 'thalia') as DeepgramVoice} // Use interface-specific voice
          microphoneActive={isMicrophoneActive}
          onAudioReady={handleAudioReady}
          messageId={pendingBotMessageId || undefined} // Pass the bot message ID
          enableNaturalSpeech={chatInterface.enable_natural_speech !== false} // Default to true if not specified
          onStart={() => {
            console.log('TTS audio started playing, resetting voice input flag');
            // CRITICAL FIX: Delay resetting voice input flag to prevent autoPlay from changing during audio processing
            setTimeout(() => {
              setMessageWasVoiceInput(false);
            }, 1000); // Wait 1 second to ensure audio processing is stable
          }}
        />

        {/* Backup timer to ensure message is revealed even if TTS fails */}

      {/* Powered By section */}
        {showPoweredBy && (
          <div className="mt-4 pt-2 text-center">
            <a
              href={poweredByUrl}
              target="_blank"
              rel="noopener noreferrer"
              className={`text-xs hover:underline ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}
              style={{ color: poweredByTextColor }}
            >
              {poweredByText}
            </a>
          </div>
        )}
      </div>
    </div>
    </SpeechToTextProvider>
  )
}

