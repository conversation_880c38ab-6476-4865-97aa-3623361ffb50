import { NextRequest, NextResponse } from 'next/server';
import { AVAILABLE_VOICES } from '@/lib/voice-config';
import { buildDeepgramTTSUrl, DEEPGRAM_HEADERS, validateTTSConfiguration, STANDARD_AUDIO_CONFIG } from '@/lib/tts-config-standard';

const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;

export async function GET(request: NextRequest) {
  if (!DEEPGRAM_API_KEY) {
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  const testText = "Testing voice model with number 14 and smart formatting.";
  const results: any[] = [];
  let successCount = 0;
  let failureCount = 0;

  console.log(`🎵 Testing all ${AVAILABLE_VOICES.length} voice models with standardized configuration...`);

  // Test each voice model
  for (const voice of AVAILABLE_VOICES) {
    const startTime = Date.now();
    
    try {
      console.log(`Testing voice: ${voice.name} (${voice.model})`);
      
      // Test with standardized configuration
      const response = await fetch(buildDeepgramTTSUrl(voice.model, 'linear16'), {
        method: 'POST',
        headers: {
          ...DEEPGRAM_HEADERS,
          'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        },
        body: JSON.stringify({ text: testText })
      });

      const duration = Date.now() - startTime;

      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();
        const audioSize = audioBuffer.byteLength;
        
        successCount++;
        results.push({
          voice: voice.name,
          model: voice.model,
          gender: voice.gender,
          status: 'success',
          audioSize,
          duration,
          sampleRate: STANDARD_AUDIO_CONFIG.sampleRate,
          smartFormat: STANDARD_AUDIO_CONFIG.smartFormat,
          encoding: STANDARD_AUDIO_CONFIG.encoding
        });
        
        console.log(`✅ ${voice.name}: ${audioSize} bytes in ${duration}ms`);
      } else {
        failureCount++;
        const errorText = await response.text();
        results.push({
          voice: voice.name,
          model: voice.model,
          gender: voice.gender,
          status: 'error',
          error: `HTTP ${response.status}: ${errorText}`,
          duration
        });
        
        console.log(`❌ ${voice.name}: HTTP ${response.status}`);
      }
    } catch (error: any) {
      failureCount++;
      const duration = Date.now() - startTime;
      results.push({
        voice: voice.name,
        model: voice.model,
        gender: voice.gender,
        status: 'error',
        error: error.message,
        duration
      });
      
      console.log(`❌ ${voice.name}: ${error.message}`);
    }

    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Validate configuration compliance
  const configValidation = validateTTSConfiguration(STANDARD_AUDIO_CONFIG);

  // Generate summary
  const summary = {
    totalVoices: AVAILABLE_VOICES.length,
    successCount,
    failureCount,
    successRate: `${((successCount / AVAILABLE_VOICES.length) * 100).toFixed(1)}%`,
    testText,
    standardConfiguration: STANDARD_AUDIO_CONFIG,
    configurationValid: configValidation.isValid,
    configurationIssues: configValidation.issues,
    voicesByGender: {
      female: results.filter(r => r.gender === 'female').length,
      male: results.filter(r => r.gender === 'male').length
    },
    averageDuration: Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length),
    totalTestDuration: results.reduce((sum, r) => sum + r.duration, 0)
  };

  console.log(`🎯 Voice Model Test Complete: ${successCount}/${AVAILABLE_VOICES.length} voices working`);

  return NextResponse.json({
    summary,
    results,
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: NextRequest) {
  // Test specific voice with custom text
  const { voiceModel, text = "Testing voice model with number 14 and smart formatting." } = await request.json();
  
  if (!DEEPGRAM_API_KEY) {
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  if (!voiceModel) {
    return NextResponse.json({ error: 'Voice model is required' }, { status: 400 });
  }

  const voice = AVAILABLE_VOICES.find(v => v.model === voiceModel || v.id === voiceModel);
  if (!voice) {
    return NextResponse.json({ error: 'Voice model not found' }, { status: 404 });
  }

  try {
    const startTime = Date.now();
    
    const response = await fetch(buildDeepgramTTSUrl(voice.model, 'linear16'), {
      method: 'POST',
      headers: {
        ...DEEPGRAM_HEADERS,
        'Authorization': `Token ${DEEPGRAM_API_KEY}`,
      },
      body: JSON.stringify({ text })
    });

    const duration = Date.now() - startTime;

    if (response.ok) {
      const audioBuffer = await response.arrayBuffer();
      
      return new Response(audioBuffer, {
        headers: {
          'Content-Type': 'audio/wav',
          'Content-Disposition': `attachment; filename="${voice.id}-test.wav"`,
          'X-Voice-Model': voice.model,
          'X-Test-Duration': duration.toString(),
          'X-Audio-Size': audioBuffer.byteLength.toString(),
          'X-Sample-Rate': STANDARD_AUDIO_CONFIG.sampleRate.toString(),
          'X-Smart-Format': STANDARD_AUDIO_CONFIG.smartFormat.toString()
        }
      });
    } else {
      const errorText = await response.text();
      return NextResponse.json({ 
        error: `Deepgram API error: ${response.status}`,
        details: errorText,
        voice: voice.name,
        model: voice.model
      }, { status: response.status });
    }
  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Test failed',
      details: error.message,
      voice: voice.name,
      model: voice.model
    }, { status: 500 });
  }
}
