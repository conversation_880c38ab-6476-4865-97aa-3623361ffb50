import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@deepgram/sdk';

export async function GET(request: NextRequest) {
  try {
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      return NextResponse.json({ 
        error: 'Deepgram API key not configured',
        status: 'MISSING_API_KEY'
      }, { status: 500 });
    }

    console.log('Testing Deepgram API key authentication...');
    console.log('API Key (first 10 chars):', deepgramApiKey.substring(0, 10) + '...');

    // Test 1: Create Deepgram client
    const deepgram = createClient(deepgramApiKey);
    console.log('✅ Deepgram client created successfully');

    // Test 2: Try to get token details (simple auth test)
    try {
      const { result: tokenDetails, error: tokenError } = await deepgram.manage.getTokenDetails();
      
      if (tokenError) {
        console.error('❌ Token details error:', tokenError);
        return NextResponse.json({
          error: 'Authentication failed',
          status: 'AUTH_FAILED',
          details: tokenError
        }, { status: 401 });
      }

      console.log('✅ Token details retrieved successfully');
      console.log('Token details:', tokenDetails);

      // Test 3: Try a simple REST TTS request
      try {
        console.log('Testing REST TTS API...');
        const ttsResponse = await fetch(`https://api.deepgram.com/v1/speak?model=aura-2-thalia-en&encoding=mp3`, {
          method: 'POST',
          headers: {
            'Authorization': `Token ${deepgramApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: 'Hello, this is a test.'
          })
        });

        console.log('REST TTS Response status:', ttsResponse.status);
        console.log('REST TTS Response headers:', Object.fromEntries(ttsResponse.headers.entries()));

        if (ttsResponse.ok) {
          const audioBuffer = await ttsResponse.arrayBuffer();
          console.log('✅ REST TTS working, audio size:', audioBuffer.byteLength, 'bytes');
          
          return NextResponse.json({
            status: 'SUCCESS',
            message: 'Deepgram API key is working correctly',
            tests: {
              clientCreation: true,
              tokenDetails: true,
              restTTS: true,
              audioSize: audioBuffer.byteLength
            },
            tokenInfo: tokenDetails
          });
        } else {
          const errorText = await ttsResponse.text();
          console.error('❌ REST TTS failed:', ttsResponse.status, errorText);
          
          return NextResponse.json({
            status: 'PARTIAL_SUCCESS',
            message: 'Authentication works but TTS failed',
            tests: {
              clientCreation: true,
              tokenDetails: true,
              restTTS: false
            },
            ttsError: {
              status: ttsResponse.status,
              error: errorText
            },
            tokenInfo: tokenDetails
          });
        }

      } catch (ttsError: any) {
        console.error('❌ REST TTS request failed:', ttsError);
        
        return NextResponse.json({
          status: 'PARTIAL_SUCCESS',
          message: 'Authentication works but TTS request failed',
          tests: {
            clientCreation: true,
            tokenDetails: true,
            restTTS: false
          },
          ttsError: ttsError.message,
          tokenInfo: tokenDetails
        });
      }

    } catch (authError: any) {
      console.error('❌ Authentication test failed:', authError);
      
      return NextResponse.json({
        error: 'Authentication test failed',
        status: 'AUTH_TEST_FAILED',
        details: authError.message
      }, { status: 401 });
    }

  } catch (error: any) {
    console.error('❌ Deepgram test error:', error);
    return NextResponse.json({
      error: 'Deepgram test failed',
      status: 'TEST_FAILED',
      details: error.message
    }, { status: 500 });
  }
}
