import { NextRequest, NextResponse } from 'next/server';

// Global rate limiting to prevent 429 errors - optimized for performance
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 800; // 800ms between requests (reduced from 1500ms for better performance)

// Helper function to enforce rate limiting
async function enforceRateLimit(): Promise<{ shouldFallback: boolean; waitTime?: number }> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);

    // If wait time is too long, suggest fallback to REST API
    if (waitTime > 1000) {
      console.log(`Wait time ${waitTime}ms is too long for streaming, suggesting fallback to REST API`);
      return { shouldFallback: true, waitTime };
    }

    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = Date.now();
  return { shouldFallback: false };
}

// Function to split text into chunks for streaming
function chunkText(text: string, maxChunkSize: number = 150): string[] {
  // Split by sentences first
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const chunks: string[] = [];
  let currentChunk = '';

  for (const sentence of sentences) {
    const trimmedSentence = sentence.trim();
    if (!trimmedSentence) continue;

    // If adding this sentence would exceed max size, save current chunk and start new one
    if (currentChunk.length + trimmedSentence.length + 1 > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = trimmedSentence;
    } else {
      currentChunk += (currentChunk ? ' ' : '') + trimmedSentence;
    }
  }

  // Add the last chunk if it has content
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  // If no sentences were found, split by words as fallback
  if (chunks.length === 0) {
    const words = text.split(' ');
    let wordChunk = '';
    for (const word of words) {
      if (wordChunk.length + word.length + 1 > maxChunkSize && wordChunk.length > 0) {
        chunks.push(wordChunk.trim());
        wordChunk = word;
      } else {
        wordChunk += (wordChunk ? ' ' : '') + word;
      }
    }
    if (wordChunk.trim()) {
      chunks.push(wordChunk.trim());
    }
  }

  return chunks.length > 0 ? chunks : [text];
}

export async function POST(request: NextRequest) {
  try {
    const { text, voiceModel = 'aura-asteria-en' } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      console.error('DEEPGRAM_API_KEY environment variable not found');
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    console.log('Using Deepgram REST API for streaming TTS');
    console.log('Voice model:', voiceModel);
    console.log('Text length:', text.length);

    // Split text into chunks for streaming
    const chunks = chunkText(text, 150);
    console.log(`Split text into ${chunks.length} chunks:`, chunks.map(c => c.substring(0, 30) + '...'));

    // Create a streaming response using REST API
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        console.log('Starting REST-based streaming TTS...');

        // Send initial status
        const statusData = JSON.stringify({ type: 'status', message: 'connected' });
        controller.enqueue(encoder.encode(`data: ${statusData}\n\n`));

        try {
          // Process each chunk sequentially
          for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            console.log(`Processing chunk ${i + 1}/${chunks.length}: "${chunk.substring(0, 50)}..."`);

            try {
              // Enforce rate limiting to prevent 429 errors
              const rateLimitResult = await enforceRateLimit();

              // If rate limiting suggests fallback, return early with fallback suggestion
              if (rateLimitResult.shouldFallback) {
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                  error: 'RATE_LIMITED_USE_REST',
                  message: 'Rate limited, use REST API for faster response',
                  waitTime: rateLimitResult.waitTime
                })}\n\n`));
                controller.close();
                return;
              }

              // Make REST API call to Deepgram TTS with retry logic
              let ttsResponse: Response;
              let retryCount = 0;
              const maxRetries = 2;

              while (retryCount <= maxRetries) {
                // Note: Deepgram expects { text } as body and model/options as query params
                // Note: sample_rate parameter removed as it's not applicable for MP3 encoding and was causing 400 errors
                ttsResponse = await fetch(`https://api.deepgram.com/v1/speak?model=${voiceModel}&encoding=mp3&smart_format=true`, {
                  method: 'POST',
                  headers: {
                    'Authorization': `Token ${deepgramApiKey}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    text: chunk
                  })
                });

                // If successful or not a rate limit error, break out of retry loop
                if (ttsResponse.ok || ttsResponse.status !== 429) {
                  break;
                }

                // If rate limited and we have retries left, wait and try again
                if (ttsResponse.status === 429 && retryCount < maxRetries) {
                  const retryDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
                  console.log(`Rate limited (429) for chunk ${i + 1}, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries + 1})`);
                  await new Promise(resolve => setTimeout(resolve, retryDelay));
                  retryCount++;
                } else {
                  break;
                }
              }

              if (!ttsResponse.ok) {
                const errorText = await ttsResponse.text();
                console.error(`TTS API error for chunk ${i + 1}:`, ttsResponse.status, errorText);
                const errorData = JSON.stringify({
                  type: 'error',
                  message: `TTS API error: ${ttsResponse.status} ${errorText}`,
                  chunk: i + 1
                });
                controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
                continue;
              }

              // Get audio data as ArrayBuffer
              const audioBuffer = await ttsResponse.arrayBuffer();
              console.log(`Received audio for chunk ${i + 1}, size: ${audioBuffer.byteLength} bytes`);

              // Convert to base64 and send to client
              const base64Audio = Buffer.from(audioBuffer).toString('base64');
              const audioData = JSON.stringify({
                type: 'audio',
                data: base64Audio,
                timestamp: Date.now(),
                chunk: i + 1,
                totalChunks: chunks.length
              });
              controller.enqueue(encoder.encode(`data: ${audioData}\n\n`));

            } catch (chunkError: any) {
              console.error(`Error processing chunk ${i + 1}:`, chunkError);
              const errorData = JSON.stringify({
                type: 'error',
                message: `Chunk processing error: ${chunkError.message}`,
                chunk: i + 1
              });
              controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
            }
          }

          // Send completion message
          console.log('All chunks processed successfully');
          const closeData = JSON.stringify({ type: 'close', message: 'completed' });
          controller.enqueue(encoder.encode(`data: ${closeData}\n\n`));
          controller.close();

        } catch (error: any) {
          console.error('Error in streaming TTS processing:', error);
          const errorData = JSON.stringify({
            type: 'error',
            message: `Streaming error: ${error.message}`
          });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log('Stream cancelled');
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error: any) {
    console.error('Streaming TTS API error:', error);
    return NextResponse.json(
      { error: 'Failed to process streaming TTS request', details: error.message },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
