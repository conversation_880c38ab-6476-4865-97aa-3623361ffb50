-- Create webhooks table
CREATE TABLE IF NOT EXISTS webhooks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  description TEXT,
  createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set permissions
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON webhooks
  FOR SELECT USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON webhooks
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');