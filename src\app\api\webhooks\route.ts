import { NextResponse } from 'next/server'
import { createClient, createServiceClient } from '@/lib/supabase/server'

export async function GET() {
  // Use proper service client that handles missing env vars gracefully
  const supabase = createServiceClient()

  if (!supabase) {
    console.error('Failed to create service client - missing environment variables')
    return NextResponse.json({ error: 'Service configuration error' }, { status: 500 })
  }
  const { data, error } = await supabase.from('webhooks').select('*').order('createdat', { ascending: false })
  if (error) {
    console.error('Supabase error:', error)
    if (error.message.includes('relation "webhooks" does not exist')) {
      return NextResponse.json({ error: 'Webhooks table does not exist' }, { status: 404 })
    }
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
  return NextResponse.json(data)
}

export async function POST(request: Request) {
  // Use proper service client that handles missing env vars gracefully
  const supabase = createServiceClient()

  if (!supabase) {
    console.error('Failed to create service client - missing environment variables')
    return NextResponse.json({
      error: 'Service configuration error',
      details: 'Missing SUPABASE_SERVICE_ROLE_KEY environment variable'
    }, { status: 500 })
  }

  let body, name, url, description;

  try {
    body = await request.json()
    console.log('Webhook POST request body:', body)
    const extracted = body;
    name = extracted.name;
    url = extracted.url;
    description = extracted.description;

    // Enhanced validation with detailed logging
    if (!name || !url) {
      console.error('Webhook validation failed:', { name: !!name, url: !!url, body })
      return NextResponse.json({
        error: 'Name and url are required',
        details: { name: !!name, url: !!url }
      }, { status: 400 })
    }

    // Validate URL format
    try {
      new URL(url)
    } catch (urlError) {
      console.error('Invalid URL format:', url, urlError)
      return NextResponse.json({
        error: 'Invalid URL format. Please include http:// or https://',
        url: url
      }, { status: 400 })
    }
  } catch (parseError) {
    console.error('Failed to parse request body:', parseError)
    return NextResponse.json({
      error: 'Invalid JSON in request body',
      details: parseError.message
    }, { status: 400 })
  }

  // Check if a chat interface with the same name already exists
  console.log('Checking for existing chat interface with name:', name)
  const { data: existingChatInterfaces, error: chatInterfaceError } = await supabase
    .from('chat_interfaces')
    .select('id')
    .eq('name', name);

  if (chatInterfaceError) {
    console.error('Error checking chat interfaces:', chatInterfaceError)
    return NextResponse.json({
      error: 'Database error checking chat interfaces',
      details: chatInterfaceError.message
    }, { status: 500 });
  }

  if (existingChatInterfaces && existingChatInterfaces.length > 0) {
    console.log('Chat interface with same name exists:', existingChatInterfaces[0])
    return NextResponse.json({
      error: 'A chat interface with this name already exists. Please choose a different name.'
    }, { status: 400 });
  }

  // Check if another webhook with the same name exists
  console.log('Checking for existing webhook with name:', name)
  const { data: existingWebhooks, error: webhookError } = await supabase
    .from('webhooks')
    .select('id')
    .eq('name', name);

  if (webhookError) {
    console.error('Error checking webhooks:', webhookError)
    return NextResponse.json({
      error: 'Database error checking webhooks',
      details: webhookError.message
    }, { status: 500 });
  }

  if (existingWebhooks && existingWebhooks.length > 0) {
    console.log('Webhook with same name exists:', existingWebhooks[0])
    return NextResponse.json({
      error: 'Another webhook with this name already exists. Please choose a different name.'
    }, { status: 400 });
  }

  // Insert the webhook
  console.log('Inserting webhook:', { name, url, description })
  const insertData = {
    name,
    url,
    description,
    createdat: new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('webhooks')
    .insert([insertData])
    .select()

  if (error) {
    console.error('Supabase insert error:', error)
    if (error.message.includes('relation "webhooks" does not exist')) {
      return NextResponse.json({
        error: 'Webhooks table does not exist',
        details: 'Database table missing'
      }, { status: 404 })
    }
    if (error.message.includes('permission denied')) {
      return NextResponse.json({
        error: 'Permission denied - check table permissions',
        details: error.message
      }, { status: 403 })
    }
    return NextResponse.json({
      error: 'Database insert failed',
      details: error.message
    }, { status: 500 })
  }

  if (!data || data.length === 0) {
    console.error('No data returned from insert operation')
    return NextResponse.json({
      error: 'No data returned from insert',
      details: 'Insert succeeded but no data returned'
    }, { status: 500 })
  }

  console.log('Webhook created successfully:', data[0])
  return NextResponse.json(data)
}

export async function PUT(request: Request) {
  // Use proper service client that handles missing env vars gracefully
  const supabase = createServiceClient()

  if (!supabase) {
    console.error('Failed to create service client - missing environment variables')
    return NextResponse.json({
      error: 'Service configuration error',
      details: 'Missing SUPABASE_SERVICE_ROLE_KEY environment variable'
    }, { status: 500 })
  }

  let body, id, name, url, description;

  try {
    body = await request.json()
    console.log('Webhook PUT request body:', body)
    const extracted = body;
    id = extracted.id;
    name = extracted.name;
    url = extracted.url;
    description = extracted.description;

    // Enhanced validation with detailed logging
    if (!id || !name || !url) {
      console.error('Webhook update validation failed:', { id: !!id, name: !!name, url: !!url, body })
      return NextResponse.json({
        error: 'ID, name and url are required',
        details: { id: !!id, name: !!name, url: !!url }
      }, { status: 400 })
    }

    // Validate URL format
    try {
      new URL(url)
    } catch (urlError) {
      console.error('Invalid URL format:', url, urlError)
      return NextResponse.json({
        error: 'Invalid URL format. Please include http:// or https://',
        url: url
      }, { status: 400 })
    }

  } catch (parseError) {
    console.error('Failed to parse webhook update request body:', parseError)
    return NextResponse.json({
      error: 'Invalid JSON in request body',
      details: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
    }, { status: 400 })
  }

  // Check if webhook with same name exists (excluding current webhook)
  console.log('Checking for existing webhooks with name:', name, 'excluding ID:', id)
  const { data: existingWebhooks, error: checkError } = await supabase
    .from('webhooks')
    .select('id, name')
    .eq('name', name)
    .neq('id', id)

  if (checkError) {
    console.error('Error checking for existing webhooks:', checkError)
    return NextResponse.json({
      error: 'Database error while checking for existing webhooks',
      details: checkError.message
    }, { status: 500 })
  }

  if (existingWebhooks && existingWebhooks.length > 0) {
    console.log('Webhook with same name exists:', existingWebhooks[0])
    return NextResponse.json({
      error: 'Another webhook with this name already exists. Please choose a different name.'
    }, { status: 400 });
  }

  // Update the webhook
  console.log('Updating webhook:', { id, name, url, description })
  const updateData = {
    name,
    url,
    description
  }

  const { data, error } = await supabase
    .from('webhooks')
    .update(updateData)
    .eq('id', id)
    .select()

  if (error) {
    console.error('Supabase update error:', error)
    return NextResponse.json({
      error: 'Failed to update webhook',
      details: error.message
    }, { status: 500 })
  }

  if (!data || data.length === 0) {
    console.error('Webhook not found for update:', id)
    return NextResponse.json({
      error: 'Webhook not found'
    }, { status: 404 })
  }

  console.log('Webhook updated successfully:', data[0])
  return NextResponse.json(data[0])
}

export async function DELETE(request: Request) {
  // Use proper service client that handles missing env vars gracefully
  const supabase = createServiceClient()

  if (!supabase) {
    console.error('Failed to create service client - missing environment variables')
    return NextResponse.json({
      error: 'Service configuration error',
      details: 'Missing SUPABASE_SERVICE_ROLE_KEY environment variable'
    }, { status: 500 })
  }
  const { id } = await request.json();

  console.log('DELETE webhook request received for ID:', id); // Added logging

  if (!id) {
    console.error('Webhook ID is missing in DELETE request'); // Added logging
    return NextResponse.json({ error: 'Webhook ID is required' }, { status: 400 });
  }

  const { error, count } = await supabase.from('webhooks').delete({ count: 'exact' }).match({ id: id });

  console.log('Supabase delete operation for ID', id, 'affected rows:', count); // Added logging

  if (error) {
    console.error('Supabase delete error for ID', id, ':', error); // Added logging
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  if (count === 0) {
    console.warn('Supabase delete operation for ID', id, 'reported success but affected 0 rows. Webhook might not have existed.'); // Added logging
    // Depending on desired behavior, you might return a 404 here
  }

  console.log('Webhook deleted successfully for ID:', id); // Added logging
  return NextResponse.json({ message: 'Webhook deleted successfully' });
}