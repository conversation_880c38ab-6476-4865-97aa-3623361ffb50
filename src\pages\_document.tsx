import { Html, <PERSON>, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Force CSS reloading on production */}
        <meta httpEquiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta httpEquiv="Pragma" content="no-cache" />
        <meta httpEquiv="Expires" content="0" />

        {/*
          CSP is now set via HTTP headers in vercel.json
          X-Frame-Options is also set via HTTP headers
          These meta tags are removed to avoid conflicts with HTTP headers
        */}

        {/* Ensure backdrop-filter is supported */}
        <style dangerouslySetInnerHTML={{ __html: `
          @supports not (backdrop-filter: blur(20px)) {
            .glass-card, .glass-button {
              background: rgba(10, 10, 20, 0.9) !important;
            }
          }

          /* Ensure hover effects work */
          .glass-card:hover, .glass-card[data-hover="true"] {
            box-shadow: 0 0 12px var(--glow-color) !important;
            border-color: rgba(59, 130, 246, 0.5) !important;
          }

          .glass-button:hover, .glass-button[data-hover="true"] {
            box-shadow: 0 0 12px var(--glow-color) !important;
            border-color: rgba(59, 130, 246, 0.5) !important;
          }

          .button-glow-hover:hover, .button-glow-hover[data-hover="true"] {
            box-shadow: 0 0 8px var(--glow-color) !important;
          }

          .shadow-glow-blue:hover, .shadow-glow-blue[data-hover="true"] {
            box-shadow: 0 0 8px var(--glow-color) !important;
          }

          .ambient-glow:hover, .ambient-glow[data-hover="true"] {
            box-shadow: 0 0 12px var(--glow-color) !important;
            border-color: rgba(59, 130, 246, 0.5) !important;
          }

          /* Fix text colors on landing page */
          .tech-grid-bg h1,
          .tech-grid-bg h2,
          .tech-grid-bg h3,
          .tech-grid-bg p,
          .tech-grid-bg blockquote,
          .tech-grid-bg a:not(.glass-card.bg-neon-blue\\/20),
          .tech-grid-bg span,
          .tech-grid-bg div {
            color: white !important;
          }

          /* Keep the sign-up button blue */
          .glass-card.bg-neon-blue\\/20 {
            color: #3b82f6 !important;
          }

          /* Fix text-neon-blue class */
          .text-neon-blue {
            color: white !important;
          }

          /* Fix pricing button styling */
          .pricing-button .glass-card,
          .pricing-button button {
            width: 128px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            background: rgba(59, 130, 246, 0.1) !important;
            border: 1px solid rgba(59, 130, 246, 0.3) !important;
          }

          /* Style for disabled button */
          .pricing-button button[disabled],
          .pricing-button .glass-card[disabled] {
            background: rgba(59, 130, 246, 0.05) !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
            color: rgba(255, 255, 255, 0.7) !important;
            opacity: 0.7 !important;
            cursor: not-allowed !important;
          }

          /* Ensure all pricing buttons have the same variant styling */
          .pricing-button button {
            background: rgba(59, 130, 246, 0.1) !important;
            border: 1px solid rgba(59, 130, 246, 0.3) !important;
          }

          /* Fix chat interface text colors */
          .chat-interface p,
          .chat-interface span,
          .chat-interface div,
          .chat-interface * {
            color: inherit;
          }

          /* Specifically target chat bubbles to ensure text color is applied */
          .chat-interface [style*="color:"] > p,
          .chat-interface [style*="color:"] > span,
          .chat-interface [style*="color:"] > div {
            color: inherit !important;
          }

          /* Force user message bubbles to use the specified text color */
          .chat-interface div[style*="backgroundColor"] p,
          .chat-interface div[style*="backgroundColor"] span {
            color: inherit !important;
          }

          /* Ensure input fields in chat interface have visible text */
          .chat-interface input,
          .chat-interface textarea {
            color: black !important;
          }

          /* Dark mode input fields */
          .chat-interface .bg-gray-800 input,
          .chat-interface .bg-gray-800 textarea,
          .chat-interface .bg-gray-900 input,
          .chat-interface .bg-gray-900 textarea {
            color: white !important;
          }

          /* Style placeholder text for better visibility */
          .chat-interface input::placeholder,
          .chat-interface textarea::placeholder {
            color: #888888 !important;
            opacity: 1 !important;
          }

          .chat-interface .bg-gray-800 input::placeholder,
          .chat-interface .bg-gray-800 textarea::placeholder,
          .chat-interface .bg-gray-900 input::placeholder,
          .chat-interface .bg-gray-900 textarea::placeholder {
            color: #aaaaaa !important;
            opacity: 1 !important;
          }
        ` }} />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
