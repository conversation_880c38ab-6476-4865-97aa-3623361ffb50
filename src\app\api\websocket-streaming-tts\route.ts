import { NextRequest, NextResponse } from 'next/server';
import { processTextForNaturalSpeech } from '@/lib/natural-speech-utils';
import { createClient, LiveTTSEvents } from '@deepgram/sdk';
import { checkVoiceModelAccess } from '@/lib/middleware/tierMiddleware';
import { createClient as createSupabaseClient } from '@/lib/supabase/server';

const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;

export async function POST(request: NextRequest) {
  try {
    const {
      text,
      voiceModel = 'aura-2-thalia-en', // Female voice
      format = 'linear16',
      sampleRate = 24000,
      enableNaturalSpeech = true
    } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    if (!DEEPGRAM_API_KEY) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    // Check user authentication and tier permissions (optional for backward compatibility)
    const supabase = createSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    // If user is authenticated, check tier permissions
    if (user && !authError) {
      // Extract voice model name from full model string
      const voiceModelName = voiceModel.replace('aura-2-', '').replace('-en', '')

      // Check tier permissions for voice features
      const tierCheck = await checkVoiceModelAccess(user.id, voiceModelName)

      if (!tierCheck.allowed) {
        return NextResponse.json({
          error: 'Voice feature access denied',
          reason: tierCheck.reason,
          userTier: tierCheck.userTier,
          upgradeRequired: tierCheck.upgradeRequired,
          details: `Voice features require ${tierCheck.upgradeRequired} tier or higher.`
        }, { status: 403 })
      }
    }
    // If no user authentication, allow for backward compatibility with existing chat interfaces

    // IFRAME DETECTION: Check if request is from iframe context
    const referer = request.headers.get('referer') || '';
    const origin = request.headers.get('origin') || '';
    const userAgent = request.headers.get('user-agent') || '';
    const isIframeContext = referer.includes('/embed/') || referer.includes('/widget/') ||
                           origin !== request.headers.get('host');

    console.log(`🔍 Request context analysis:`, {
      referer: referer.substring(0, 100),
      origin,
      isIframeContext,
      textLength: text.length
    });

    console.log('🚀 WebSocket Streaming TTS - Ultra-fast audio start...');
    console.log('📝 Text length:', text.length);
    console.log('🎤 Voice model:', voiceModel);
    console.log('🧠 Natural speech processing:', enableNaturalSpeech ? 'ENABLED (parallel)' : 'DISABLED');

    // Create streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial status immediately
          const statusData = JSON.stringify({
            type: 'status',
            message: 'websocket_connecting',
            optimization: 'websocket_streaming'
          });
          controller.enqueue(encoder.encode(`data: ${statusData}\n\n`));

          let chunkNumber = 0;
          let processingResult: any = null;
          let streamClosed = false;
          let isStreamEnding = false; // CRITICAL FIX: Track when stream is ending
          let lastChunkTimeout: NodeJS.Timeout | null = null; // Timeout to detect final chunk

          // PRODUCTION-GRADE BUFFERING: Reliable audio buffer management
          let audioBuffer: Uint8Array[] = [];
          let bufferSize = 0;
          const TARGET_CHUNK_SIZE = 48000; // ~1 second of audio at 24kHz 16-bit
          const MIN_CHUNK_SIZE = 24000;    // ~0.5 seconds minimum
          const MAX_BUFFER_TIME = 1000;    // Max 1 second buffering delay

          // PRODUCTION: Unified state management with comprehensive monitoring
          let connectionState = {
            isOpen: false,
            isEnding: false,
            isClosed: false,
            chunksReceived: 0,
            lastChunkTime: 0,
            flushedEventReceived: false,
            forceCompleted: false,
            startTime: Date.now(),
            errors: [],
            retryCount: 0,
            totalBytesReceived: 0,
            averageChunkSize: 0
          };

          // PRODUCTION: Monitoring and error recovery
          let completionTimer: NodeJS.Timeout | null = null;
          let healthMonitorTimer: NodeJS.Timeout | null = null;
          let performanceMetrics = {
            firstChunkLatency: 0,
            totalProcessingTime: 0,
            bufferFlushCount: 0,
            errorCount: 0,
            completionMethod: 'unknown'
          };

          // PRODUCTION: Error logging and monitoring functions
          const logError = (error: any, context: string) => {
            const errorInfo = {
              timestamp: Date.now(),
              context,
              error: error.message || error,
              connectionState: { ...connectionState },
              performanceMetrics: { ...performanceMetrics }
            };
            connectionState.errors.push(errorInfo);
            console.error(`❌ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] ${context}:`, errorInfo);
          };

          const logPerformanceMetric = (metric: string, value: number, context?: string) => {
            console.log(`📊 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] ${metric}: ${value}ms${context ? ` (${context})` : ''}`);
          };

          const sendHealthStatus = (status: string, data?: any) => {
            const healthData = JSON.stringify({
              type: 'health_status',
              status,
              timestamp: Date.now(),
              connectionState: { ...connectionState },
              performanceMetrics: { ...performanceMetrics },
              context: isIframeContext ? 'iframe' : 'direct',
              ...data
            });
            if (!streamClosed) {
              controller.enqueue(encoder.encode(`data: ${healthData}\n\n`));
            }
          };

          // ULTRA-FAST: Start natural speech processing in parallel (completely non-blocking)
          let naturalSpeechPromise: Promise<any> | null = null;
          if (enableNaturalSpeech) {
            console.log('🧠 Starting natural speech processing in parallel (non-blocking)...');
            naturalSpeechPromise = processTextForNaturalSpeech(text);
          }

          // PRODUCTION: Reliable chunk sending with comprehensive monitoring
          const sendBufferedChunk = (audioData: Uint8Array, isLast: boolean = false) => {
            if (connectionState.isClosed || streamClosed) {
              logError('Attempted to send chunk after connection closed', 'sendBufferedChunk');
              return;
            }

            chunkNumber++;
            connectionState.chunksReceived = chunkNumber;
            connectionState.lastChunkTime = Date.now();
            connectionState.totalBytesReceived += audioData.length;
            connectionState.averageChunkSize = connectionState.totalBytesReceived / chunkNumber;

            // MONITORING: Track first chunk latency
            if (chunkNumber === 1) {
              performanceMetrics.firstChunkLatency = Date.now() - connectionState.startTime;
              logPerformanceMetric('First chunk latency', performanceMetrics.firstChunkLatency);
            }

            const durationMs = (audioData.length / 48000 * 1000).toFixed(0);
            console.log(`🎵 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Sending buffered audio chunk #${chunkNumber} (${audioData.length} bytes, ~${durationMs}ms)${isLast ? ' 🏁 FINAL CHUNK' : ''}`);

            try {
              const base64Data = Buffer.from(audioData).toString('base64');

              const audioMessage = JSON.stringify({
                type: 'audio',
                data: base64Data,
                encoding: format,
                sampleRate: sampleRate,
                chunkNumber: chunkNumber,
                totalChunks: isLast ? chunkNumber : undefined,
                isLastChunk: isLast,
                chunkSize: audioData.length,
                timestamp: Date.now(),
                source: 'websocket_streaming_buffered',
                // MONITORING: Add performance data
                firstChunkLatency: performanceMetrics.firstChunkLatency,
                averageChunkSize: Math.round(connectionState.averageChunkSize)
              });

              controller.enqueue(encoder.encode(`data: ${audioMessage}\n\n`));

              // RELIABILITY: Mark completion state if this is the final chunk
              if (isLast) {
                connectionState.forceCompleted = true;
                performanceMetrics.totalProcessingTime = Date.now() - connectionState.startTime;
                performanceMetrics.completionMethod = 'normal_completion';
                logPerformanceMetric('Total processing time', performanceMetrics.totalProcessingTime);
                console.log(`🏁 ✅ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] FINAL CHUNK #${chunkNumber} sent successfully with isLastChunk: true`);
                sendHealthStatus('completed', { totalChunks: chunkNumber, processingTime: performanceMetrics.totalProcessingTime });
              } else {
                console.log(`✅ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Sent buffered audio chunk #${chunkNumber}`);
              }

            } catch (error) {
              performanceMetrics.errorCount++;
              logError(error, 'sendBufferedChunk');
            }
          };

          // Function to flush current buffer
          const flushBuffer = (force: boolean = false, isLastChunk: boolean = false) => {
            if (audioBuffer.length === 0) return;

            const shouldFlush = force || bufferSize >= MIN_CHUNK_SIZE;

            if (shouldFlush) {
              // Concatenate all buffered audio
              const totalSize = audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0);
              const concatenated = new Uint8Array(totalSize);
              let offset = 0;

              for (const chunk of audioBuffer) {
                concatenated.set(chunk, offset);
                offset += chunk.length;
              }

              console.log(`🔄 Flushing buffer: ${audioBuffer.length} small chunks → 1 large chunk (${totalSize} bytes)${isLastChunk ? ' (FINAL CHUNK)' : ''}`);
              sendBufferedChunk(concatenated, isLastChunk);

              // Clear buffer
              audioBuffer = [];
              bufferSize = 0;
            }
          };

          // IMMEDIATE: Initialize Deepgram client and connect without waiting for natural speech
          const deepgram = createClient(DEEPGRAM_API_KEY);

          // Create WebSocket TTS connection with enhanced emotional expressiveness
          const dgConnection = deepgram.speak.live({
            model: voiceModel,
            encoding: format,
            sample_rate: sampleRate,
            bit_rate: 128000, // Higher bit rate for better voice quality and emotion
            smart_format: false // Disable smart format for more natural prosody
          });

          // PRODUCTION: Reliable connection open handling with monitoring
          dgConnection.on(LiveTTSEvents.Open, () => {
            connectionState.isOpen = true;
            connectionState.lastChunkTime = Date.now();
            console.log(`✅ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] WebSocket TTS connection opened`);

            // Send connection status with monitoring data
            sendHealthStatus('connected', { textLength: text.length });

            // IMMEDIATE AUDIO START: Send original text first for instant response
            console.log(`⚡ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Sending original text for immediate audio generation...`);
            dgConnection.sendText(text);
            dgConnection.flush();

            // PRODUCTION: Start health monitoring
            const startHealthMonitoring = () => {
              if (healthMonitorTimer) clearInterval(healthMonitorTimer);

              healthMonitorTimer = setInterval(() => {
                const timeSinceLastChunk = Date.now() - connectionState.lastChunkTime;
                const isStalled = timeSinceLastChunk > 5000 && !connectionState.forceCompleted; // 5s stall detection

                if (isStalled) {
                  console.warn(`⚠️ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Connection stalled - ${timeSinceLastChunk}ms since last chunk`);
                  sendHealthStatus('stalled', { timeSinceLastChunk, chunksReceived: connectionState.chunksReceived });

                  // RECOVERY: Force completion if stalled too long
                  if (timeSinceLastChunk > 10000 && audioBuffer.length > 0) {
                    console.log(`🔄 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Auto-recovery: Forcing completion due to stall`);
                    connectionState.isEnding = true;
                    performanceMetrics.completionMethod = 'auto_recovery_stall';
                    flushBuffer(true, true);
                  }
                }
              }, 2000); // Check every 2 seconds
            };

            // RELIABILITY: Start completion monitoring
            const startCompletionMonitoring = () => {
              if (completionTimer) clearTimeout(completionTimer);

              const monitoringTimeout = isIframeContext ? 3000 : 2000; // 3s for iframe, 2s for direct
              completionTimer = setTimeout(() => {
                if (!connectionState.forceCompleted && !connectionState.isClosed && audioBuffer.length > 0) {
                  console.log(`⏰ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Completion monitoring triggered - forcing final buffer flush`);
                  connectionState.isEnding = true;
                  performanceMetrics.completionMethod = 'timeout_completion';
                  flushBuffer(true, true);
                }
              }, monitoringTimeout);
            };

            // Start monitoring systems
            startHealthMonitoring();
            setTimeout(startCompletionMonitoring, 1000);
          });

          // PRODUCTION: Reliable stream completion detection
          dgConnection.on(LiveTTSEvents.Flushed, () => {
            connectionState.flushedEventReceived = true;
            connectionState.isEnding = true;
            console.log(`🏁 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Deepgram stream flushed - marking stream as ending`);

            // Clear completion timer since we got the official flush event
            if (completionTimer) {
              clearTimeout(completionTimer);
              completionTimer = null;
            }

            // IMMEDIATE: Flush any remaining buffer as the final chunk
            if (audioBuffer.length > 0) {
              console.log(`🔄 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Flushing final buffer after stream flushed...`);
              flushBuffer(true, true); // Force flush and mark as final chunk
            } else {
              console.log(`🏁 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Stream flushed but no buffer to flush - final chunk already sent`);
            }
          });

          // PRODUCTION: Reliable audio data handling with unified state
          dgConnection.on(LiveTTSEvents.Audio, (audioData: any) => {
            if (connectionState.isClosed || streamClosed) return;

            const audioArray = new Uint8Array(audioData);
            connectionState.lastChunkTime = Date.now();
            console.log(`🎵 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Received small WebSocket audio chunk (${audioArray.length} bytes) - buffering...`);

            try {
              // Add to buffer
              audioBuffer.push(audioArray);
              bufferSize += audioArray.length;

              // RELIABILITY: Simple buffer flushing based on size, not complex timeouts
              const shouldFlush = bufferSize >= TARGET_CHUNK_SIZE || connectionState.isEnding;

              if (shouldFlush) {
                flushBuffer(false, connectionState.isEnding);
              }

            } catch (error) {
              console.error('❌ Error buffering audio chunk:', error);
            }
          });

          // PRODUCTION: Reliable connection close handling with cleanup
          dgConnection.on(LiveTTSEvents.Close, async () => {
            connectionState.isClosed = true;
            performanceMetrics.totalProcessingTime = Date.now() - connectionState.startTime;
            console.log(`🔚 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] WebSocket TTS connection closed after ${chunkNumber} chunks`);

            // CLEANUP: Clean up all monitoring timers
            if (completionTimer) {
              clearTimeout(completionTimer);
              completionTimer = null;
            }
            if (healthMonitorTimer) {
              clearInterval(healthMonitorTimer);
              healthMonitorTimer = null;
            }

            // GUARANTEED COMPLETION: Always flush remaining buffer as final chunk on close
            if (audioBuffer.length > 0 && !connectionState.forceCompleted) {
              console.log(`🔄 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] FORCE: Flushing final buffer on close (${audioBuffer.length} chunks, ${bufferSize} bytes)...`);
              connectionState.isEnding = true;
              flushBuffer(true, true); // Force flush remaining audio and mark as last chunk
              console.log(`✅ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] FINAL CHUNK sent with isLastChunk: true on connection close`);
            } else {
              console.log(`ℹ️ [${isIframeContext ? 'IFRAME' : 'DIRECT'}] No remaining audio buffer to flush (completed: ${connectionState.forceCompleted})`);

              // RELIABILITY: Always send a final completion marker if we have chunks
              if (chunkNumber > 0 && !connectionState.forceCompleted) {
                console.log(`📤 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Sending final completion marker...`);
                const finalMarker = JSON.stringify({
                  type: 'audio_complete',
                  message: 'All audio chunks sent',
                  totalChunks: chunkNumber,
                  isLastChunk: true,
                  flushedEventReceived: connectionState.flushedEventReceived,
                  timestamp: Date.now()
                });
                controller.enqueue(encoder.encode(`data: ${finalMarker}\n\n`));
              }
            }

            // Wait for natural speech processing to complete for metadata
            if (naturalSpeechPromise) {
              try {
                processingResult = await naturalSpeechPromise;
                console.log('🧠 Natural speech processing completed for metadata');
              } catch (error) {
                console.log('⚠️ Natural speech processing failed:', error);
              }
            }

            if (!streamClosed) {
              // Send final status with natural speech metadata
              const closeMessage = JSON.stringify({
                type: 'close',
                message: 'WebSocket streaming completed',
                totalChunks: chunkNumber,
                naturalSpeechProcessed: processingResult?.processed || false,
                tokensUsed: processingResult?.tokensUsed || 0,
                originalLength: text.length,
                processedLength: processingResult?.processedText?.length || text.length,
                timestamp: Date.now()
              });

              controller.enqueue(encoder.encode(`data: ${closeMessage}\n\n`));
              controller.close();
              streamClosed = true;
              console.log('✅ WebSocket streaming completed');
            }
          });

          // PRODUCTION: Enhanced error handling with recovery
          dgConnection.on(LiveTTSEvents.Error, (error: any) => {
            performanceMetrics.errorCount++;
            logError(error, 'WebSocket TTS error');

            // RECOVERY: Attempt to flush any remaining buffer before closing
            if (audioBuffer.length > 0 && !connectionState.forceCompleted) {
              console.log(`🔄 [${isIframeContext ? 'IFRAME' : 'DIRECT'}] Error recovery: Flushing remaining buffer`);
              connectionState.isEnding = true;
              performanceMetrics.completionMethod = 'error_recovery';
              flushBuffer(true, true);
            }

            if (!streamClosed) {
              sendHealthStatus('error', {
                error: error.message || error,
                errorCount: performanceMetrics.errorCount,
                chunksReceived: connectionState.chunksReceived,
                recoveryAttempted: audioBuffer.length > 0
              });

              const errorMessage = JSON.stringify({
                type: 'error',
                message: `WebSocket TTS error: ${error}`,
                timestamp: Date.now(),
                context: isIframeContext ? 'iframe' : 'direct',
                connectionState: { ...connectionState },
                performanceMetrics: { ...performanceMetrics }
              });
              controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
              controller.close();
              streamClosed = true;
            }
          });

          // Handle natural speech processing completion (for enhanced audio)
          if (naturalSpeechPromise) {
            naturalSpeechPromise.then((result) => {
              if (result.processed && !streamClosed) {
                console.log('🎭 Natural speech processing completed, sending enhanced text...');
                console.log('📝 Enhanced text:', result.processedText.substring(0, 100) + '...');
                
                // Send enhanced text for better quality (this will be additional audio)
                // The original text already started playing for immediate response
                dgConnection.sendText(result.processedText);
                dgConnection.flush();
                
                processingResult = result;
              }
            }).catch((error) => {
              console.log('⚠️ Natural speech processing error:', error);
            });
          }

          // IFRAME FIX: Extended timeout for iframe contexts
          const timeoutDuration = isIframeContext ? 60000 : 30000; // 60s for iframe, 30s for direct
          console.log(`⏰ Setting WebSocket timeout: ${timeoutDuration/1000}s (iframe: ${isIframeContext})`);

          setTimeout(() => {
            if (!streamClosed) {
              console.log(`⏰ WebSocket timeout after ${timeoutDuration/1000}s (iframe: ${isIframeContext}), closing connection`);
              console.log(`📊 Final stats: ${chunkNumber} chunks sent, streamEnding: ${isStreamEnding}`);
              try {
                dgConnection.requestClose();
              } catch (error) {
                console.log('⚠️ Error closing WebSocket connection:', error);
              }
            }
          }, timeoutDuration);

        } catch (error) {
          console.error('❌ Error in WebSocket streaming setup:', error);
          
          const errorMessage = JSON.stringify({
            type: 'error',
            message: `WebSocket setup error: ${error}`,
            timestamp: Date.now()
          });
          controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log('🚀 WebSocket streaming cancelled by client');
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Accel-Buffering': 'no',
        'X-TTS-Engine': 'deepgram-websocket',
        'X-TTS-Version': '3.0.0',
        'X-Optimization': 'websocket-streaming'
      },
    });

  } catch (error: any) {
    console.error('❌ WebSocket TTS request failed:', error);
    
    return NextResponse.json(
      {
        error: 'WebSocket TTS request failed',
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}
