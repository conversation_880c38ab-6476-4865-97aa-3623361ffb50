import React, { useRef, useState, useImperativeHandle, forwardRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DeepgramSpeechToTextProps, 
  DeepgramSpeechToTextRef,
  DeepgramSTTConfig,
  STTErrorCode 
} from '@/types/deepgram-stt';

const DeepgramSpeechToText = forwardRef<DeepgramSpeechToTextRef, DeepgramSpeechToTextProps>(({
  onTranscript,
  disabled = false,
  color = '#3b82f6',
  onMicrophoneActiveChange,
  autoSend = false,
  onSend
}, ref) => {
  const [isListening, setIsListening] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for managing connections and audio
  const websocketRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const configRef = useRef<DeepgramSTTConfig | null>(null);
  const finalTranscriptRef = useRef<string>('');
  const hasReceivedSpeechRef = useRef<boolean>(false);

  // CRITICAL FIX: Use ref for onMicrophoneActiveChange to prevent useCallback dependency issues
  const onMicrophoneActiveChangeRef = useRef(onMicrophoneActiveChange);
  const isMountedRef = useRef(true);

  // Update ref when prop changes
  useEffect(() => {
    onMicrophoneActiveChangeRef.current = onMicrophoneActiveChange;
  }, [onMicrophoneActiveChange]);

  // Set mounted flag
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Get Deepgram configuration from backend
  const getDeepgramConfig = useCallback(async () => {
    try {
      const response = await fetch('/api/deepgram-stt');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('[DeepgramSTT] Failed to get configuration:', error);
      throw error;
    }
  }, []);

  // Initialize WebSocket connection to Deepgram
  const initializeWebSocket = useCallback(async (config: DeepgramSTTConfig, apiKey: string) => {
    return new Promise<WebSocket>((resolve, reject) => {
      try {
        const params = new URLSearchParams({
          model: config.model,
          language: config.language,
          smart_format: config.smart_format.toString(),
          interim_results: config.interim_results.toString(),
          endpointing: config.endpointing.toString(),
          vad_events: config.vad_events.toString(),
          punctuate: config.punctuate?.toString() || 'true',
          numerals: config.numerals?.toString() || 'true',
          utterance_end_ms: '2000' // Wait 2 seconds for utterance end detection
        });

        const websocketUrl = `wss://api.deepgram.com/v1/listen?${params.toString()}`;

        console.log('[DeepgramSTT] Connecting to Deepgram...');

        // Use WebSocket subprotocol for authentication (browser-compatible method)
        const ws = new WebSocket(websocketUrl, ['token', apiKey]);

        ws.onopen = () => {
          console.log('[DeepgramSTT] WebSocket connected');
          resolve(ws);
        };

        ws.onmessage = (event) => {
          try {
            const response = JSON.parse(event.data);
            console.log('[DeepgramSTT] Received:', response);

            if (response.channel?.alternatives?.[0]) {
              const alternative = response.channel.alternatives[0];
              const transcript = alternative.transcript;
              const isFinal = response.is_final || false;

              console.log('[DeepgramSTT] Transcript:', transcript, 'isFinal:', isFinal);

              // Track if we've received actual speech content
              if (transcript.trim()) {
                hasReceivedSpeechRef.current = true;
                console.log('[DeepgramSTT] Speech detected, enabling auto-stop on speech_final');
              }

              // Call onTranscript with the current transcript
              onTranscript(transcript);

              // Handle final results for auto-send
              if (isFinal && transcript.trim()) {
                finalTranscriptRef.current = transcript;

                if (autoSend && onSend) {
                  console.log('[DeepgramSTT] Auto-sending message:', transcript);
                  setTimeout(() => {
                    onSend(transcript);
                  }, 100);
                }
              }
            }

            // Handle speech_final (end of speech) - only if we've received actual speech
            if (response.speech_final && hasReceivedSpeechRef.current) {
              console.log('[DeepgramSTT] Speech final detected after receiving speech - stopping recognition');
              setTimeout(() => {
                stopRecognition();
              }, 500);
            } else if (response.speech_final) {
              console.log('[DeepgramSTT] Speech final detected but no speech received yet - ignoring');
            }

            // Handle UtteranceEnd for better end-of-speech detection
            if (response.type === 'UtteranceEnd' && hasReceivedSpeechRef.current) {
              console.log('[DeepgramSTT] Utterance end detected after receiving speech - stopping recognition');
              setTimeout(() => {
                stopRecognition();
              }, 300);
            }

          } catch (error) {
            console.error('[DeepgramSTT] Message parsing error:', error);
          }
        };

        ws.onerror = (error) => {
          console.error('[DeepgramSTT] WebSocket error:', error);
          reject(new Error('WebSocket connection failed'));
        };

        ws.onclose = (event) => {
          console.log('[DeepgramSTT] WebSocket closed:', event.code, event.reason);
          setIsListening(false);
          if (onMicrophoneActiveChangeRef.current) {
            onMicrophoneActiveChangeRef.current(false);
          }
        };

      } catch (error) {
        console.error('[DeepgramSTT] WebSocket initialization error:', error);
        reject(error);
      }
    });
  }, [onTranscript, autoSend, onSend]); // Removed onMicrophoneActiveChange dependency

  // Start audio capture and recognition
  const startRecognition = useCallback(async () => {
    if (isListening || isConnecting || disabled) return;

    try {
      setIsConnecting(true);
      setError(null);
      finalTranscriptRef.current = '';
      hasReceivedSpeechRef.current = false; // Reset speech tracking

      console.log('[DeepgramSTT] Starting recognition...');

      // Get configuration from backend
      const { config, api_key } = await getDeepgramConfig();
      configRef.current = config;

      // Initialize WebSocket connection
      const ws = await initializeWebSocket(config, api_key);
      websocketRef.current = ws;

      // Get microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      mediaStreamRef.current = stream;

      // Create MediaRecorder for audio capture
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      mediaRecorderRef.current = mediaRecorder;

      // Handle audio data
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && ws.readyState === WebSocket.OPEN) {
          // Convert blob to array buffer and send to Deepgram
          event.data.arrayBuffer().then(buffer => {
            ws.send(buffer);
          });
        }
      };

      // Start recording with small chunks for real-time streaming
      mediaRecorder.start(100); // 100ms chunks

      setIsListening(true);
      setIsConnecting(false);

      if (onMicrophoneActiveChangeRef.current) {
        onMicrophoneActiveChangeRef.current(true);
      }

      console.log('[DeepgramSTT] Recognition started successfully');

    } catch (error) {
      console.error('[DeepgramSTT] Start recognition error:', error);
      setError(error instanceof Error ? error.message : 'Failed to start recognition');
      setIsConnecting(false);
      setIsListening(false);

      if (onMicrophoneActiveChangeRef.current) {
        onMicrophoneActiveChangeRef.current(false);
      }
    }
  }, [isListening, isConnecting, disabled, getDeepgramConfig, initializeWebSocket]); // Removed onMicrophoneActiveChange dependency

  // Stop audio capture and recognition
  // CRITICAL FIX: Remove onMicrophoneActiveChange dependency to prevent useEffect cleanup loops
  const stopRecognition = useCallback(() => {
    console.log('[DeepgramSTT] Stopping recognition...');

    // Stop media recorder
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // Stop media stream
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    // Close WebSocket
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      websocketRef.current.close();
    }

    setIsListening(false);
    setIsConnecting(false);

    // Use ref instead of prop to avoid dependency issues
    if (onMicrophoneActiveChangeRef.current) {
      onMicrophoneActiveChangeRef.current(false);
    }

    console.log('[DeepgramSTT] Recognition stopped');
  }, []); // CRITICAL: Empty dependency array to prevent recreation

  // Expose ref methods (same as original SpeechToText)
  useImperativeHandle(ref, () => ({
    resetMicrophoneActive: stopRecognition
  }), [stopRecognition]);

  // Cleanup on unmount only - CRITICAL FIX: Remove stopRecognition dependency to prevent cleanup loops
  useEffect(() => {
    return () => {
      // Only cleanup if component is actually unmounting, not just re-rendering
      if (!isMountedRef.current) {
        console.log('[DeepgramSTT] Component unmounting, cleaning up recognition');
        stopRecognition();
      } else {
        console.log('[DeepgramSTT] Component re-rendering, skipping cleanup to prevent premature stop');
      }
    };
  }, []); // CRITICAL: Empty dependency array to prevent cleanup on every render

  return (
    <Button
      type="button"
      onClick={isListening ? stopRecognition : startRecognition}
      disabled={disabled || isConnecting}
      style={{
        backgroundColor: isListening ? '#ef4444' : color,
        minWidth: '36px',
        width: '36px',
        height: '36px',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      title={
        isConnecting 
          ? 'Connecting...' 
          : isListening 
            ? 'Tap to stop listening' 
            : 'Tap to speak'
      }
      aria-label={
        isConnecting 
          ? 'Connecting to speech recognition' 
          : isListening 
            ? 'Stop listening' 
            : 'Start speech recognition'
      }
    >
      {isConnecting ? (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          {isListening ? (
            <rect x="6" y="6" width="12" height="12" rx="2" />
          ) : (
            <>
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
              <line x1="12" y1="19" x2="12" y2="22" />
            </>
          )}
        </svg>
      )}
    </Button>
  );
});

DeepgramSpeechToText.displayName = 'DeepgramSpeechToText';

export default DeepgramSpeechToText;
