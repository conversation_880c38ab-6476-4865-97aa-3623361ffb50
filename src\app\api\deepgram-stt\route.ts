import { NextRequest, NextResponse } from 'next/server';
import {
  DeepgramSTTConfig,
  RateLimitInfo,
  RateLimitResult
} from '@/types/deepgram-stt';

// Rate limiting
const rateLimitMap = new Map<string, RateLimitInfo>();

// Configuration from environment
const getSTTConfig = (): DeepgramSTTConfig => ({
  model: (process.env.DEEPGRAM_STT_MODEL as 'nova-2' | 'nova-3') || 'nova-2',
  language: process.env.DEEPGRAM_STT_LANGUAGE || 'en-US',
  smart_format: process.env.DEEPGRAM_STT_SMART_FORMAT === 'true',
  interim_results: process.env.DEEPGRAM_STT_INTERIM_RESULTS === 'true',
  endpointing: parseInt(process.env.DEEPGRAM_STT_ENDPOINTING || '10'),
  vad_events: process.env.DEEPGRAM_STT_VAD_EVENTS === 'true',
  punctuate: true,
  numerals: true
});

// Rate limiting
const checkRateLimit = (clientId: string): RateLimitResult => {
  const limit = parseInt(process.env.STT_RATE_LIMIT_PER_MINUTE || '100');
  const windowSize = 60 * 1000; // 1 minute
  const now = Date.now();
  
  const rateLimitInfo = rateLimitMap.get(clientId) || {
    requests: 0,
    windowStart: now,
    windowSize,
    limit
  };

  // Reset window if expired
  if (now - rateLimitInfo.windowStart > windowSize) {
    rateLimitInfo.requests = 0;
    rateLimitInfo.windowStart = now;
  }

  const allowed = rateLimitInfo.requests < limit;
  if (allowed) {
    rateLimitInfo.requests++;
  }

  rateLimitMap.set(clientId, rateLimitInfo);

  return {
    allowed,
    remaining: Math.max(0, limit - rateLimitInfo.requests),
    resetTime: rateLimitInfo.windowStart + windowSize
  };
};



export async function GET(request: NextRequest) {
  // Check if STT is enabled
  if (process.env.DEEPGRAM_STT_ENABLED !== 'true') {
    return NextResponse.json({ error: 'Deepgram STT is disabled' }, { status: 503 });
  }

  // Check if Deepgram API key is available
  const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
  if (!deepgramApiKey) {
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  // Get client IP for rate limiting
  const clientIP = request.headers.get('x-forwarded-for') ||
                   request.headers.get('x-real-ip') ||
                   'unknown';

  // Check rate limit
  const rateLimitResult = checkRateLimit(clientIP);
  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      { error: 'Rate limit exceeded' },
      {
        status: 429,
        headers: {
          'X-RateLimit-Limit': process.env.STT_RATE_LIMIT_PER_MINUTE || '100',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      }
    );
  }

  // Return configuration for client-side connection
  const config = getSTTConfig();
  const websocketUrl = createDeepgramWebSocketURL(config);

  return NextResponse.json({
    enabled: true,
    config,
    websocket_url: websocketUrl,
    api_key: deepgramApiKey,
    rate_limit: {
      remaining: rateLimitResult.remaining,
      reset_time: rateLimitResult.resetTime
    }
  });
}

// Create Deepgram WebSocket URL with parameters
export function createDeepgramWebSocketURL(config: DeepgramSTTConfig): string {
  const params = new URLSearchParams({
    model: config.model,
    language: config.language,
    smart_format: config.smart_format.toString(),
    interim_results: config.interim_results.toString(),
    endpointing: config.endpointing.toString(),
    vad_events: config.vad_events.toString(),
    punctuate: config.punctuate?.toString() || 'true',
    numerals: config.numerals?.toString() || 'true'
  });

  if (config.keyterm && config.keyterm.length > 0) {
    config.keyterm.forEach(term => params.append('keyterm', term));
  }

  return `wss://api.deepgram.com/v1/listen?${params.toString()}`;
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Upgrade, Connection, Sec-WebSocket-Key, Sec-WebSocket-Version, Sec-WebSocket-Protocol',
      'Access-Control-Max-Age': '86400'
    }
  });
}
