import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting database initialization...')

    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    const results = []

    // 1. Check if user_profiles table exists, if not create it via direct table operations
    console.log('📋 Checking/Creating user_profiles table...')

    // Try to query the table first to see if it exists
    const { data: tableCheck, error: tableCheckError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1)

    if (tableCheckError && tableCheckError.code === '42P01') {
      // Table doesn't exist, we need to create it
      console.log('❌ user_profiles table does not exist')
      results.push({
        step: 'check_user_profiles_table',
        success: false,
        error: 'user_profiles table does not exist - needs manual database migration',
        solution: 'Run the migration script or create the table manually in Supabase dashboard'
      })
    } else if (tableCheckError) {
      console.error('❌ Error checking user_profiles table:', tableCheckError)
      results.push({
        step: 'check_user_profiles_table',
        success: false,
        error: tableCheckError.message
      })
    } else {
      console.log('✅ user_profiles table exists')
      results.push({
        step: 'check_user_profiles_table',
        success: true
      })
    }

    // 2. Skip trigger creation for now (requires SQL execution privileges)
    console.log('⏭️ Skipping trigger creation (requires manual setup)')
    results.push({
      step: 'create_trigger',
      success: true,
      note: 'Trigger creation skipped - will be handled manually'
    })

    // 3. Create profiles for existing users (only if table exists)
    const tableExists = results.find(r => r.step === 'check_user_profiles_table')?.success

    if (tableExists) {
      console.log('👥 Creating profiles for existing users...')

      // Get all existing users
      const { data: users, error: usersError } = await supabase.auth.admin.listUsers()

      if (usersError) {
        console.error('❌ Error fetching users:', usersError)
        results.push({
          step: 'fetch_existing_users',
          success: false,
          error: usersError.message
        })
      } else {
        console.log(`📊 Found ${users.users.length} existing users`)

        for (const user of users.users) {
          try {
            // Set admin user to Pro tier, others to Free
            const tier = user.email === '<EMAIL>' ? 'pro' : 'free'
            const now = new Date().toISOString()
            const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()

            const { error: profileError } = await supabase
              .from('user_profiles')
              .upsert({
                id: user.id,
                tier,
                subscription_status: 'active',
                subscription_start_date: now,
                subscription_end_date: tier === 'free' ? null : oneYearFromNow,
                created_at: now,
                updated_at: now
              })

            if (profileError) {
              console.error(`❌ Error creating profile for ${user.email}:`, profileError)
              results.push({
                step: `create_profile_${user.email}`,
                success: false,
                error: profileError.message
              })
            } else {
              console.log(`✅ Profile created for ${user.email} with ${tier} tier`)
              results.push({
                step: `create_profile_${user.email}`,
                success: true,
                tier,
                email: user.email
              })
            }
          } catch (error) {
            console.error(`❌ Error processing user ${user.email}:`, error)
            results.push({
              step: `create_profile_${user.email}`,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
          }
        }
      }
    } else {
      console.log('⏭️ Skipping user profile creation - table does not exist')
      results.push({
        step: 'create_user_profiles',
        success: false,
        error: 'Cannot create profiles - user_profiles table does not exist'
      })
    }

    // 4. Verify table exists and has data
    console.log('🔍 Verifying database setup...')
    const { data: verifyData, error: verifyError } = await supabase
      .from('user_profiles')
      .select('id, tier, subscription_status')
      .limit(5)

    if (verifyError) {
      console.error('❌ Error verifying table:', verifyError)
      results.push({
        step: 'verify_table',
        success: false,
        error: verifyError.message
      })
    } else {
      console.log(`✅ Table verified with ${verifyData.length} profiles`)
      results.push({
        step: 'verify_table',
        success: true,
        profileCount: verifyData.length,
        sampleProfiles: verifyData
      })
    }

    const successCount = results.filter(r => r.success).length
    const totalSteps = results.length

    return NextResponse.json({
      message: 'Database initialization completed',
      success: successCount === totalSteps,
      results,
      summary: {
        total: totalSteps,
        successful: successCount,
        failed: totalSteps - successCount
      }
    })
  } catch (error) {
    console.error('💥 Error in database initialization:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check database status
export async function GET() {
  try {
    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    // Check if user_profiles table exists
    const { data: statusCheck, error: statusError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1)

    const status = {
      tableExists: !statusError,
      tableError: statusError?.message || null,
      profileCount: 0,
      adminProfileExists: false
    }

    if (!statusError) {
      // Get profile count
      const { count } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })

      status.profileCount = count || 0

      // Check if admin profile exists
      const { data: adminProfile } = await supabase
        .from('user_profiles')
        .select('tier')
        .eq('id', (await supabase.auth.admin.listUsers()).data.users.find(u => u.email === '<EMAIL>')?.id || '')
        .single()

      status.adminProfileExists = !!adminProfile
    }

    return NextResponse.json({
      message: 'Database status check',
      status
    })
  } catch (error) {
    console.error('Error checking database status:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
