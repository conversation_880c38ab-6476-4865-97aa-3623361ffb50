'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard'

export function ClientOnlyScriptTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [scriptCode, setScriptCode] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Fetch the embed code from the API
    const fetchEmbedCode = async () => {
      try {
        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/generate-embed-code?chatId=${chatId}&t=${timestamp}`, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        if (response.ok) {
          const data = await response.json();
          setScriptCode(data.mobileOptimizedEmbedCode);
        } else {
          console.error('Failed to fetch embed code');
          // Fallback to a basic embed code
          const siteOrigin = window.location.origin;
          setScriptCode(`<!-- BotFusion Chat Widget -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" type="text/javascript">
(function(w, d, s, o, f, js, fjs) {
    w['BotFusion-Widget'] = o;
    w[o] = w[o] || function() {
        (w[o].q = w[o].q || []).push(arguments);
    };
    js = d.createElement(s);
    fjs = d.getElementsByTagName(s)[0];
    js.id = o;
    js.src = f;
    js.async = 1;
    fjs.parentNode.insertBefore(js, fjs);
}(window, document, 'script', 'bf', '${siteOrigin}/api/embed-script'));

bf('init', {
    chatId: "${chatId}",
    position: 'right',
    primaryColor: "#3b82f6",
    userBubbleColor: "#ffffff",
    botBubbleColor: "#3b82f6",
    userTextColor: "#000000",
    botTextColor: "#ffffff",
    logoUrl: "",
    darkMode: false,
    greeting: "Hello! How can I help you today?"
});
</script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' ${siteOrigin};
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  connect-src 'self' ${siteOrigin};
  frame-src 'self' ${siteOrigin};
  frame-ancestors 'self' ${siteOrigin};
">
-->`);
        }
      } catch (error) {
        console.error('Error fetching embed code:', error);
        // Use fallback code here too
        const siteOrigin = window.location.origin;
        setScriptCode(`<!-- BotFusion Chat Widget -->
<!-- Add this script tag at the end of your body section -->
<script nonce="REPLACE_WITH_YOUR_NONCE" type="text/javascript">
(function(w, d, s, o, f, js, fjs) {
    w['BotFusion-Widget'] = o;
    w[o] = w[o] || function() {
        (w[o].q = w[o].q || []).push(arguments);
    };
    js = d.createElement(s);
    fjs = d.getElementsByTagName(s)[0];
    js.id = o;
    js.src = f;
    js.async = 1;
    fjs.parentNode.insertBefore(js, fjs);
}(window, document, 'script', 'bf', '${siteOrigin}/api/embed-script'));

bf('init', {
    chatId: "${chatId}",
    position: 'right',
    primaryColor: "#3b82f6",
    userBubbleColor: "#ffffff",
    botBubbleColor: "#3b82f6",
    userTextColor: "#000000",
    botTextColor: "#ffffff",
    logoUrl: "",
    darkMode: false,
    greeting: "Hello! How can I help you today?"
});
</script>

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' ${siteOrigin};
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  connect-src 'self' ${siteOrigin};
  frame-src 'self' ${siteOrigin};
  frame-ancestors 'self' ${siteOrigin};
">
-->`);
      }
    };

    fetchEmbedCode();
  }, [chatId, chatName])

  const { isCopied: isScriptCopied, copyToClipboard: copyScript } = useCopyToClipboard(2000)

  const copyToClipboard = (text: string, type: string) => {
    copyScript(text, `${type} code copied to clipboard!`)
  }

  if (!isClient) {
    return <div className="p-4 text-gray-400">Loading script embed code...</div>
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-neon-blue mb-2">Script Embed Code</h3>
      <div className="relative">
        <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
          {scriptCode}
        </pre>
        <Button
          onClick={() => copyToClipboard(scriptCode, 'Script')}
          className="absolute top-2 right-2 glass-card hover:shadow-glow-blue"
          size="sm"
        >
          {isScriptCopied ? 'COPIED' : 'Copy'}
        </Button>
      </div>
      <p className="text-sm text-gray-400 mt-3">
        <strong>🚀 Mobile-Optimized Script Embed</strong>
        <br /><br />
        <strong>✅ FIXES:</strong> Mobile scrolling issues, touch events, button blocking, navigation problems
        <br />
        <strong>📱 MOBILE:</strong> Full-screen on mobile, desktop-sized on desktop/tablet
        <br />
        <strong>🌐 UNIVERSAL:</strong> Works on WordPress, Durable.co, Shopify, custom sites
        <br /><br />
        You can customize the appearance by changing the configuration variables at the top:
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li><code className="bg-gray-700 px-1 py-0.5 rounded text-xs">BOTFUSION_CHAT_ID</code> - Your chat interface ID (automatically set)</li>
          <li><code className="bg-gray-700 px-1 py-0.5 rounded text-xs">BOTFUSION_BASE_URL</code> - Your site URL (automatically set)</li>
          <li><code className="bg-gray-700 px-1 py-0.5 rounded text-xs">BOTFUSION_PRIMARY_COLOR</code> - Main color for the chat button</li>
          <li><code className="bg-gray-700 px-1 py-0.5 rounded text-xs">BOTFUSION_DEBUG</code> - Set to true for debugging console logs</li>
        </ul>
        <br />
        <strong>📋 How to use:</strong> Copy the entire script and paste it before the closing &lt;/body&gt; tag in your HTML. The widget will automatically load and create a floating chat button.
        <br /><br />
        <strong>🔒 CSP Issues?</strong> If your site has strict Content Security Policy that blocks scripts, use the iframe tab instead.
      </p>
    </div>
  )
}

export function ClientOnlyIframeTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [iframeCode, setIframeCode] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const siteOrigin = window.location.origin
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime()
    const embedUrl = `${siteOrigin}/embed/${chatId}?t=${timestamp}`

    // Generate the iframe embed code with button-only mode for minimal footprint
    const buttonOnlyUrl = `${embedUrl}${embedUrl.includes('?') ? '&' : '?'}mode=button-only`;
    setIframeCode(`<iframe
  src="${buttonOnlyUrl}"
  width="80px"
  height="80px"
  style="border: none; background: transparent; position: fixed; bottom: 20px; right: 20px; z-index: 9999;"
  allow="microphone"
  title="${chatName} Chat Button"
  sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
  loading="lazy"
  importance="high"
  referrerpolicy="origin"
  credentialless=""
  fetchpriority="high"
></iframe>

<!-- IMPORTANT: This iframe shows only the chat button. -->
<!-- When clicked, it will open the full chat in a new window/tab. -->
<!-- For a complete embedded chat experience, use the Widget tab instead. -->

<!-- IMPORTANT: If your site uses Content Security Policy (CSP), add these directives: -->
<!--
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  frame-src 'self' ${siteOrigin};
  frame-ancestors 'self' ${siteOrigin};
">
-->`)
  }, [chatId, chatName])

  const { isCopied: isIframeCopied, copyToClipboard: copyIframe } = useCopyToClipboard(2000)

  const copyToClipboard = (text: string, type: string) => {
    copyIframe(text, `${type} code copied to clipboard!`)
  }

  if (!isClient) {
    return <div className="p-4 text-gray-400">Loading iframe embed code...</div>
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-neon-blue mb-2">iframe Embed Code</h3>
      <div className="relative">
        <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
          {iframeCode}
        </pre>
        <Button
          onClick={() => copyToClipboard(iframeCode, 'iframe')}
          className="absolute top-2 right-2 glass-card hover:shadow-glow-blue"
          size="sm"
        >
          {isIframeCopied ? 'COPIED' : 'Copy'}
        </Button>
      </div>
      <p className="text-sm text-gray-400 mt-3">
        <strong>⚠️ FIXED:</strong> This iframe now shows only a small chat button (80x80px) to eliminate the white container issue.
        <br /><br />
        <strong>How it works:</strong> The iframe displays just the chat button. When users click it, the full chat opens in a new window/tab.
        <br /><br />
        <strong>For embedded chat:</strong> Use the &quot;Widget&quot; tab instead for a complete embedded chat experience that opens within your page.
      </p>
    </div>
  )
}



export function ClientOnlyLinksTab({ chatId }: { chatId: string }) {
  const [origin, setOrigin] = useState('')
  const [embedUrl, setEmbedUrl] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const siteOrigin = window.location.origin
    setOrigin(siteOrigin)

    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime()
    // Generate the embed URL
    setEmbedUrl(`${siteOrigin}/embed/${chatId}?t=${timestamp}`)
  }, [chatId])

  const { isCopied: isChatUrlCopied, copyToClipboard: copyChatUrl } = useCopyToClipboard(2000)
  const { isCopied: isEmbedUrlCopied, copyToClipboard: copyEmbedUrl } = useCopyToClipboard(2000)

  const copyToClipboard = (text: string, type: string, urlType: 'chat' | 'embed') => {
    if (urlType === 'chat') {
      copyChatUrl(text, `${type} copied to clipboard!`)
    } else if (urlType === 'embed') {
      copyEmbedUrl(text, `${type} copied to clipboard!`)
    }
  }

  if (!isClient) {
    return <div className="p-4 text-gray-400">Loading direct links...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-neon-blue mb-2">Full Page Chat URL</h3>
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={`${origin}/chat/${chatId}`}
            readOnly
            className="flex-1 bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
          />
          <Button
            onClick={() => copyToClipboard(`${origin}/chat/${chatId}`, 'Full Page Chat URL', 'chat')}
            className="glass-card hover:shadow-glow-blue"
          >
            {isChatUrlCopied ? 'COPIED' : 'Copy'}
          </Button>
        </div>
        <p className="text-sm text-gray-400 mt-1">
          Share this URL directly with users to access the full-page chat interface in a browser.
        </p>
      </div>

      <div>
        <h3 className="text-lg font-medium text-neon-blue mb-2">Widget Embed URL</h3>
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={embedUrl}
            readOnly
            className="flex-1 bg-gray-800 border border-gray-700 rounded-md p-2 text-white"
          />
          <Button
            onClick={() => copyToClipboard(embedUrl, 'Widget Embed URL', 'embed')}
            className="glass-card hover:shadow-glow-blue"
          >
            {isEmbedUrlCopied ? 'COPIED' : 'Copy'}
          </Button>
        </div>
        <p className="text-sm text-gray-400 mt-1">
          Direct URL to the embeddable chat widget. Use this in iframes or embed codes.
        </p>
      </div>


    </div>
  )
}
