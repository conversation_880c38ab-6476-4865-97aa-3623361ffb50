'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { toast } from 'sonner'

interface WebhookEditModalProps {
  webhook: {
    id: string
    name: string
    url: string
    description?: string
  }
  onClose: () => void
  onUpdate: (updatedWebhook: any) => void
}

export default function WebhookEditModal({ webhook, onClose, onUpdate }: WebhookEditModalProps) {
  const [name, setName] = useState(webhook.name)
  const [url, setUrl] = useState(webhook.url)
  const [description, setDescription] = useState(webhook.description || '')
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testCount, setTestCount] = useState(0)
  const [lastTestResult, setLastTestResult] = useState<{
    success: boolean
    message: string
    timestamp: string
  } | null>(null)

  const handleTest = async () => {
    try {
      if (!url) {
        toast.error('Please enter a webhook URL')
        return
      }

      try {
        new URL(url) // Validate URL format
      } catch {
        toast.error('Please enter a valid URL (include http:// or https://)')
        return
      }

      setIsTesting(true)
      setTestCount(prev => prev + 1)

      console.log('🧪 Starting webhook test...')
      console.log('🔗 URL:', url)
      console.log('📦 Payload:', {
        message: "Test message from BotFusion webhook test",
        chatId: "webhook-test",
        sessionId: `test-${Date.now()}`
      })

      // Use toast.promise for better visual feedback
      await toast.promise(
        fetch(url, {
          method: 'POST',
          mode: 'cors',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: "Test message from BotFusion webhook test",
            chatId: "webhook-test",
            sessionId: `test-${Date.now()}`
          })
        }).then(async (response) => {
          if (!response.ok) {
            let errorMessage = `Test failed with status ${response.status}`
            try {
              const errorData = await response.json()
              if (errorData.message) {
                errorMessage += `: ${errorData.message}`
              } else if (errorData.error) {
                errorMessage += `: ${errorData.error}`
              }
            } catch {
              // If we can't parse JSON, try to get text
              try {
                const errorText = await response.text()
                if (errorText) {
                  errorMessage += `: ${errorText.substring(0, 100)}`
                }
              } catch {
                errorMessage += ': No error details available'
              }
            }
            throw new Error(errorMessage)
          }

          // Try to get response data for additional confirmation
          let responseData = null
          try {
            responseData = await response.json()
            console.log('Webhook test response:', responseData)
          } catch {
            // If response isn't JSON, that's still okay for a successful test
            console.log('Webhook test successful (non-JSON response)')
          }

          // Set visual confirmation
          setLastTestResult({
            success: true,
            message: responseData?.output || 'Webhook test successful!',
            timestamp: new Date().toLocaleTimeString()
          })

          return response
        }),
        {
          loading: 'Testing webhook...',
          success: 'Webhook test successful! ✅',
          error: (error) => {
            const errorMsg = error instanceof Error ? error.message : 'Webhook test failed'
            console.error('Webhook test failed:', errorMsg)

            // Set visual error confirmation
            setLastTestResult({
              success: false,
              message: errorMsg,
              timestamp: new Date().toLocaleTimeString()
            })

            return `❌ ${errorMsg}`
          }
        }
      )
    } catch (error) {
      // Error is already handled by toast.promise
      console.error('Webhook test error:', error)
    } finally {
      setIsTesting(false)
    }
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!name || !url) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      setIsSaving(true)
      
      const response = await fetch('/api/webhooks', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: webhook.id,
          name,
          url,
          description
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update webhook')
      }

      const updatedWebhook = await response.json()
      onUpdate(updatedWebhook)
      toast.success('Webhook updated successfully')
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to update webhook')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="bg-gray-900 p-6 rounded-lg max-w-md w-full shadow-xl border border-gray-800">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-neon-blue">Edit Webhook</h2>
          <Button 
            variant="ghost" 
            onClick={onClose} 
            className="text-gray-400 hover:bg-gray-800 rounded-full p-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </Button>
        </div>

        <form onSubmit={handleSave} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-neon-blue">
              Webhook Name *
            </label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter webhook name"
              required
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-neon-blue">
              Webhook URL *
            </label>
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://your-webhook-endpoint.com"
              required
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-neon-blue">
              Description (Optional)
            </label>
            <Input
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe this webhook..."
              className="bg-gray-800 border-gray-700 text-white"
            />
          </div>

          {testCount > 0 && (
            <div className="text-xs text-gray-400 bg-gray-800 p-2 rounded">
              Tests performed: {testCount} | Check browser console for detailed logs
            </div>
          )}

          {lastTestResult && (
            <div className={`p-3 rounded-lg border ${
              lastTestResult.success
                ? 'bg-green-900/20 border-green-500/30 text-green-400'
                : 'bg-red-900/20 border-red-500/30 text-red-400'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {lastTestResult.success ? (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span className="font-medium">
                    {lastTestResult.success ? 'Test Successful' : 'Test Failed'}
                  </span>
                </div>
                <span className="text-xs opacity-70">{lastTestResult.timestamp}</span>
              </div>
              <div className="mt-2 text-sm">
                {lastTestResult.message}
              </div>
            </div>
          )}

          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleTest}
              disabled={isTesting || !url}
              className="flex-1 hover:bg-green-500/10 hover:border-green-500 hover:text-green-400"
            >
              {isTesting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Testing...
                </>
              ) : (
                <>
                  <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Test
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSaving}
              className="flex-1 bg-neon-blue hover:bg-neon-blue/80"
            >
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Save
                </>
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  )
}
