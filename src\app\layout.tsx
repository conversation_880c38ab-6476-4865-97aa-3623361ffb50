import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "BotFusion X",
  description: "Generate and customize chat interfaces connected to n8n workflows via webhooks.",
};

import HydrationErrorBoundary from '@/components/HydrationErrorBoundary'
import HoverEffectsProvider from '@/components/HoverEffectsProvider'

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <HydrationErrorBoundary>
          <HoverEffectsProvider />
          {children}
        </HydrationErrorBoundary>
      </body>
    </html>
  );
}
