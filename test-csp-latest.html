<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Latest CSP Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
    <!-- Simulate a strict CSP -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app 'unsafe-inline'; connect-src 'self' https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app; frame-src 'self' https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app; frame-ancestors 'self';">
</head>
<body>
    <h1>Latest CSP Embed Test</h1>

    <div class="note">
        <strong>Important:</strong> This page tests the CSP-friendly embed code with a strict Content Security Policy.
        Please open your browser's developer console (F12 or right-click > Inspect > Console)
        to check for any errors during the script loading process.
    </div>

    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget and check for any errors in the console.</p>

        <!-- BotFusion Chat Widget - CSP-Friendly Version -->
        <script nonce="test-nonce" src="https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app/api/csp-embed-script?chatId=1"></script>
    </div>

    <!-- Error Tracking -->
    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });

        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all scripts and resources');

            // Log all iframes on the page
            const iframes = document.querySelectorAll('iframe');
            console.log(`Found ${iframes.length} iframes on the page:`);
            iframes.forEach((iframe, index) => {
                console.log(`Iframe #${index + 1}:`, {
                    id: iframe.id,
                    src: iframe.src,
                    width: iframe.style.width,
                    height: iframe.style.height
                });
            });
        });
    </script>
</body>
</html>
