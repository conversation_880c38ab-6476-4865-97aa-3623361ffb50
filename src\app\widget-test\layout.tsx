import { Metadata } from 'next'
import { <PERSON>ei<PERSON> } from 'next/font/google'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Widget Test',
  description: 'BotFusion X Widget Test',
}

// Add custom headers to allow embedding
export const headers = () => {
  return [
    {
      key: 'Content-Security-Policy',
      value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src *; frame-ancestors *; font-src 'self' data:;",
    },
    {
      key: 'X-Frame-Options',
      value: 'ALLOWALL',
    },
    {
      key: 'Access-Control-Allow-Origin',
      value: '*',
    },
    {
      key: 'Access-Control-Allow-Methods',
      value: 'GET, POST, OPTIONS',
    },
    {
      key: 'Access-Control-Allow-Headers',
      value: 'Content-Type, Authorization, X-Requested-With, Accept',
    },
    {
      key: 'Cross-Origin-Embedder-Policy',
      value: 'unsafe-none',
    },
    {
      key: 'Cross-Origin-Opener-Policy',
      value: 'unsafe-none',
    },
    {
      key: 'Cross-Origin-Resource-Policy',
      value: 'cross-origin',
    },
  ]
}

export default function WidgetTestLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Add CSP meta tag to allow embedding */}
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src *; frame-ancestors *; font-src 'self' data:;"
        />
        <meta httpEquiv="X-Frame-Options" content="ALLOWALL" />
      </head>
      <body className={`${geistSans.variable} antialiased`}>
        {children}
      </body>
    </html>
  )
}
