import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId')
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
    }

    // Get the origin for the API
    const origin = process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'

    // Get the chat interface details from the database
    const supabase = createServiceClient()
    const { data, error } = await supabase
      .from('chat_interfaces')
      .select('*')
      .eq('id', chatId)
      .single()

    if (error) {
      console.error('Error fetching chat interface:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!data) {
      return NextResponse.json({ error: 'Chat interface not found' }, { status: 404 })
    }

    // Get parameters from the query string or use defaults from the database
    const primaryColor = request.nextUrl.searchParams.get('primaryColor') || data.primary_color || '#3b82f6';
    const userBubbleColor = request.nextUrl.searchParams.get('userBubbleColor') || data.user_bubble_color || '#ffffff';
    const botBubbleColor = request.nextUrl.searchParams.get('botBubbleColor') || data.bot_bubble_color || '#3b82f6';
    const userTextColor = request.nextUrl.searchParams.get('userTextColor') || data.user_text_color || '#000000';
    const botTextColor = request.nextUrl.searchParams.get('botTextColor') || data.bot_text_color || '#ffffff';
    const logoUrl = request.nextUrl.searchParams.get('logoUrl') || data.logo_url || '';
    const darkMode = request.nextUrl.searchParams.get('darkMode') === 'true' || data.dark_mode || false;
    const greeting = request.nextUrl.searchParams.get('greeting') || data.welcome_message || 'Hello! How can I help you today?';

    // Generate the HTML for the iframe embed
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BotFusion Chat Widget</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      overflow: hidden;
      background-color: transparent;
    }

    /* This route no longer creates buttons - the main embed interface handles all UI */

    /* For small screens */
    @media (max-width: 480px) {
      .chat-window {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
      }
    }
  </style>
</head>
<body>
  <!-- This route is deprecated - use the main embed interface which has built-in toggle functionality -->
  <div style="display: none;">This embed method is no longer supported. Please use the main embed interface.</div>
</body>
</html>`;

    // Return the HTML with proper headers
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': "default-src 'self'; script-src 'unsafe-inline'; style-src 'unsafe-inline'; img-src data:; connect-src 'self' https:; frame-src *; frame-ancestors *; font-src data:;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      }
    })
  } catch (error) {
    console.error('Error serving HTML embed:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'X-Frame-Options': 'ALLOWALL',
      'Content-Security-Policy': "default-src 'self'; script-src 'unsafe-inline'; style-src 'unsafe-inline'; img-src data:; connect-src 'self' https:; frame-src *; frame-ancestors *; font-src data:;",
    },
  });
}
