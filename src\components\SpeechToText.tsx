import React, { useRef, useState, useImperative<PERSON>andle, forwardRef } from 'react';
import { Button } from '@/components/ui/button';

export interface SpeechToTextRef {
  resetMicrophoneActive: () => void;
}

interface SpeechToTextProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
  color?: string;
  onMicrophoneActiveChange?: (active: boolean) => void;
  autoSend?: boolean;
  onSend?: () => void;
}

const SpeechToText = forwardRef<SpeechToTextRef, SpeechToTextProps>(({
  onTranscript,
  disabled = false,
  color = '#3b82f6',
  onMicrophoneActiveChange,
  autoSend = false,
  onSend
}, ref) => {
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<any>(null);

  const startRecognition = () => {
    if (typeof window === 'undefined') return;
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('SpeechRecognition not supported in this browser.');
      return;
    }
    if (recognitionRef.current) {
      recognitionRef.current.abort();
      recognitionRef.current = null;
    }
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US';
    recognition.continuous = false;
    recognition.interimResults = true;
    console.log('[SpeechToText] Starting recognition');
    recognition.onstart = () => {
      console.log('[SpeechToText] onstart');
      setIsListening(true);
      if (onMicrophoneActiveChange) {
        onMicrophoneActiveChange(true);
      }
    };
    recognition.onaudiostart = () => { console.log('[SpeechToText] onaudiostart'); };
    recognition.onspeechstart = () => { console.log('[SpeechToText] onspeechstart'); };
    recognition.onsoundstart = () => { console.log('[SpeechToText] onsoundstart'); };
    recognition.onsoundend = () => { console.log('[SpeechToText] onsoundend'); };
    recognition.onaudioend = () => { console.log('[SpeechToText] onaudioend'); };
    recognition.onend = () => {
      console.log('[SpeechToText] onend');
      setIsListening(false);
      if (onMicrophoneActiveChange) {
        onMicrophoneActiveChange(false);
      }
    };
    recognition.onresult = (event: any) => {
      console.log('[SpeechToText] onresult', event);
      let finalTranscript = '';
      let isFinal = false;

      for (let i = event.resultIndex; i < event.results.length; ++i) {
        finalTranscript += event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          isFinal = true;
        }
      }

      console.log('[SpeechToText] Final transcript:', finalTranscript, 'isFinal:', isFinal);
      console.log('[DEBUG] Auto-send conditions:', {autoSend, isFinal, hasTranscript: !!finalTranscript.trim(), hasOnSend: !!onSend});
      onTranscript(finalTranscript);

      // Auto-send if enabled and we have a final result
      if (autoSend && isFinal && finalTranscript.trim() && onSend) {
        console.log('[SpeechToText] Auto-sending message with transcript:', `"${finalTranscript}"`);
        setTimeout(() => {
          console.log('[DEBUG] Auto-send timeout triggered, calling onSend() with transcript');
          onSend(finalTranscript); // Pass transcript directly to avoid race condition
        }, 100); // Small delay to ensure transcript is set
      } else {
        console.log('[DEBUG] Auto-send conditions not met, skipping auto-send');
      }
    };
    recognition.onerror = (event: any) => {
      console.error('[SpeechToText] onerror', event);
      setIsListening(false);
      if (onMicrophoneActiveChange) {
        onMicrophoneActiveChange(false);
      }
    };
    recognition.onnomatch = (event: any) => { console.log('[SpeechToText] onnomatch', event); };
    recognitionRef.current = recognition;
    recognition.start();
  };

  const stopRecognition = () => {
    if (recognitionRef.current) {
      console.log('[SpeechToText] Stopping recognition');
      recognitionRef.current.stop();
      setIsListening(false);
      if (onMicrophoneActiveChange) {
        onMicrophoneActiveChange(false);
      }
    }
  };

  React.useImperativeHandle(ref, () => ({
    resetMicrophoneActive: stopRecognition
  }));

  React.useEffect(() => {
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
        recognitionRef.current = null;
      }
    };
  }, []);

  return (
    <Button
      type="button"
      onClick={isListening ? stopRecognition : startRecognition}
      disabled={disabled}
      style={{
        backgroundColor: isListening ? '#ef4444' : color,
        minWidth: '36px',
        width: '36px',
        height: '36px',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      title={isListening ? 'Tap to stop listening' : 'Tap to speak'}
      aria-label={isListening ? 'Stop listening' : 'Start speech recognition'}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        {isListening ? (
          <rect x="6" y="6" width="12" height="12" rx="2" />
        ) : (
          <>
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
            <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
            <line x1="12" y1="19" x2="12" y2="22" />
          </>
        )}
      </svg>
    </Button>
  );
});

export default SpeechToText;
