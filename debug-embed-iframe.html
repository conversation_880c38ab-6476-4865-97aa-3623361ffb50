<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Embed (Iframe)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
        .debug-info pre {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Debug Embed (Iframe)</h1>

    <div class="important">
        <strong>Important:</strong> This page is for debugging the iframe embed code. Please open the browser console (F12) to see any errors.
    </div>

    <div class="content">
        <h2>Iframe Embed</h2>
        <p>This test uses the iframe embed approach:</p>
        <pre>&lt;iframe src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/simple-embed.html?chatId=test-chat-id" style="position: fixed; bottom: 20px; right: 20px; width: 70px; height: 70px; border: none; z-index: 9999; box-shadow: none !important; filter: none !important; background-color: transparent;" scrolling="no" allow="microphone"&gt;&lt;/iframe&gt;</pre>
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Iframe Embed -->
    <iframe src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/simple-embed.html?chatId=test-chat-id"
            style="position: fixed; bottom: 20px; right: 20px; width: 70px; height: 70px; border: none; z-index: 9999; box-shadow: none !important; filter: none !important; background-color: transparent;"
            scrolling="no"
            allow="microphone"></iframe>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        (function() {
            var oldConsoleLog = console.log;
            var oldConsoleError = console.error;
            var oldConsoleWarn = console.warn;
            var consoleOutput = document.getElementById('console-output');

            function appendToOutput(type, args) {
                var message = Array.from(args).map(arg => {
                    if (typeof arg === 'object') {
                        try {
                            return JSON.stringify(arg);
                        } catch (e) {
                            return String(arg);
                        }
                    }
                    return String(arg);
                }).join(' ');

                var pre = document.createElement('pre');
                pre.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
                pre.textContent = type.toUpperCase() + ': ' + message;
                consoleOutput.appendChild(pre);
            }

            console.log = function() {
                oldConsoleLog.apply(console, arguments);
                appendToOutput('log', arguments);
            };

            console.error = function() {
                oldConsoleError.apply(console, arguments);
                appendToOutput('error', arguments);
            };

            console.warn = function() {
                oldConsoleWarn.apply(console, arguments);
                appendToOutput('warn', arguments);
            };

            // Log any errors
            window.addEventListener('error', function(event) {
                appendToOutput('error', [event.message + ' at ' + event.filename + ':' + event.lineno]);
            });
        })();
    </script>
</body>
</html>
