'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

export default function SetProPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const setProTier = async () => {
    if (!email) {
      toast.error('Please enter your email')
      return
    }

    try {
      setLoading(true)
      setResult(null)

      const response = await fetch('/api/public/set-pro-tier', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to set Pro tier')
      }

      setResult(data)
      toast.success(data.message)
    } catch (error) {
      console.error('Error setting Pro tier:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to set Pro tier')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🚀 Set Pro Tier
          </h1>
          <p className="text-xl text-gray-300">
            Upgrade your account to Pro tier instantly
          </p>
        </div>

        {/* Set Pro Form */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Upgrade to Pro</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Your Email Address
              </label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="bg-white/20 border-gray-600 text-white placeholder-gray-400"
              />
            </div>

            <Button
              onClick={setProTier}
              disabled={loading || !email}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
            >
              {loading ? 'Setting Pro Tier...' : '🎯 Set Pro Tier'}
            </Button>
          </div>
        </Card>

        {/* Result */}
        {result && (
          <Card className="bg-green-500/20 border-green-500 p-6">
            <h2 className="text-xl font-bold text-white mb-4">✅ Success!</h2>
            <div className="space-y-2 text-white">
              <p><strong>Message:</strong> {result.message}</p>
              <p><strong>Email:</strong> {result.email}</p>
              <p><strong>User ID:</strong> {result.userId}</p>
              {result.previousTier && (
                <p><strong>Previous Tier:</strong> {result.previousTier}</p>
              )}
              <p><strong>New Tier:</strong> {result.newTier}</p>
            </div>
          </Card>
        )}

        {/* Pro Features */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">💎 Pro Tier Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-white">🎤 Voice Features</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• 6 Premium Voice Models</li>
                <li>• High-Quality TTS</li>
                <li>• Advanced Voice Options</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-white">🚀 Advanced Features</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Unlimited Chat Interfaces</li>
                <li>• White Labeling</li>
                <li>• Custom Branding</li>
                <li>• Analytics Dashboard</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <div className="flex gap-4 justify-center">
            <Button
              onClick={() => window.open('/dashboard', '_blank')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              🏠 Go to Dashboard
            </Button>
            <Button
              onClick={() => window.open('/admin/set-tier', '_blank')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              ⚙️ Admin Panel
            </Button>
          </div>
        </Card>

        {/* Instructions */}
        <Card className="bg-yellow-500/20 border-yellow-500 p-6">
          <h2 className="text-xl font-bold text-white mb-4">📋 Instructions</h2>
          <div className="text-white space-y-2">
            <p>1. Enter the email address associated with your account</p>
            <p>2. Click "Set Pro Tier" to upgrade instantly</p>
            <p>3. Your voice features will work immediately</p>
            <p>4. No authentication required - this is a direct database update</p>
          </div>
        </Card>
      </div>
    </div>
  )
}
