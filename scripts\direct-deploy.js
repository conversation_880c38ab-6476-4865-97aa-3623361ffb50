// direct-deploy.js
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to run a command and return its output
function runCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8' });
    return { success: true, output };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Main function
async function main() {
  console.log('Starting direct deployment to Vercel...');
  
  // Step 1: Check if Vercel CLI is installed
  const vercelCheck = runCommand('vercel --version');
  if (!vercelCheck.success) {
    console.log('Vercel CLI is not installed. Installing...');
    const npmInstall = runCommand('npm install -g vercel');
    if (!npmInstall.success) {
      console.error('Failed to install Vercel CLI:', npmInstall.error);
      return;
    }
  }
  
  // Step 2: Set git user configuration
  console.log('Setting git user configuration...');
  runCommand('git config user.name "Tellivision"');
  runCommand('git config user.email "<EMAIL>"');
  
  // Step 3: Commit any changes
  console.log('Committing any changes...');
  const gitStatus = runCommand('git status --porcelain');
  if (gitStatus.output.trim()) {
    runCommand('git add .');
    runCommand('git commit -m "Update for Vercel deployment"');
    runCommand('git push origin main');
  }
  
  // Step 4: Deploy directly to Vercel
  console.log('Deploying to Vercel...');
  console.log('This will open a browser window for authentication if needed.');
  console.log('Please follow the instructions in the browser.');
  
  // Deploy with production flag and link to the GitHub repo
  const deployCommand = 'vercel --prod';
  const deployResult = runCommand(deployCommand);
  
  if (deployResult.success) {
    console.log('Deployment successful!');
    console.log(deployResult.output);
  } else {
    console.error('Deployment failed:', deployResult.error);
  }
}

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
});
