import { NextRequest, NextResponse } from 'next/server';
import { buildDeepgramTTSUrl, DEEPGRAM_HEADERS } from '@/lib/tts-config-standard';
// CRITICAL FIX: Remove Deepgram SDK dependency and use direct HTTP approach like working REST TTS

// CRITICAL FIX: Use same rate limiting as working REST TTS
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 800; // Same as working REST TTS

// Helper function to enforce rate limiting (same as working REST TTS)
async function enforceRateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = Date.now();
}

export async function POST(request: NextRequest) {
  try {
    const { text, voiceModel = 'aura-2-thalia-en' } = await request.json(); // Changed to female voice

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    console.log('🚀 CRITICAL FIX: Using proven REST TTS approach with streaming response...');
    console.log('🎤 Voice model:', voiceModel);
    console.log('📝 Text length:', text.length);

    // Enforce rate limiting (same as working REST TTS)
    await enforceRateLimit();

    // CRITICAL FIX: Use proven working REST TTS approach with streaming response
    console.log('🔑 Deepgram API key found (first 10 chars):', deepgramApiKey.substring(0, 10) + '...');

    // Create streaming response that uses working REST TTS method
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        console.log('🔌 FIXED: Using proven REST TTS approach with streaming...');

        // Send initial status
        const statusData = JSON.stringify({ type: 'status', message: 'connecting' });
        controller.enqueue(encoder.encode(`data: ${statusData}\n\n`));

        try {
          // CRITICAL FIX: Use proven working REST TTS approach instead of SDK WebSocket
          console.log('🔧 FIXED: Using proven REST TTS API call...');

          // Prepare payload exactly like working REST TTS
          const payload = {
            text: text
          };

          // Send connected status
          const connectedData = JSON.stringify({ type: 'status', message: 'connected' });
          controller.enqueue(encoder.encode(`data: ${connectedData}\n\n`));

          // Call Deepgram REST API with standardized configuration
          console.log('📤 Making REST TTS API call with standardized configuration...');
          const response = await fetch(buildDeepgramTTSUrl(voiceModel, 'mp3'), {
            method: 'POST',
            headers: {
              ...DEEPGRAM_HEADERS,
              'Authorization': `Token ${deepgramApiKey}`,
              'Accept': 'audio/mpeg'
            },
            body: JSON.stringify(payload)
          });

          console.log('📥 REST TTS Response status:', response.status);

          if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ REST TTS API error:', response.status, errorText);

            const errorData = JSON.stringify({
              type: 'error',
              message: `REST TTS API error: ${response.status} - ${errorText}`
            });
            controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
            controller.close();
            return;
          }

          // Get audio data from REST API response
          console.log('📥 Getting audio buffer from Deepgram response...');
          const audioBuffer = await response.arrayBuffer();
          console.log('🎵 FIXED: Received audio buffer, size:', audioBuffer.byteLength, 'bytes');

          if (audioBuffer.byteLength === 0) {
            const errorData = JSON.stringify({
              type: 'error',
              message: 'Empty audio response from Deepgram'
            });
            controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
            controller.close();
            return;
          }

          // CRITICAL FIX: Stream the audio in chunks to simulate real-time streaming
          const CHUNK_SIZE = 8192; // 8KB chunks for smooth streaming
          const audioArray = new Uint8Array(audioBuffer);
          let chunkNumber = 0;

          // STACK-SAFE BASE64 ENCODING HELPER FUNCTION
          function uint8ArrayToBase64(uint8Array: Uint8Array): string {
            let binaryString = '';
            for (let i = 0; i < uint8Array.length; i++) {
              binaryString += String.fromCharCode(uint8Array[i]);
            }
            return btoa(binaryString);
          }

          for (let i = 0; i < audioArray.length; i += CHUNK_SIZE) {
            const chunk = audioArray.slice(i, i + CHUNK_SIZE);
            const base64Audio = uint8ArrayToBase64(chunk); // FIXED: Stack-safe base64 encoding
            chunkNumber++;

            const audioChunkData = JSON.stringify({
              type: 'audio',
              data: base64Audio,
              timestamp: Date.now(),
              encoding: 'mp3', // FIXED: Use MP3 like working REST TTS
              chunkNumber: chunkNumber,
              isLastChunk: i + CHUNK_SIZE >= audioArray.length
            });

            controller.enqueue(encoder.encode(`data: ${audioChunkData}\n\n`));
            console.log(`🎵 Streamed audio chunk #${chunkNumber}, size: ${chunk.length} bytes`);

            // Small delay to simulate streaming (optional)
            if (i + CHUNK_SIZE < audioArray.length) {
              await new Promise(resolve => setTimeout(resolve, 50)); // 50ms delay between chunks
            }
          }

          // Send completion message
          console.log(`🎉 FIXED: Audio streaming complete! Total chunks: ${chunkNumber}`);
          const closeData = JSON.stringify({
            type: 'close',
            message: 'completed',
            totalChunks: chunkNumber
          });
          controller.enqueue(encoder.encode(`data: ${closeData}\n\n`));
          controller.close();

        } catch (error: any) {
          console.error('❌ CRITICAL ERROR in REST TTS streaming processing:', error);
          console.error('❌ Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
          const errorData = JSON.stringify({
            type: 'error',
            message: `REST TTS streaming error: ${error.message}`,
            errorName: error.name,
            errorStack: error.stack
          });
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log('FIXED: REST TTS stream cancelled');
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error: any) {
    console.error('FIXED: REST TTS streaming API error:', error);
    return NextResponse.json(
      { error: 'Failed to process streaming TTS request', details: error.message },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
