import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@deepgram/sdk';

// PRODUCTION: Optimal configuration for real-time streaming TTS
const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;
const SAMPLE_RATE = 24000; // Optimal for speech
const BYTES_PER_SAMPLE = 2; // 16-bit = 2 bytes
const CHUNK_DURATION_MS = 100; // ~100ms chunks for fastest response time
const SAMPLES_PER_CHUNK = Math.floor(SAMPLE_RATE * CHUNK_DURATION_MS / 1000);
const CHUNK_SIZE_BYTES = SAMPLES_PER_CHUNK * BYTES_PER_SAMPLE; // ~8160 bytes

// PRODUCTION: Request timeout and retry configuration
const REQUEST_TIMEOUT_MS = 25000; // 25 seconds for Deepgram TTS
const MAX_RETRIES = 3;
const RETRY_DELAY_BASE = 1000; // 1 second base delay

// PRODUCTION: Stack-safe base64 encoding for large audio data
function uint8ArrayToBase64(uint8Array: Uint8Array): string {
  const CHUNK_SIZE = 8192; // Process in 8KB chunks to prevent stack overflow
  let result = '';

  for (let i = 0; i < uint8Array.length; i += CHUNK_SIZE) {
    const chunk = uint8Array.slice(i, i + CHUNK_SIZE);
    const chunkString = String.fromCharCode.apply(null, Array.from(chunk));
    result += chunkString;
  }

  return btoa(result);
}

// PRODUCTION: Extract audio data from Deepgram response object
async function extractAudioFromDeepgramResponse(response: any): Promise<Uint8Array> {
  console.log('🔍 Analyzing Deepgram response structure:', {
    responseType: typeof response,
    responseConstructor: response?.constructor?.name,
    responseKeys: response ? Object.keys(response) : 'null',
    hasResult: response && 'result' in response
  });

  // Based on our test: Deepgram returns SpeakRestClient with response.result containing Response object
  if (response && typeof response === 'object' && 'result' in response) {
    const result = response.result;

    console.log('🔍 Analyzing response.result:', {
      resultType: typeof result,
      resultConstructor: result?.constructor?.name,
      hasArrayBuffer: result && typeof result.arrayBuffer === 'function',
      status: result?.status,
      ok: result?.ok
    });

    // Check if result is a Response object with arrayBuffer method
    if (result && typeof result.arrayBuffer === 'function') {
      console.log('✅ Found Response object with arrayBuffer method');

      if (result.ok) {
        try {
          const audioBuffer = await result.arrayBuffer();
          console.log(`✅ Successfully extracted ${audioBuffer.byteLength} bytes from Response.arrayBuffer()`);
          return new Uint8Array(audioBuffer);
        } catch (error) {
          console.error('❌ Error calling result.arrayBuffer():', error);
          throw new Error(`Failed to extract audio from Response.arrayBuffer(): ${error}`);
        }
      } else {
        throw new Error(`Deepgram Response not ok: status ${result.status}`);
      }
    }
  }

  // Fallback: Handle other possible structures (legacy compatibility)
  if (response instanceof ArrayBuffer) {
    console.log('✅ Response is ArrayBuffer, converting to Uint8Array');
    return new Uint8Array(response);
  }

  if (response instanceof Uint8Array) {
    console.log('✅ Response is already Uint8Array');
    return response;
  }

  throw new Error(`Unable to extract audio data from Deepgram response. Response type: ${typeof response}, constructor: ${response?.constructor?.name}`);
}

// PRODUCTION: Retry logic with exponential backoff
async function retryDeepgramRequest(text: string, options: any, maxRetries: number = MAX_RETRIES): Promise<any> {
  const deepgram = createClient(DEEPGRAM_API_KEY!);

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🎵 Deepgram TTS attempt ${attempt}/${maxRetries}`);

      // Create timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Deepgram request timeout')), REQUEST_TIMEOUT_MS);
      });

      // Race between Deepgram request and timeout
      const requestPromise = deepgram.speak.request({ text }, options);
      const response = await Promise.race([requestPromise, timeoutPromise]) as any;

      // Based on our test: response is SpeakRestClient with response.result containing Response object
      if (!response) {
        console.error(`❌ No response returned from Deepgram (attempt ${attempt})`);

        if (attempt === maxRetries) {
          throw new Error('No response returned from Deepgram after all retries');
        }

        const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // Check if response.result exists and is a valid Response object
      if (!response.result || typeof response.result.arrayBuffer !== 'function') {
        console.error(`❌ Invalid response structure (attempt ${attempt}):`, {
          hasResult: !!response.result,
          resultType: typeof response.result,
          hasArrayBuffer: response.result && typeof response.result.arrayBuffer === 'function'
        });

        if (attempt === maxRetries) {
          throw new Error('Invalid response structure from Deepgram');
        }

        const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // Check if the Response is ok
      if (!response.result.ok) {
        console.error(`❌ Deepgram Response not ok (attempt ${attempt}):`, {
          status: response.result.status,
          statusText: response.result.statusText
        });

        if (attempt === maxRetries) {
          throw new Error(`Deepgram TTS failed with status ${response.result.status}: ${response.result.statusText}`);
        }

        const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      console.log(`✅ Deepgram TTS successful on attempt ${attempt}`);
      return response;

    } catch (error: any) {
      console.error(`❌ Deepgram request failed (attempt ${attempt}):`, error);

      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff for network errors
      const delay = RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
      console.log(`⏳ Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new Error('Unexpected end of retry loop');
}

export async function POST(request: NextRequest) {
  console.log('🎵 Production WebSocket TTS: Request received');

  if (!DEEPGRAM_API_KEY) {
    console.error('❌ DEEPGRAM_API_KEY not configured');
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  try {
    const { text, voiceModel = 'aura-2-thalia-en', format = 'linear16', sampleRate = 24000 } = await request.json(); // Changed to female voice

    if (!text || text.trim() === '') {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    // Validate text length for production
    if (text.length > 2000) {
      return NextResponse.json({
        error: 'Text too long. Maximum 2000 characters allowed for optimal performance.'
      }, { status: 400 });
    }

    console.log('🎵 Production TTS Request:', {
      textLength: text.length,
      voiceModel,
      format,
      sampleRate,
      chunkSize: CHUNK_SIZE_BYTES,
      maxRetries: MAX_RETRIES,
      timeout: REQUEST_TIMEOUT_MS
    });

    // PRODUCTION: Use retry logic with proper error handling
    const response = await retryDeepgramRequest(
      text,
      {
        model: voiceModel,
        encoding: 'linear16', // CRITICAL: Use LINEAR16 PCM instead of MP3
        container: 'none',    // Raw PCM data, no container
        sample_rate: sampleRate
        // NOTE: bit_rate is not applicable for LINEAR16 encoding
      }
    );

    // PRODUCTION: Extract audio data using the correct method
    const audioArray = await extractAudioFromDeepgramResponse(response);

    // Validate audio data
    if (!audioArray || audioArray.length === 0) {
      throw new Error('No audio data received from Deepgram');
    }

    console.log('🎵 Production TTS: Audio generated successfully', {
      totalBytes: audioArray.length,
      durationSeconds: (audioArray.length / BYTES_PER_SAMPLE / SAMPLE_RATE).toFixed(2),
      estimatedChunks: Math.ceil(audioArray.length / CHUNK_SIZE_BYTES),
      audioFormat: 'linear16',
      sampleRate: SAMPLE_RATE
    });

    // PRODUCTION: Create Server-Sent Events stream with enhanced error handling
    const encoder = new TextEncoder();
    let streamClosed = false;

    const stream = new ReadableStream({
      start(controller) {
        try {
          // Send initial status with comprehensive metadata
          const statusMessage = JSON.stringify({
            type: 'status',
            message: 'Production TTS streaming started',
            format: 'linear16',
            sampleRate: SAMPLE_RATE,
            totalBytes: audioArray.length,
            chunkSize: CHUNK_SIZE_BYTES,
            estimatedDuration: (audioArray.length / BYTES_PER_SAMPLE / SAMPLE_RATE).toFixed(2),
            timestamp: Date.now()
          });
          controller.enqueue(encoder.encode(`data: ${statusMessage}\n\n`));

          // PRODUCTION: Process audio in sample-aligned chunks with enhanced error handling
          let chunkNumber = 1;
          const totalChunks = Math.ceil(audioArray.length / CHUNK_SIZE_BYTES);

          const processChunk = (index: number) => {
            // Check if stream was closed
            if (streamClosed) {
              console.log('🎵 Production TTS: Stream already closed, stopping chunk processing');
              return;
            }

            if (index >= audioArray.length) {
              // Send completion message
              const closeMessage = JSON.stringify({
                type: 'close',
                message: 'Production TTS streaming completed successfully',
                totalChunks: chunkNumber - 1,
                totalBytes: audioArray.length,
                timestamp: Date.now()
              });

              try {
                controller.enqueue(encoder.encode(`data: ${closeMessage}\n\n`));
                controller.close();
                streamClosed = true;
                console.log(`✅ Production TTS: Streaming completed successfully (${chunkNumber - 1} chunks)`);
              } catch (error) {
                console.error('❌ Error sending close message:', error);
              }
              return;
            }

            try {
              // PRODUCTION: Sample-aligned chunking (not arbitrary byte boundaries)
              const chunkEnd = Math.min(index + CHUNK_SIZE_BYTES, audioArray.length);
              const chunk = audioArray.slice(index, chunkEnd);

              // Ensure chunk is sample-aligned (even number of bytes for 16-bit audio)
              const alignedChunk = chunk.length % 2 === 0 ? chunk : chunk.slice(0, -1);

              if (alignedChunk.length === 0) {
                console.warn('⚠️ Skipping empty aligned chunk');
                // IMMEDIATE PROCESSING: No delay for empty chunks
                processChunk(chunkEnd);
                return;
              }

              const base64Data = uint8ArrayToBase64(alignedChunk);
              const isLastChunk = chunkEnd >= audioArray.length;
              const progress = (chunkEnd / audioArray.length * 100).toFixed(1);

              const audioMessage = JSON.stringify({
                type: 'audio',
                data: base64Data,
                encoding: 'linear16',
                sampleRate: SAMPLE_RATE,
                chunkNumber: chunkNumber,
                totalChunks: totalChunks,
                isLastChunk: isLastChunk,
                chunkSize: alignedChunk.length,
                progress: parseFloat(progress),
                timestamp: Date.now()
              });

              controller.enqueue(encoder.encode(`data: ${audioMessage}\n\n`));

              console.log(`🎵 Production TTS: Sent chunk ${chunkNumber}/${totalChunks} (${alignedChunk.length} bytes, ${progress}%)`);
              chunkNumber++;

              // IMMEDIATE PROCESSING: Send chunks as fast as possible for zero-latency
              processChunk(chunkEnd);

            } catch (error) {
              console.error('❌ Production TTS: Error processing chunk:', error);

              if (!streamClosed) {
                try {
                  const errorMessage = JSON.stringify({
                    type: 'error',
                    message: `Chunk processing error: ${error instanceof Error ? error.message : String(error)}`,
                    chunkNumber: chunkNumber,
                    timestamp: Date.now()
                  });
                  controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
                  controller.close();
                  streamClosed = true;
                } catch (closeError) {
                  console.error('❌ Error closing stream after chunk error:', closeError);
                }
              }
            }
          };

          // Start processing immediately
          processChunk(0);

        } catch (error) {
          console.error('❌ Production TTS: Error in stream start:', error);

          try {
            const errorMessage = JSON.stringify({
              type: 'error',
              message: `Stream initialization error: ${error instanceof Error ? error.message : String(error)}`,
              timestamp: Date.now()
            });
            controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
            controller.close();
            streamClosed = true;
          } catch (closeError) {
            console.error('❌ Error closing stream after initialization error:', closeError);
          }
        }
      },

      cancel() {
        console.log('🎵 Production TTS: Stream cancelled by client');
        streamClosed = true;
      }
    });

    // PRODUCTION: Optimized response headers for streaming
    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
        'X-Content-Type-Options': 'nosniff',
        'X-TTS-Engine': 'deepgram-production',
        'X-TTS-Version': '1.0.0'
      },
    });

  } catch (error: any) {
    console.error('❌ Production TTS: Request failed:', {
      error: error.message || String(error),
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Determine appropriate error status and message
    let status = 500;
    let errorMessage = 'Internal server error';

    if (error.message?.includes('timeout')) {
      status = 504;
      errorMessage = 'Request timeout - please try again with shorter text';
    } else if (error.message?.includes('API key')) {
      status = 401;
      errorMessage = 'Authentication failed';
    } else if (error.message?.includes('rate limit') || error.message?.includes('429')) {
      status = 429;
      errorMessage = 'Rate limit exceeded - please try again later';
    } else if (error.message?.includes('text too long') || error.message?.includes('2000 characters')) {
      status = 400;
      errorMessage = 'Text too long - maximum 2000 characters allowed';
    } else if (error.message?.includes('Deepgram')) {
      status = 502;
      errorMessage = 'TTS service temporarily unavailable';
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
        timestamp: new Date().toISOString(),
        requestId: Math.random().toString(36).substring(7)
      },
      { status }
    );
  }
}

// PRODUCTION: Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}


