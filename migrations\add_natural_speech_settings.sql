-- Add natural speech settings to chat_interfaces table
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS enable_natural_speech BOOLEAN DEFAULT true;
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_model TEXT DEFAULT 'gpt-4o-mini';
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_temperature DECIMAL(3,2) DEFAULT 0.7;
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_max_tokens INTEGER DEFAULT 500;

-- Add comments for documentation
COMMENT ON COLUMN chat_interfaces.enable_natural_speech IS 'Enable GPT-4o-mini processing for more natural speech patterns';
COMMENT ON COLUMN chat_interfaces.natural_speech_model IS 'OpenAI model to use for natural speech processing';
COMMENT ON COLUMN chat_interfaces.natural_speech_temperature IS 'Temperature setting for natural speech processing (0.0-1.0)';
COMMENT ON COLUMN chat_interfaces.natural_speech_max_tokens IS 'Maximum tokens for natural speech processing';
