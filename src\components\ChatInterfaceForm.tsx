'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
// Removed Select components - using radio buttons instead
import { toast } from 'sonner'
import { AVAILABLE_VOICES, DEFAULT_VOICE, getVoiceById, type VoiceOption } from '@/lib/voice-config'
import { useVoiceModelAccess, useFeatureAccess } from '@/hooks/useTier'
import { User } from '@supabase/supabase-js'

export default function ChatInterfaceForm({
  onSave,
  webhooks,
  initialData = null,
  user = null
}: {
  onSave: (data: {
    name: string
    description: string
    webhookUrl: string
    primaryColor: string
    useGradientHeader: boolean
    gradientStartColor: string
    gradientEndColor: string
    gradientDirection: string
    userBubbleColor: string
    botBubbleColor: string
    userTextColor: string
    botTextColor: string
    logoUrl: string
    darkMode: boolean
    showName: boolean
    useBlackOutline: boolean
    showPoweredBy: boolean
    poweredByText: string
    poweredByUrl: string
    poweredByTextColor: string
    welcomeMessage: string
    enableNaturalSpeech: boolean
    naturalSpeechModel: string
    naturalSpeechTemperature: number
    naturalSpeechMaxTokens: number
    voiceModel: string
  }) => void
  webhooks?: Array<{
    id: string
    name: string
    url: string
    description?: string
  }>
  user?: User | null
  initialData?: {
    name: string
    description: string
    webhookUrl?: string
    webhookurl?: string // Support both camelCase and lowercase
    primary_color?: string
    use_gradient_header?: boolean
    gradient_start_color?: string
    gradient_end_color?: string
    gradient_direction?: string
    user_bubble_color?: string
    bot_bubble_color?: string
    user_text_color?: string
    bot_text_color?: string
    logo_url?: string
    dark_mode?: boolean
    show_name?: boolean
    use_black_outline?: boolean
    show_powered_by?: boolean
    powered_by_text?: string
    powered_by_url?: string
    powered_by_text_color?: string
    welcome_message?: string
    enable_natural_speech?: boolean
    natural_speech_model?: string
    natural_speech_temperature?: number
    natural_speech_max_tokens?: number
    voice_model?: string
  } | null
}) {
  const [name, setName] = useState(initialData?.name || '')
  const [nameError, setNameError] = useState<string | null>(null)
  const [description, setDescription] = useState(initialData?.description || '')
  const [webhookUrl, setWebhookUrl] = useState(initialData?.webhookUrl || initialData?.webhookurl || '')
  const [primaryColor, setPrimaryColor] = useState(initialData?.primary_color || '#3b82f6')
  const [useGradientHeader, setUseGradientHeader] = useState(initialData?.use_gradient_header || false)
  const [gradientStartColor, setGradientStartColor] = useState(initialData?.gradient_start_color || '#3b82f6')
  const [gradientEndColor, setGradientEndColor] = useState(initialData?.gradient_end_color || '#9333ea')
  const [gradientDirection, setGradientDirection] = useState(initialData?.gradient_direction || 'to bottom')
  const [userBubbleColor, setUserBubbleColor] = useState(initialData?.user_bubble_color || '#ffffff')
  const [botBubbleColor, setBotBubbleColor] = useState(initialData?.bot_bubble_color || '#3b82f6')
  const [userTextColor, setUserTextColor] = useState(initialData?.user_text_color || '#000000')
  const [botTextColor, setBotTextColor] = useState(initialData?.bot_text_color || '#ffffff')
  const [logoUrl, setLogoUrl] = useState(initialData?.logo_url || '')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState(initialData?.logo_url || '')
  const [darkMode, setDarkMode] = useState(initialData?.dark_mode || false)
  const [showName, setShowName] = useState(initialData?.show_name !== false) // Default to true
  const [showPoweredBy, setShowPoweredBy] = useState(initialData?.show_powered_by !== undefined ? initialData.show_powered_by : true)
  const [poweredByText, setPoweredByText] = useState(initialData?.powered_by_text || 'Powered by BotFusion')
  const [poweredByUrl, setPoweredByUrl] = useState(initialData?.powered_by_url || 'https://botfusion.io')
  const [poweredByTextColor, setPoweredByTextColor] = useState(initialData?.powered_by_text_color || primaryColor)
  const [welcomeMessage, setWelcomeMessage] = useState(initialData?.welcome_message || '')
  const [useBlackOutline, setUseBlackOutline] = useState(initialData?.use_black_outline || false)
  const [enableNaturalSpeech, setEnableNaturalSpeech] = useState(initialData?.enable_natural_speech !== false) // Default to true
  const [naturalSpeechModel, setNaturalSpeechModel] = useState(initialData?.natural_speech_model || 'gpt-4o-mini')
  const [naturalSpeechTemperature, setNaturalSpeechTemperature] = useState(initialData?.natural_speech_temperature || 0.7)
  const [naturalSpeechMaxTokens, setNaturalSpeechMaxTokens] = useState(initialData?.natural_speech_max_tokens || 500)
  const [voiceModel, setVoiceModel] = useState(initialData?.voice_model || DEFAULT_VOICE)
  const [activeTab, setActiveTab] = useState('basic')
  const [activeAppearanceTab, setActiveAppearanceTab] = useState('general')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Get voice access information for current user
  const { voiceFeaturesEnabled, availableModels, userTier } = useVoiceModelAccess(user, voiceModel)

  // Get branding access information for current user
  const { hasAccess: canRemoveBranding } = useFeatureAccess(user, 'brandingRemoval')
  const { hasAccess: hasWhiteLabeling } = useFeatureAccess(user, 'whiteLabelingEnabled')
  const { hasAccess: hasCustomBranding } = useFeatureAccess(user, 'customBrandingText')

  // Get advanced customization access
  const { hasAccess: hasAdvancedCustomization } = useFeatureAccess(user, 'advancedCustomization')

  // Handle logo file selection
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check file size (limit to 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast.error('Logo image must be less than 2MB')
        return
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        toast.error('File must be an image')
        return
      }

      setLogoFile(file)

      // Create a preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setLogoPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Convert image to data URL instead of using Supabase Storage
  const convertLogoToDataUrl = async (): Promise<string | null> => {
    if (!logoFile) {
      return logoUrl // Return existing URL if no new file
    }

    try {
      // Use the preview data URL we already created
      if (logoPreview && logoPreview.startsWith('data:')) {
        console.log('Using existing data URL for logo')
        return logoPreview
      }

      // Create a new data URL if needed
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onloadend = () => {
          const result = reader.result as string
          resolve(result)
        }
        reader.onerror = () => {
          reject(new Error('Failed to read file'))
        }
        reader.readAsDataURL(logoFile)
      })
    } catch (error) {
      console.error('Error converting logo to data URL:', error)
      toast.error('Failed to process logo image')
      return null
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name) {
      toast.error('Please enter a name for the chat interface')
      return
    }
    if (!webhookUrl) {
      toast.error('Please select a webhook URL')
      return
    }
    try {
      new URL(webhookUrl) // Validate URL format

      // Convert logo to data URL if there's a new file
      let finalLogoUrl = logoUrl
      if (logoFile) {
        const dataUrl = await convertLogoToDataUrl()
        if (dataUrl) {
          finalLogoUrl = dataUrl
        }
      }

      // Prepare the data object to be passed to onSave
      const chatInterfaceData = {
        name,
        description,
        webhookUrl,
        primaryColor,
        useGradientHeader,
        gradientStartColor,
        gradientEndColor,
        gradientDirection,
        userBubbleColor,
        botBubbleColor,
        userTextColor,
        botTextColor,
        logoUrl: finalLogoUrl,
        darkMode,
        showName,
        useBlackOutline,
        showPoweredBy,
        poweredByText,
        poweredByUrl,
        poweredByTextColor,
        welcomeMessage,
        enableNaturalSpeech,
        naturalSpeechModel,
        naturalSpeechTemperature,
        naturalSpeechMaxTokens,
        voiceModel
      };

      // Call the onSave callback with the data
      // The parent component will handle the API call and state updates
      onSave(chatInterfaceData);

    } catch (error) {
      console.error('Error in form submission:', error);
      if (error instanceof Error) {
        toast.error(`Failed to save chat interface: ${error.message}`);
      } else {
        toast.error('Failed to save chat interface due to unknown error');
      }
    }
  }

  // Preview component for chat bubbles
  const ChatPreview = () => {
    const bgColor = darkMode ? 'bg-gray-900' : 'bg-gray-50';
    const textColor = darkMode ? 'text-white' : 'text-black';

    // Create header style based on gradient settings
    const headerStyle = useGradientHeader
      ? {
          background: `linear-gradient(${gradientDirection}, ${gradientStartColor}, ${gradientEndColor})`,
        }
      : { backgroundColor: primaryColor };

    return (
      <div className={`chat-interface p-4 rounded-lg ${bgColor} ${textColor} ${useBlackOutline ? 'border-2 border-black' : 'border border-gray-300'} mb-4`}>
        <h3 className="text-sm font-medium mb-2">Preview</h3>

        {/* Header preview */}
        <div className="rounded-t-lg p-3 mb-4" style={headerStyle}>
          <div className="flex items-center gap-2">
            {logoPreview && (
              <img
                src={logoPreview}
                alt="Chat Logo"
                className="h-8 w-auto object-contain"
              />
            )}
            {showName && (
              <h3 className="text-lg font-medium text-white">
                {name || 'Chat Interface Name'}
              </h3>
            )}
          </div>
        </div>

        <div className="space-y-3">
          {welcomeMessage && (
            <div className="flex justify-start">
              <div
                className="max-w-[80%] rounded-lg p-2 shadow-sm"
                style={{ backgroundColor: botBubbleColor, color: botTextColor }}
              >
                <p className="text-sm" style={{ color: botTextColor }}>{welcomeMessage}</p>
              </div>
            </div>
          )}
          <div className="flex justify-end">
            <div
              className="max-w-[80%] rounded-lg p-2 shadow-sm border border-gray-200"
              style={{ backgroundColor: userBubbleColor, color: userTextColor }}
            >
              <p className="text-sm" style={{ color: userTextColor }}>Hello! How can I help?</p>
            </div>
          </div>
          <div className="flex justify-start">
            <div
              className="max-w-[80%] rounded-lg p-2 shadow-sm"
              style={{ backgroundColor: botBubbleColor, color: botTextColor }}
            >
              <p className="text-sm" style={{ color: botTextColor }}>I'm your assistant. What would you like to know?</p>
            </div>
          </div>
        </div>

        {/* Powered By section */}
        {showPoweredBy && (
          <div className="mt-4 pt-2 border-t border-gray-300 text-center">
            <a
              href={poweredByUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs hover:underline"
              style={{ color: poweredByTextColor }}
            >
              {poweredByText}
            </a>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="p-4">
      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="basic" className="w-full" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-white">
                Interface Name
              </label>
              <Input
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  setNameError(null); // Clear error when user types
                }}
                placeholder="My Chat Interface"
                required
                className={nameError ? "border-red-500 focus:ring-red-500" : ""}
              />
              {nameError && (
                <div className="text-red-500 text-sm mt-1 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  {nameError}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">
                Description
              </label>
              <Input
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe this chat interface..."
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">
                Welcome Message
              </label>
              <textarea
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                placeholder="Enter a welcome message that will be shown when a user first opens the chat..."
                className="w-full rounded border border-gray-300 bg-white p-2 text-black min-h-[100px]"
              />
              <p className="text-xs text-white">
                This message will be displayed automatically when a user first opens the chat.
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white">
                Select Webhook
              </label>
              <select
                className="w-full rounded border border-gray-300 bg-white p-2 text-black"
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select a webhook
                </option>
                {webhooks?.map((webhook) => (
                  <option key={webhook.id} value={webhook.url}>
                    {webhook.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                onClick={() => setActiveTab('appearance')}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                Next: Appearance
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4">
            {/* Appearance Sub-Tabs */}
            <div className="mb-4 border-b border-gray-200">
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setActiveAppearanceTab('general')}
                  className={`px-3 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeAppearanceTab === 'general'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-500 hover:bg-gray-100'
                  }`}
                >
                  General
                </button>
                <button
                  type="button"
                  onClick={() => setActiveAppearanceTab('colors')}
                  className={`px-3 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeAppearanceTab === 'colors'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-500 hover:bg-gray-100'
                  }`}
                >
                  Colors
                </button>
                <button
                  type="button"
                  onClick={() => setActiveAppearanceTab('logo')}
                  className={`px-3 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeAppearanceTab === 'logo'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-500 hover:bg-gray-100'
                  }`}
                >
                  Logo
                </button>
                <button
                  type="button"
                  onClick={() => setActiveAppearanceTab('speech')}
                  className={`px-3 py-2 text-sm font-medium rounded-t-lg transition-colors ${
                    activeAppearanceTab === 'speech'
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-500 hover:bg-gray-100'
                  }`}
                >
                  Speech
                </button>
              </div>
            </div>

            {/* General Settings */}
            {activeAppearanceTab === 'general' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dark-mode" className="text-sm font-medium text-white">
                    Dark Mode
                  </Label>
                  <Switch
                    id="dark-mode"
                    checked={darkMode}
                    onCheckedChange={setDarkMode}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-name" className="text-sm font-medium text-white">
                    Show Chat Name
                  </Label>
                  <Switch
                    id="show-name"
                    checked={showName}
                    onCheckedChange={setShowName}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="use-black-outline" className="text-sm font-medium text-white">
                    Use Black Outline
                  </Label>
                  <Switch
                    id="use-black-outline"
                    checked={useBlackOutline}
                    onCheckedChange={setUseBlackOutline}
                  />
                </div>

                <div className="border-t pt-4 mt-4">
                  <h3 className="text-sm font-medium text-white mb-4">
                    Powered By Section
                    {!canRemoveBranding && (
                      <span className="ml-2 text-xs text-yellow-400">
                        (Branding removal requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                      </span>
                    )}
                  </h3>

                  {!canRemoveBranding && (
                    <div className="p-3 bg-blue-900/20 border border-blue-600 rounded-lg mb-4">
                      <p className="text-sm text-blue-200">
                        🏷️ Free tier requires "Powered by BotFusion X" branding.
                        Upgrade to Standard tier to optionally remove branding,
                        or Pro tier for complete white-labeling with custom text/URL.
                      </p>
                    </div>
                  )}

                  <div className="flex items-center justify-between mb-4">
                    <Label htmlFor="show-powered-by" className="text-sm font-medium">
                      Show "Powered By" Section
                      {!canRemoveBranding && (
                        <span className="ml-2 text-xs text-gray-400">(Required for {userTier} tier)</span>
                      )}
                    </Label>
                    <Switch
                      id="show-powered-by"
                      checked={showPoweredBy}
                      onCheckedChange={canRemoveBranding ? setShowPoweredBy : undefined}
                      disabled={!canRemoveBranding}
                    />
                  </div>

                  {showPoweredBy && (
                    <>
                      <div className="space-y-2 mb-4">
                        <Label htmlFor="powered-by-text" className="text-sm font-medium">
                          Text
                          {!hasCustomBranding && (
                            <span className="ml-2 text-xs text-orange-400">
                              (Custom text requires Pro tier)
                            </span>
                          )}
                        </Label>
                        <Input
                          id="powered-by-text"
                          value={poweredByText}
                          onChange={hasCustomBranding ? (e) => setPoweredByText(e.target.value) : undefined}
                          placeholder={hasCustomBranding ? "Powered by..." : "Powered by BotFusion X (Pro tier required for custom text)"}
                          disabled={!hasCustomBranding}
                          className={!hasCustomBranding ? 'opacity-60 cursor-not-allowed' : ''}
                        />
                        {!hasCustomBranding && (
                          <p className="text-xs text-gray-400">
                            Free and Standard tiers must use "Powered by BotFusion X".
                            Upgrade to Pro tier for custom branding text.
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="powered-by-url" className="text-sm font-medium">
                          URL
                          {!hasCustomBranding && (
                            <span className="ml-2 text-xs text-orange-400">
                              (Custom URL requires Pro tier)
                            </span>
                          )}
                        </Label>
                        <Input
                          id="powered-by-url"
                          value={poweredByUrl}
                          onChange={hasCustomBranding ? (e) => setPoweredByUrl(e.target.value) : undefined}
                          placeholder={hasCustomBranding ? "https://..." : "https://botfusion.io (Pro tier required for custom URL)"}
                          disabled={!hasCustomBranding}
                          className={!hasCustomBranding ? 'opacity-60 cursor-not-allowed' : ''}
                        />
                        {!hasCustomBranding && (
                          <p className="text-xs text-gray-400">
                            Free and Standard tiers must link to BotFusion.
                            Upgrade to Pro tier for custom branding URL.
                          </p>
                        )}
                      </div>

                      <div className="space-y-2 mt-4">
                        <Label htmlFor="powered-by-text-color" className="text-sm font-medium">
                          Text Color
                        </Label>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-full border border-gray-300"
                            style={{ backgroundColor: poweredByTextColor }}
                          ></div>
                          <Input
                            id="powered-by-text-color"
                            type="color"
                            value={poweredByTextColor}
                            onChange={(e) => setPoweredByTextColor(e.target.value)}
                            className="w-full h-10"
                          />
                          <Input
                            type="text"
                            value={poweredByTextColor}
                            onChange={(e) => setPoweredByTextColor(e.target.value)}
                            className="w-28"
                          />
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Color Settings */}
            {activeAppearanceTab === 'colors' && (
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white">
                      Primary Color
                    </label>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-8 h-8 rounded-full border border-gray-300"
                        style={{ backgroundColor: primaryColor }}
                      ></div>
                      <Input
                        type="color"
                        value={primaryColor}
                        onChange={(e) => setPrimaryColor(e.target.value)}
                        className="w-full h-10"
                      />
                      <Input
                        type="text"
                        value={primaryColor}
                        onChange={(e) => setPrimaryColor(e.target.value)}
                        className="w-28"
                      />
                    </div>
                  </div>

                {/* Gradient Header Options */}
                <div className="border-t border-gray-700 pt-4 mt-4">
                  <div className="flex items-center justify-between mb-4">
                    <Label htmlFor="use-gradient-header" className="text-sm font-medium text-white">
                      Use Gradient Header
                      {!hasAdvancedCustomization && (
                        <span className="ml-2 text-xs text-orange-400">
                          (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                        </span>
                      )}
                    </Label>
                    <Switch
                      id="use-gradient-header"
                      checked={useGradientHeader}
                      onCheckedChange={hasAdvancedCustomization ? setUseGradientHeader : undefined}
                      disabled={!hasAdvancedCustomization}
                    />
                  </div>

                  {!hasAdvancedCustomization && (
                    <div className="p-3 bg-orange-900/20 border border-orange-600 rounded-lg mb-4">
                      <p className="text-sm text-orange-200">
                        🎨 Advanced customization features like gradient headers are available on Standard tier and above.
                        Upgrade to unlock gradient headers, advanced color options, and more customization features.
                      </p>
                    </div>
                  )}

                  {useGradientHeader && hasAdvancedCustomization && (
                    <>
                      <div className="space-y-2 mt-4">
                        <label className="text-sm font-medium text-white">
                          Gradient Start Color
                        </label>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-full border border-gray-300"
                            style={{ backgroundColor: gradientStartColor }}
                          ></div>
                          <Input
                            type="color"
                            value={gradientStartColor}
                            onChange={(e) => setGradientStartColor(e.target.value)}
                            className="w-full h-10"
                          />
                          <Input
                            type="text"
                            value={gradientStartColor}
                            onChange={(e) => setGradientStartColor(e.target.value)}
                            className="w-28"
                          />
                        </div>
                      </div>

                      <div className="space-y-2 mt-4">
                        <label className="text-sm font-medium text-white">
                          Gradient End Color
                        </label>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-full border border-gray-300"
                            style={{ backgroundColor: gradientEndColor }}
                          ></div>
                          <Input
                            type="color"
                            value={gradientEndColor}
                            onChange={(e) => setGradientEndColor(e.target.value)}
                            className="w-full h-10"
                          />
                          <Input
                            type="text"
                            value={gradientEndColor}
                            onChange={(e) => setGradientEndColor(e.target.value)}
                            className="w-28"
                          />
                        </div>
                      </div>

                      <div className="space-y-2 mt-4">
                        <label className="text-sm font-medium text-white">
                          Gradient Direction
                        </label>
                        <select
                          value={gradientDirection}
                          onChange={(e) => setGradientDirection(e.target.value)}
                          className="w-full rounded border border-gray-300 bg-white p-2 text-black"
                        >
                          <option value="to bottom">Top to Bottom</option>
                          <option value="to top">Bottom to Top</option>
                          <option value="to right">Left to Right</option>
                          <option value="to left">Right to Left</option>
                          <option value="to bottom right">Top Left to Bottom Right</option>
                          <option value="to bottom left">Top Right to Bottom Left</option>
                          <option value="to top right">Bottom Left to Top Right</option>
                          <option value="to top left">Bottom Right to Top Left</option>
                        </select>
                      </div>
                    </>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">
                    User Bubble Color
                    {!hasAdvancedCustomization && (
                      <span className="ml-2 text-xs text-orange-400">
                        (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                      </span>
                    )}
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-8 h-8 rounded-full border border-gray-300"
                      style={{ backgroundColor: userBubbleColor }}
                    ></div>
                    <Input
                      type="color"
                      value={userBubbleColor}
                      onChange={hasAdvancedCustomization ? (e) => setUserBubbleColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-full h-10 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                    <Input
                      type="text"
                      value={userBubbleColor}
                      onChange={hasAdvancedCustomization ? (e) => setUserBubbleColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-28 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">
                    User Text Color
                    {!hasAdvancedCustomization && (
                      <span className="ml-2 text-xs text-orange-400">
                        (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                      </span>
                    )}
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-8 h-8 rounded-full border border-gray-300"
                      style={{ backgroundColor: userTextColor }}
                    ></div>
                    <Input
                      type="color"
                      value={userTextColor}
                      onChange={hasAdvancedCustomization ? (e) => setUserTextColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-full h-10 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                    <Input
                      type="text"
                      value={userTextColor}
                      onChange={hasAdvancedCustomization ? (e) => setUserTextColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-28 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">
                    Bot Bubble Color
                    {!hasAdvancedCustomization && (
                      <span className="ml-2 text-xs text-orange-400">
                        (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                      </span>
                    )}
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-8 h-8 rounded-full border border-gray-300"
                      style={{ backgroundColor: botBubbleColor }}
                    ></div>
                    <Input
                      type="color"
                      value={botBubbleColor}
                      onChange={hasAdvancedCustomization ? (e) => setBotBubbleColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-full h-10 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                    <Input
                      type="text"
                      value={botBubbleColor}
                      onChange={hasAdvancedCustomization ? (e) => setBotBubbleColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-28 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">
                    Bot Text Color
                    {!hasAdvancedCustomization && (
                      <span className="ml-2 text-xs text-orange-400">
                        (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                      </span>
                    )}
                  </label>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-8 h-8 rounded-full border border-gray-300"
                      style={{ backgroundColor: botTextColor }}
                    ></div>
                    <Input
                      type="color"
                      value={botTextColor}
                      onChange={hasAdvancedCustomization ? (e) => setBotTextColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-full h-10 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                    <Input
                      type="text"
                      value={botTextColor}
                      onChange={hasAdvancedCustomization ? (e) => setBotTextColor(e.target.value) : undefined}
                      disabled={!hasAdvancedCustomization}
                      className={`w-28 ${!hasAdvancedCustomization ? 'opacity-60 cursor-not-allowed' : ''}`}
                    />
                  </div>
                </div>
                </div>
              </ScrollArea>
            )}

            {/* Logo Settings */}
            {activeAppearanceTab === 'logo' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">
                    Logo Image
                  </label>
                  <div className="flex flex-col gap-2">
                    {logoPreview && (
                      <div className="flex justify-center p-4 bg-gray-100 rounded-md">
                        <img
                          src={logoPreview}
                          alt="Logo Preview"
                          className="h-24 w-auto object-contain"
                        />
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoChange}
                        className="hidden"
                        ref={fileInputRef}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="flex-1"
                      >
                        {logoFile ? 'Change Logo' : 'Upload Logo'}
                      </Button>
                      {(logoFile || logoPreview) && (
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={() => {
                            setLogoFile(null)
                            setLogoPreview('')
                            setLogoUrl('')
                          }}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                    <p className="text-xs text-white">
                      Recommended size: 200x200px. Max 2MB. PNG or JPG.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Natural Speech Settings */}
            {activeAppearanceTab === 'speech' && (
              <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Voice & Speech Settings</h3>
                  <p className="text-sm text-gray-300">
                    Configure voice selection and natural speech processing for text-to-speech.
                  </p>

                  {/* Voice Selection */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-white">
                      Voice Selection
                      {!voiceFeaturesEnabled && (
                        <span className="ml-2 text-xs text-yellow-400">
                          (Requires {userTier === 'free' ? 'Standard' : 'Pro'} tier)
                        </span>
                      )}
                    </Label>

                    {!voiceFeaturesEnabled && (
                      <div className="p-3 bg-yellow-900/20 border border-yellow-600 rounded-lg">
                        <p className="text-sm text-yellow-200">
                          🎤 Voice features are not available on the {userTier} tier.
                          Upgrade to Standard tier to unlock basic voice features with 3 voice models,
                          or Pro tier for all 6 voice models plus natural speech processing.
                        </p>
                      </div>
                    )}

                    {/* Female Voices */}
                    <div className="space-y-2">
                      <h4 className="text-xs font-semibold text-gray-300 uppercase tracking-wide">Female Voices</h4>
                      <div className="space-y-2">
                        {AVAILABLE_VOICES.filter(voice => voice.gender === 'female').map((voice) => {
                          const hasAccess = voiceFeaturesEnabled && availableModels.includes(voice.id)
                          const isDisabled = !voiceFeaturesEnabled || !hasAccess

                          return (
                            <label key={voice.id} className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                              isDisabled
                                ? 'bg-gray-800/50 border-gray-700 cursor-not-allowed opacity-60'
                                : 'bg-white/10 border-gray-600 hover:bg-white/20 cursor-pointer'
                            }`}>
                              <input
                                type="radio"
                                name="voiceModel"
                                value={voice.id}
                                checked={voiceModel === voice.id}
                                onChange={(e) => setVoiceModel(e.target.value)}
                                disabled={isDisabled}
                                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:opacity-50"
                              />
                              <div className="flex-1">
                                <div className={`text-sm font-medium ${isDisabled ? 'text-gray-400' : 'text-white'}`}>
                                  {voice.name}
                                  {!hasAccess && voiceFeaturesEnabled && (
                                    <span className="ml-2 text-xs text-orange-400">(Pro tier)</span>
                                  )}
                                </div>
                                <div className={`text-xs ${isDisabled ? 'text-gray-500' : 'text-gray-300'}`}>
                                  {voice.description}
                                </div>
                              </div>
                            </label>
                          )
                        })}
                      </div>
                    </div>

                    {/* Male Voices */}
                    <div className="space-y-2">
                      <h4 className="text-xs font-semibold text-gray-300 uppercase tracking-wide">Male Voices</h4>
                      <div className="space-y-2">
                        {AVAILABLE_VOICES.filter(voice => voice.gender === 'male').map((voice) => {
                          const hasAccess = voiceFeaturesEnabled && availableModels.includes(voice.id)
                          const isDisabled = !voiceFeaturesEnabled || !hasAccess

                          return (
                            <label key={voice.id} className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                              isDisabled
                                ? 'bg-gray-800/50 border-gray-700 cursor-not-allowed opacity-60'
                                : 'bg-white/10 border-gray-600 hover:bg-white/20 cursor-pointer'
                            }`}>
                              <input
                                type="radio"
                                name="voiceModel"
                                value={voice.id}
                                checked={voiceModel === voice.id}
                                onChange={(e) => setVoiceModel(e.target.value)}
                                disabled={isDisabled}
                                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:opacity-50"
                              />
                              <div className="flex-1">
                                <div className={`text-sm font-medium ${isDisabled ? 'text-gray-400' : 'text-white'}`}>
                                  {voice.name}
                                  {!hasAccess && voiceFeaturesEnabled && (
                                    <span className="ml-2 text-xs text-orange-400">(Pro tier)</span>
                                  )}
                                </div>
                                <div className={`text-xs ${isDisabled ? 'text-gray-500' : 'text-gray-300'}`}>
                                  {voice.description}
                                </div>
                              </div>
                            </label>
                          )
                        })}
                      </div>
                    </div>

                    <p className="text-xs text-gray-400">
                      Choose the voice that will be used for text-to-speech responses. Each voice has a unique personality and tone.
                    </p>
                  </div>

                  {/* Voice Preview Button */}
                  <div className="space-y-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      disabled={!voiceFeaturesEnabled || !availableModels.includes(voiceModel)}
                      onClick={async () => {
                        const selectedVoice = getVoiceById(voiceModel);
                        if (selectedVoice) {
                          try {
                            // Create a sample text for voice preview
                            const sampleText = `Hello! This is ${selectedVoice.name}, a ${selectedVoice.gender} voice. ${selectedVoice.description}`;

                            // Call the TTS API to generate audio
                            const response = await fetch('/api/deepgram-tts', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify({
                                text: sampleText,
                                voiceModel: `aura-2-${voiceModel}-en`,
                                format: 'linear16',
                                sampleRate: 24000
                              })
                            });

                            if (response.ok) {
                              const audioBlob = await response.blob();
                              const audioUrl = URL.createObjectURL(audioBlob);
                              const audio = new Audio(audioUrl);

                              audio.onended = () => {
                                URL.revokeObjectURL(audioUrl);
                              };

                              await audio.play();
                              toast.success(`Playing preview of ${selectedVoice.name} voice`);
                            } else {
                              throw new Error('Failed to generate voice preview');
                            }
                          } catch (error) {
                            console.error('Voice preview error:', error);
                            toast.error('Failed to play voice preview. Please try again.');
                          }
                        }
                      }}
                      className="w-full"
                      title={!voiceFeaturesEnabled ? 'Voice features require Standard tier or higher' :
                             !availableModels.includes(voiceModel) ? 'This voice requires Pro tier' : ''}
                    >
                      🔊 Preview Voice
                      {(!voiceFeaturesEnabled || !availableModels.includes(voiceModel)) && (
                        <span className="ml-2 text-xs">
                          ({!voiceFeaturesEnabled ? 'Upgrade Required' : 'Pro Tier'})
                        </span>
                      )}
                    </Button>
                    <p className="text-xs text-gray-400">
                      Click to hear a sample of the selected voice.
                    </p>
                  </div>

                  <div className="border-t border-gray-600 pt-4">
                    <h4 className="text-md font-medium text-white mb-2">Natural Speech Processing</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Configure GPT-4o-mini processing to make text-to-speech sound more natural and conversational.
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enable-natural-speech" className="text-sm font-medium text-white">
                        Enable Natural Speech Processing
                      </Label>
                      <p className="text-xs text-gray-400 mt-1">
                        Process bot responses through GPT-4o-mini for more natural speech patterns
                      </p>
                    </div>
                    <Switch
                      id="enable-natural-speech"
                      checked={enableNaturalSpeech}
                      onCheckedChange={setEnableNaturalSpeech}
                    />
                  </div>

                  {enableNaturalSpeech && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="natural-speech-model" className="text-sm font-medium text-white">
                          AI Model
                        </Label>
                        <select
                          id="natural-speech-model"
                          className="w-full rounded border border-gray-300 bg-white p-2 text-black"
                          value={naturalSpeechModel}
                          onChange={(e) => setNaturalSpeechModel(e.target.value)}
                        >
                          <option value="gpt-4o-mini">GPT-4o-mini (Recommended)</option>
                          <option value="gpt-4o">GPT-4o (Higher Quality)</option>
                          <option value="gpt-3.5-turbo">GPT-3.5 Turbo (Faster)</option>
                        </select>
                        <p className="text-xs text-gray-400">
                          GPT-4o-mini provides the best balance of quality and speed for natural speech processing.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="natural-speech-temperature" className="text-sm font-medium text-white">
                          Creativity Level: {naturalSpeechTemperature}
                        </Label>
                        <input
                          id="natural-speech-temperature"
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={naturalSpeechTemperature}
                          onChange={(e) => setNaturalSpeechTemperature(parseFloat(e.target.value))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-400">
                          <span>Conservative (0.0)</span>
                          <span>Balanced (0.7)</span>
                          <span>Creative (1.0)</span>
                        </div>
                        <p className="text-xs text-gray-400">
                          Higher values make speech more creative and varied, lower values more consistent.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="natural-speech-max-tokens" className="text-sm font-medium text-white">
                          Max Response Length: {naturalSpeechMaxTokens} tokens
                        </Label>
                        <input
                          id="natural-speech-max-tokens"
                          type="range"
                          min="100"
                          max="1000"
                          step="50"
                          value={naturalSpeechMaxTokens}
                          onChange={(e) => setNaturalSpeechMaxTokens(parseInt(e.target.value))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-400">
                          <span>Short (100)</span>
                          <span>Medium (500)</span>
                          <span>Long (1000)</span>
                        </div>
                        <p className="text-xs text-gray-400">
                          Maximum length for processed speech. Longer responses may be more natural but cost more.
                        </p>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-start">
                          <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          <div>
                            <h4 className="text-sm font-medium text-blue-800">Natural Speech Processing</h4>
                            <p className="text-xs text-blue-700 mt-1">
                              This feature uses OpenAI's GPT models to make bot responses sound more natural when spoken.
                              It adds conversational elements, improves flow, and makes speech more engaging.
                            </p>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-between mt-6 pt-4 border-t border-gray-200">
              <Button
                type="button"
                onClick={() => setActiveTab('basic')}
                variant="outline"
              >
                Back
              </Button>
              <Button
                type="button"
                onClick={() => setActiveTab('preview')}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                Next: Preview
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <ChatPreview />

            <div className="flex justify-between">
              <Button
                type="button"
                onClick={() => setActiveTab('appearance')}
                variant="outline"
              >
                Back
              </Button>
              <Button
                type="submit"
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {initialData ? 'Update' : 'Create'} Chat Interface
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </form>
    </Card>
  )
}