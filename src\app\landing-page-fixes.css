/* Landing page specific text color fixes */

/* Make all text white by default */
.tech-grid-bg h1,
.tech-grid-bg h2,
.tech-grid-bg h3,
.tech-grid-bg p,
.tech-grid-bg blockquote,
.tech-grid-bg a:not(.glass-card.bg-neon-blue\/20),
.tech-grid-bg span,
.tech-grid-bg div {
  color: white !important;
}

/* Specific fixes for sections */
.tech-grid-bg .text-neon-blue {
  color: white !important;
}

/* Keep the sign-up button blue */
.glass-card.bg-neon-blue\/20 {
  color: var(--neon-blue) !important;
}

/* Fix testimonials */
.tech-grid-bg blockquote {
  color: white !important;
}

/* Fix footer links */
.tech-grid-bg footer a {
  color: white !important;
}

/* Fix pricing plan text */
.tech-grid-bg .glass-card h3,
.tech-grid-bg .glass-card p {
  color: white !important;
}

/* Ensure pricing cards have consistent height */
.tech-grid-bg .max-w-5xl .pricing-card {
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  min-height: 320px !important;
}

/* Style pricing card content */
.pricing-content {
  flex-grow: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Style pricing card button container */
.pricing-button {
  margin-top: auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 60px !important;
}

/* Fix button styling in pricing section */
.tech-grid-bg .max-w-5xl .glass-card .glass-card.button-glow-hover {
  min-width: 128px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix "How It Works" section */
.tech-grid-bg .glass-card h3.text-neon-blue,
.tech-grid-bg .glass-card p.text-neon-blue {
  color: white !important;
}
