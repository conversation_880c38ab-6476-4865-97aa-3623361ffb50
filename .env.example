# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Deepgram Configuration
# API key for Deepgram TTS and STT
DEEPGRAM_API_KEY=your-deepgram-api-key

# Deepgram STT Configuration
DEEPGRAM_STT_ENABLED=true
DEEPGRAM_STT_MODEL=nova-2
DEEPGRAM_STT_LANGUAGE=en-US
DEEPGRAM_STT_SMART_FORMAT=true
DEEPGRAM_STT_INTERIM_RESULTS=true
DEEPGRAM_STT_ENDPOINTING=10
DEEPGRAM_STT_VAD_EVENTS=true

# STT Provider Configuration
STT_PROVIDER=deepgram
STT_FALLBACK_ENABLED=true
STT_RATE_LIMIT_PER_MINUTE=100

# Optional: Set a specific port for the Next.js app
# PORT=3000
