import { createClient } from '@/lib/supabase/server'

export async function runMigrations() {
  try {
    console.log('Running database migrations...')
    const supabase = createClient()

    // First, try to query the chat_interfaces table to see if it exists
    const { error: tableCheckError } = await supabase
      .from('chat_interfaces')
      .select('id')
      .limit(1)

    if (tableCheckError) {
      console.error('Error checking chat_interfaces table:', tableCheckError)
      return
    }

    // Try to query the use_black_outline column to see if it exists
    // If the column doesn't exist, this will fail with an error
    const { error: columnCheckError } = await supabase
      .from('chat_interfaces')
      .select('use_black_outline')
      .limit(1)

    // If we get an error, the column likely doesn't exist
    if (columnCheckError) {
      console.log('use_black_outline column may not exist, adding fallback handling in API routes')
    } else {
      console.log('use_black_outline column exists in chat_interfaces table')
    }

    console.log('Migration check completed')
  } catch (error) {
    console.error('Error in migration process:', error)
  }
}
