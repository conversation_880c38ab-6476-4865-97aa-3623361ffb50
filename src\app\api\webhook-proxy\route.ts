import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

// ADVANCED OPTIMIZATION: Simple in-memory cache for common responses
const responseCache = new Map<string, { response: string; timestamp: number }>();
const CACHE_TTL = 300000; // 5 minutes cache TTL
const MAX_CACHE_SIZE = 100; // Limit cache size to prevent memory issues

// Helper function to generate cache key
function generateCacheKey(message: string, chatId: string): string {
  return `${chatId}:${message.toLowerCase().trim()}`;
}

// Helper function to clean expired cache entries
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of responseCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      responseCache.delete(key);
    }
  }
}

// ADVANCED OPTIMIZATION: Reuse database connection
let cachedSupabaseClient: any = null;
function getOptimizedSupabaseClient() {
  if (!cachedSupabaseClient) {
    cachedSupabaseClient = createServiceClient();
  }
  return cachedSupabaseClient;
}

// ADVANCED OPTIMIZATION: Request deduplication to prevent duplicate processing
const activeRequests = new Map<string, Promise<NextResponse>>();
function generateRequestKey(message: string, chatId: string, sessionId: string): string {
  return `${chatId}:${sessionId}:${message}:${Date.now().toString().slice(-6)}`;
}

export async function POST(request: Request) {
  const startTime = Date.now();
  console.log('🚀 [PERFORMANCE] Webhook proxy started at:', new Date().toISOString());

  try {
    const parseStartTime = Date.now();
    const body = await request.json()
    const parseTime = Date.now() - parseStartTime;
    console.log('🚀 [PERFORMANCE] JSON parsing took:', parseTime, 'ms');
    console.log('Webhook proxy received request:', body)

    // Forward the request to the actual webhook
    const webhookUrl = body.webhookUrl
    const message = body.message
    const chatId = body.chatId
    const sessionId = body.sessionId || Date.now().toString()

    if (!webhookUrl || !message) {
      return NextResponse.json({
        error: 'Missing webhookUrl or message in request body'
      }, { status: 400 })
    }

    if (!chatId) {
      return NextResponse.json({
        error: 'Missing chatId in request body'
      }, { status: 400 })
    }

    // ADVANCED OPTIMIZATION: Check cache first for common responses
    cleanExpiredCache(); // Clean expired entries
    const cacheKey = generateCacheKey(message, chatId);
    const cachedResponse = responseCache.get(cacheKey);

    if (cachedResponse) {
      console.log('🚀 [PERFORMANCE] Cache hit! Returning cached response for:', message.substring(0, 50));
      const cacheTime = Date.now() - parseStartTime;
      console.log('🚀 [PERFORMANCE] Total cached response time:', cacheTime, 'ms');

      return NextResponse.json({
        success: true,
        status: 200,
        cached: true,
        response: cachedResponse.response
      });
    }

    console.log(`Forwarding message to webhook: ${webhookUrl}`)

    const webhookStartTime = Date.now();
    console.log('🚀 [PERFORMANCE] Webhook call started at:', new Date().toISOString());

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: message,
        chatId: body.chatId || 'unknown',
        sessionId: body.sessionId || Date.now().toString()
      })
    })

    const webhookTime = Date.now() - webhookStartTime;
    console.log('🚀 [PERFORMANCE] Webhook call took:', webhookTime, 'ms');
    console.log('Webhook response status:', response.status)

    const responseText = await response.text()
    console.log('Webhook response text:', responseText)

    let responseData
    try {
      // Only try to parse if there's actual content
      if (responseText && responseText.trim()) {
        responseData = JSON.parse(responseText)
        console.log('Parsed webhook response:', responseData)
      } else {
        console.log('Empty response from webhook, using default response')
        responseData = { response: "I've received your message and am processing it." }
      }
    } catch (e) {
      console.error('Failed to parse webhook response as JSON:', e)
      responseData = { response: responseText || "I've received your message." }
    }

    // Extract the bot response
    const botResponse = responseData.response || responseData.output || responseData.text || responseData.message || responseText || "Thank you for your message."

    // ADVANCED OPTIMIZATION: Cache the response for future use
    if (responseCache.size < MAX_CACHE_SIZE) {
      responseCache.set(cacheKey, {
        response: botResponse,
        timestamp: Date.now()
      });
      console.log('🚀 [PERFORMANCE] Cached response for future use');
    }

    // OPTIMIZATION: Streamlined database operations for faster response
    const dbStartTime = Date.now();
    console.log('🚀 [PERFORMANCE] Database operations started at:', new Date().toISOString());

    try {
      // ADVANCED OPTIMIZATION: Use cached database connection
      const supabase = getOptimizedSupabaseClient()
      console.log('Logging messages to database for chatId:', chatId, 'sessionId:', sessionId)

      // ADVANCED OPTIMIZATION: Log both messages in parallel for better performance
      const [userResult, botResult] = await Promise.all([
        supabase
          .from('chat_messages')
          .insert({
            chat_interface_id: chatId,
            session_id: sessionId,
            sender: 'user',
            content: message
          })
          .select(),
        supabase
          .from('chat_messages')
          .insert({
            chat_interface_id: chatId,
            session_id: sessionId,
            sender: 'bot',
            content: botResponse
          })
          .select()
      ]);

      if (userResult.error) {
        console.error('Error logging user message:', userResult.error)
        throw userResult.error
      }

      if (botResult.error) {
        console.error('Error logging bot message:', botResult.error)
        throw botResult.error
      }

      console.log('Both messages logged successfully in parallel')
      console.log('User message:', userResult.data)
      console.log('Bot message:', botResult.data)

      // OPTIMIZATION: Skip verification step to reduce database load and improve response time

      // OPTIMIZATION: Simplified stats update using upsert for better performance
      try {
        const { error: statsError } = await supabase
          .from('chat_stats')
          .upsert({
            chat_interface_id: chatId,
            session_id: sessionId,
            message_count: 2, // Will be incremented if exists
            last_message_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'chat_interface_id,session_id'
          })

        if (statsError) {
          console.error(`Error upserting stats for session ${sessionId}:`, statsError)
        } else {
          console.log(`Updated stats for session ${sessionId}`)
        }
      } catch (statsError) {
        console.error(`Error updating chat stats for session ${sessionId}:`, statsError)
        // Continue even if stats update fails
      }
    } catch (error) {
      console.error('Error logging messages to database:', error)
      // Continue even if logging fails
    }

    const dbTime = Date.now() - dbStartTime;
    const totalTime = Date.now() - startTime;
    console.log('🚀 [PERFORMANCE] Database operations took:', dbTime, 'ms');
    console.log('🚀 [PERFORMANCE] Total request processing took:', totalTime, 'ms');
    console.log('🚀 [PERFORMANCE] Breakdown - Parse:', parseTime, 'ms, Webhook:', webhookTime, 'ms, DB:', dbTime, 'ms');

    // Return the webhook response
    return NextResponse.json({
      success: true,
      status: response.status,
      data: responseData,
      response: botResponse
    })
  } catch (error) {
    console.error('Error in webhook proxy:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

