# Test Accounts Setup Guide

This guide helps you create test accounts for comprehensive tier system testing in BotFusion X.

## Overview

We need to create 3 test accounts (one for each tier) to thoroughly test the tier system:

1. **Free Tier Account** - Limited features, 1 chat interface max
2. **Standard Tier Account** - Voice features, unlimited interfaces, branding removal
3. **Pro Tier Account** - All features including white-labeling, analytics, premium voices

## Step 1: Create Auth Users (Manual)

Since Supabase Auth manages the `auth.users` table, you need to create these users manually through the Supabase dashboard or programmatically.

### Option A: Via Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Users
3. Click "Add user" and create these accounts:

**Free Tier Test Account:**
- Email: `<EMAIL>`
- Password: `TestFree123!`
- User ID: `********-1111-1111-1111-********1111`

**Standard Tier Test Account:**
- Email: `<EMAIL>`
- Password: `TestStandard123!`
- User ID: `*************-2222-2222-************`

**Pro Tier Test Account:**
- Email: `<EMAIL>`
- Password: `TestPro123!`
- User ID: `*************-3333-3333-************`

### Option B: Via Supabase Admin API

```javascript
// Use this in your Supabase Edge Function or Node.js script
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'YOUR_SUPABASE_URL',
  'YOUR_SERVICE_ROLE_KEY' // Service role key required for admin operations
)

async function createTestUsers() {
  const testUsers = [
    {
      id: '********-1111-1111-1111-********1111',
      email: '<EMAIL>',
      password: 'TestFree123!',
      tier: 'free'
    },
    {
      id: '*************-2222-2222-************',
      email: '<EMAIL>',
      password: 'TestStandard123!',
      tier: 'standard'
    },
    {
      id: '*************-3333-3333-************',
      email: '<EMAIL>',
      password: 'TestPro123!',
      tier: 'pro'
    }
  ]

  for (const user of testUsers) {
    const { data, error } = await supabase.auth.admin.createUser({
      user_id: user.id,
      email: user.email,
      password: user.password,
      email_confirm: true
    })
    
    if (error) {
      console.error(`Error creating ${user.tier} user:`, error)
    } else {
      console.log(`Created ${user.tier} user:`, data.user.email)
    }
  }
}

createTestUsers()
```

## Step 2: Run the SQL Script

After creating the auth users, run the SQL script to create user profiles and test data:

1. Open your Supabase SQL Editor
2. Copy and paste the contents of `create-test-accounts.sql`
3. Execute the script

This will create:
- User profiles with appropriate tier settings
- Sample chat interfaces for each tier
- Test data demonstrating tier restrictions

## Step 3: Verify Test Accounts

After running the script, verify the accounts were created correctly:

```sql
-- Check user profiles
SELECT 
  id,
  tier,
  subscription_status,
  subscription_start_date,
  subscription_end_date
FROM user_profiles 
WHERE id IN (
  '********-1111-1111-1111-********1111',
  '*************-2222-2222-************', 
  '*************-3333-3333-************'
)
ORDER BY tier;

-- Check chat interfaces
SELECT 
  ci.name,
  up.tier,
  ci.show_powered_by,
  ci.use_gradient_header,
  ci.enable_natural_speech,
  ci.voice_model
FROM chat_interfaces ci
JOIN user_profiles up ON ci.user_id = up.id
WHERE up.id IN (
  '********-1111-1111-1111-********1111',
  '*************-2222-2222-************',
  '*************-3333-3333-************'
)
ORDER BY up.tier, ci.name;
```

## Test Account Features

### Free Tier Account (`<EMAIL>`)
- ✅ 1 chat interface (at limit)
- ❌ No voice features
- ❌ Cannot remove branding
- ❌ No advanced customization
- ❌ No gradient headers
- ❌ No custom colors
- ✅ Basic chat functionality

### Standard Tier Account (`<EMAIL>`)
- ✅ Unlimited chat interfaces (2 created for testing)
- ✅ Voice features (3 voice models: thalia, asteria, helena)
- ✅ Can remove branding
- ✅ Advanced customization (gradients, colors)
- ❌ No white-labeling (custom branding text/URL)
- ❌ No natural speech processing
- ❌ No premium voice models

### Pro Tier Account (`<EMAIL>`)
- ✅ Unlimited chat interfaces (3 created for testing)
- ✅ All voice features (6 voice models)
- ✅ Natural speech processing
- ✅ Complete white-labeling
- ✅ Custom branding text and URLs
- ✅ Analytics dashboard access
- ✅ Priority support
- ✅ All customization features

## Testing Scenarios

### 1. Chat Interface Creation Limits
- Login as free tier user → Try to create 2nd interface (should fail)
- Login as standard/pro tier user → Create multiple interfaces (should succeed)

### 2. Voice Feature Access
- Free tier → Voice options should be disabled/hidden
- Standard tier → Should see 3 voice models (thalia, asteria, helena)
- Pro tier → Should see all 6 voice models + natural speech options

### 3. Branding Controls
- Free tier → Cannot disable "Powered by BotFusion X"
- Standard tier → Can disable branding, but cannot customize text/URL
- Pro tier → Full white-labeling with custom text and URLs

### 4. Advanced Customization
- Free tier → Basic colors only, no gradients or advanced options
- Standard/Pro tier → Full gradient headers, custom colors, advanced styling

### 5. UI Restrictions
- Each tier should show appropriate upgrade prompts for restricted features
- Disabled controls should have helpful tooltips explaining tier requirements
- Upgrade buttons should link to pricing page with correct tier highlighted

## Cleanup

To remove test accounts and data:

```sql
-- Remove chat interfaces
DELETE FROM chat_interfaces 
WHERE user_id IN (
  '********-1111-1111-1111-********1111',
  '*************-2222-2222-************',
  '*************-3333-3333-************'
);

-- Remove user profiles
DELETE FROM user_profiles 
WHERE id IN (
  '********-1111-1111-1111-********1111',
  '*************-2222-2222-************',
  '*************-3333-3333-************'
);
```

Then manually delete the auth users from the Supabase dashboard.

## Notes

- Test accounts use `.test` domain to clearly identify them as test data
- UUIDs are sequential for easy identification (11111..., 22222..., 33333...)
- Sample chat interfaces demonstrate tier-specific features and restrictions
- All test data includes realistic examples of tier-appropriate configurations
