/* Production-specific CSS fixes */

/* Fix for backdrop-filter in production */
.glass-card,
.glass-button {
  background: var(--card-bg);
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--card-border);
  box-shadow: var(--glow-shadow);
}

/* Fix for hover effects in production */
.glass-card:hover,
.glass-card[data-hover="true"] {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

.glass-button:hover,
.glass-button[data-hover="true"] {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

.button-glow-hover:hover,
.button-glow-hover[data-hover="true"] {
  box-shadow: 0 0 8px var(--glow-color) !important;
}

.shadow-glow-blue:hover,
.shadow-glow-blue[data-hover="true"] {
  box-shadow: 0 0 8px var(--glow-color) !important;
}

.ambient-glow:hover,
.ambient-glow[data-hover="true"] {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

/* Fix for animations in production */
@keyframes blob {
  0%, 100% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.03);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.97);
  }
}

@-webkit-keyframes blob {
  0%, 100% {
    -webkit-transform: translate(0px, 0px) scale(1);
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    -webkit-transform: translate(30px, -50px) scale(1.03);
    transform: translate(30px, -50px) scale(1.03);
  }
  66% {
    -webkit-transform: translate(-20px, 20px) scale(0.97);
    transform: translate(-20px, 20px) scale(0.97);
  }
}

.animate-blob {
  -webkit-animation: blob 15s ease-in-out infinite !important;
  animation: blob 15s ease-in-out infinite !important;
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
  50% { box-shadow: 0 0 6px rgba(59, 130, 246, 0.4); }
  100% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
}

@-webkit-keyframes pulse-glow {
  0% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
  50% { box-shadow: 0 0 6px rgba(59, 130, 246, 0.4); }
  100% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
}

.pulse-glow {
  -webkit-animation: pulse-glow 4s infinite !important;
  animation: pulse-glow 4s infinite !important;
}

/* Ensure all transitions work properly */
.glass-card,
.glass-button,
.button-glow-hover,
.shadow-glow-blue,
.ambient-glow {
  -webkit-transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out !important;
  transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out !important;
}

/* Add default glow to elements */
.glass-card,
.glass-button {
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.2) !important;
}

/* Ensure text colors are visible */
.text-neon-blue {
  color: var(--text-primary) !important; /* Changed to white */
}

/* Only keep the sign-up button blue */
.glass-card.bg-neon-blue\/20 .text-neon-blue {
  color: var(--neon-blue) !important;
}

.text-glow-blue {
  text-shadow: 1px 1px 1px black, 0 0 2px var(--glow-color) !important;
  color: var(--text-primary) !important;
}

/* Force all text in the main content to be white, except in chat interfaces */
h1:not(.chat-interface *),
h2:not(.chat-interface *),
h3:not(.chat-interface *),
p:not(.chat-interface *),
blockquote:not(.chat-interface *),
a:not(.chat-interface *),
span:not(.chat-interface *),
div:not(.chat-interface *) {
  color: var(--text-primary) !important;
}

/* Ensure chat interface text follows the specified colors */
.chat-interface p,
.chat-interface span,
.chat-interface div,
.chat-interface * {
  color: inherit;
}

/* Specifically target chat bubbles to ensure text color is applied */
.chat-interface [style*="color:"] > p,
.chat-interface [style*="color:"] > span,
.chat-interface [style*="color:"] > div {
  color: inherit !important;
}

/* Force user message bubbles to use the specified text color */
.chat-interface div[style*="backgroundColor"] p,
.chat-interface div[style*="backgroundColor"] span {
  color: inherit !important;
}

/* Ensure input fields in chat interface have visible text */
.chat-interface input,
.chat-interface textarea {
  color: black !important;
}

/* Dark mode input fields */
.chat-interface .bg-gray-800 input,
.chat-interface .bg-gray-800 textarea,
.chat-interface .bg-gray-900 input,
.chat-interface .bg-gray-900 textarea {
  color: white !important;
}

/* Style placeholder text for better visibility */
.chat-interface input::placeholder,
.chat-interface textarea::placeholder {
  color: #888888 !important;
  opacity: 1 !important;
}

.chat-interface .bg-gray-800 input::placeholder,
.chat-interface .bg-gray-800 textarea::placeholder,
.chat-interface .bg-gray-900 input::placeholder,
.chat-interface .bg-gray-900 textarea::placeholder {
  color: #aaaaaa !important;
  opacity: 1 !important;
}

/* Exception for the sign-up button */
.glass-card.bg-neon-blue\/20 {
  color: var(--neon-blue) !important;
}
