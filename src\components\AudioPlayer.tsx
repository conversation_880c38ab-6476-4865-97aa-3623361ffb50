'use client';

import React, { useEffect, useRef, useState } from 'react';

interface AudioPlayerProps {
  text: string;
  voice?: string;
  autoPlay?: boolean;
  onStart?: () => void;
  onEnd?: () => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  text,
  voice = 'nova',
  autoPlay = false,
  onStart,
  onEnd,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [audioSrc, setAudioSrc] = useState<string>('');
  const audioRef = useRef<HTMLAudioElement>(null);
  const previousText = useRef<string>('');

  useEffect(() => {
    // Only generate audio if text has changed
    if (text && text !== previousText.current) {
      previousText.current = text;
      generateAudioSrc();
    }
  }, [text]);

  useEffect(() => {
    // Auto-play when audio source is available and autoPlay is true
    if (audioSrc && autoPlay && audioRef.current) {
      playAudio();
    }
  }, [audioSrc, autoPlay]);

  const generateAudioSrc = () => {
    if (!text || isLoading) return;

    setIsLoading(true);

    try {
      // Create a timestamp to prevent caching
      const timestamp = Date.now();

      // Create a direct URL to the Deepgram TTS API with the text and voice as query parameters
      // This avoids using blob URLs entirely
      const encodedText = encodeURIComponent(text);
      const encodedVoice = encodeURIComponent(voice);

      // Set the audio source to the Deepgram TTS API endpoint
      // The audio element will make a GET request to this URL when it loads
      const directUrl = `/api/deepgram-tts?text=${encodedText}&voice=${encodedVoice}&t=${timestamp}`;

      // Set the audio source
      setAudioSrc(directUrl);
    } catch (error) {
      console.error('Error generating audio source:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const playAudio = () => {
    if (!audioRef.current || !audioSrc) return;

    onStart?.();

    audioRef.current.play().catch(error => {
      console.error('Error playing audio:', error);
    });
  };

  const handleEnded = () => {
    onEnd?.();
  };

  return (
    <audio
      ref={audioRef}
      src={audioSrc}
      onEnded={handleEnded}
      style={{ display: 'none' }}
      controls
    />
  );
};

export default AudioPlayer;
