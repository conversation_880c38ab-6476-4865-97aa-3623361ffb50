// <PERSON>ript to run the user tiers migration
import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function runTierMigration() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables:')
    console.error('- NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl)
    console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey)
    process.exit(1)
  }

  // Create Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  console.log('🚀 Starting user tiers migration...')

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'migrations', '0006_create_user_tiers_system.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    console.log('📄 Running migration SQL...')

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    console.log(`📝 Executing ${statements.length} SQL statements...`)

    // Execute each statement individually
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement.trim()) {
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)

        const { error } = await supabase.rpc('exec', {
          sql: statement + ';'
        })

        if (error) {
          console.error(`❌ Statement ${i + 1} failed:`, error)
          console.error('Statement:', statement.substring(0, 100) + '...')

          // Try alternative method for some statements
          if (statement.includes('CREATE TABLE') || statement.includes('INSERT INTO')) {
            console.log('🔄 Trying alternative execution method...')

            try {
              // For table creation and inserts, try using the REST API directly
              if (statement.includes('CREATE TABLE IF NOT EXISTS user_profiles')) {
                await createUserProfilesTable(supabase)
              } else if (statement.includes('CREATE TABLE IF NOT EXISTS tier_features')) {
                await createTierFeaturesTable(supabase)
              } else if (statement.includes('CREATE TABLE IF NOT EXISTS usage_tracking')) {
                await createUsageTrackingTable(supabase)
              } else if (statement.includes('INSERT INTO tier_features')) {
                await insertTierFeatures(supabase)
              }
            } catch (altError) {
              console.error('❌ Alternative method also failed:', altError)
            }
          }
        } else {
          console.log(`✅ Statement ${i + 1} completed`)
        }
      }
    }

    console.log('✅ Migration completed successfully!')

    // Verify the migration by checking if tables exist
    console.log('🔍 Verifying migration...')

    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['user_profiles', 'tier_features', 'usage_tracking'])

    if (tablesError) {
      console.error('❌ Error verifying tables:', tablesError)
    } else {
      console.log('✅ Tables created:', tables.map(t => t.table_name))
    }

    // Check if tier features were inserted
    const { data: features, error: featuresError } = await supabase
      .from('tier_features')
      .select('tier, feature_name')
      .limit(5)

    if (featuresError) {
      console.error('❌ Error checking tier features:', featuresError)
    } else {
      console.log('✅ Tier features inserted:', features.length, 'features found')
    }

    // Check if functions were created
    const { data: functions, error: functionsError } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .in('routine_name', ['get_user_tier_features', 'can_create_chat_interface', 'handle_new_user'])

    if (functionsError) {
      console.error('❌ Error checking functions:', functionsError)
    } else {
      console.log('✅ Functions created:', functions.map(f => f.routine_name))
    }

    console.log('🎉 User tiers system migration completed successfully!')
    console.log('')
    console.log('📋 Summary:')
    console.log('- Created user_profiles table with tier information')
    console.log('- Created tier_features table with feature definitions')
    console.log('- Created usage_tracking table for monitoring limits')
    console.log('- Added utility functions for tier management')
    console.log('- Updated chat_interfaces policies to respect tier limits')
    console.log('- Added trigger to auto-create profiles for new users')

  } catch (error) {
    console.error('❌ Unexpected error during migration:', error)
    process.exit(1)
  }
}

// Helper functions for manual table creation
async function createUserProfilesTable(supabase) {
  console.log('📋 Creating user_profiles table...')

  // Check if table exists
  const { data: existingTable } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .eq('table_name', 'user_profiles')
    .single()

  if (existingTable) {
    console.log('✅ user_profiles table already exists')
    return
  }

  // Create using SQL query (simplified approach)
  const { error } = await supabase.rpc('exec', {
    sql: `
      CREATE TABLE user_profiles (
        id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
        tier TEXT NOT NULL DEFAULT 'free' CHECK (tier IN ('free', 'standard', 'pro')),
        subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial')),
        subscription_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        subscription_end_date TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    `
  })

  if (error) {
    console.error('❌ Failed to create user_profiles table:', error)
  } else {
    console.log('✅ user_profiles table created')
  }
}

async function createTierFeaturesTable(supabase) {
  console.log('📋 Creating tier_features table...')

  const { error } = await supabase.rpc('exec', {
    sql: `
      CREATE TABLE IF NOT EXISTS tier_features (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        tier TEXT NOT NULL CHECK (tier IN ('free', 'standard', 'pro')),
        feature_name TEXT NOT NULL,
        feature_value JSONB NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  })

  if (error) {
    console.error('❌ Failed to create tier_features table:', error)
  } else {
    console.log('✅ tier_features table created')
  }
}

async function createUsageTrackingTable(supabase) {
  console.log('📋 Creating usage_tracking table...')

  const { error } = await supabase.rpc('exec', {
    sql: `
      CREATE TABLE IF NOT EXISTS usage_tracking (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        resource_type TEXT NOT NULL,
        current_usage INTEGER NOT NULL DEFAULT 0,
        period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        period_end TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
    `
  })

  if (error) {
    console.error('❌ Failed to create usage_tracking table:', error)
  } else {
    console.log('✅ usage_tracking table created')
  }
}

async function insertTierFeatures(supabase) {
  console.log('📋 Inserting tier features...')

  const features = [
    // Free Tier Features
    { tier: 'free', feature_name: 'max_chat_interfaces', feature_value: 1, description: 'Maximum number of chat interfaces allowed' },
    { tier: 'free', feature_name: 'voice_features_enabled', feature_value: false, description: 'Whether voice features (TTS/STT) are available' },
    { tier: 'free', feature_name: 'voice_models', feature_value: [], description: 'Available voice models for TTS' },
    { tier: 'free', feature_name: 'branding_removal', feature_value: false, description: 'Whether "Powered by BotFusion X" can be removed' },

    // Standard Tier Features
    { tier: 'standard', feature_name: 'max_chat_interfaces', feature_value: -1, description: 'Unlimited chat interfaces' },
    { tier: 'standard', feature_name: 'voice_features_enabled', feature_value: true, description: 'Voice features available' },
    { tier: 'standard', feature_name: 'voice_models', feature_value: ['thalia', 'asteria', 'helena'], description: 'Basic voice models available' },
    { tier: 'standard', feature_name: 'branding_removal', feature_value: true, description: 'Can remove "Powered by BotFusion X"' },

    // Pro Tier Features
    { tier: 'pro', feature_name: 'max_chat_interfaces', feature_value: -1, description: 'Unlimited chat interfaces' },
    { tier: 'pro', feature_name: 'voice_features_enabled', feature_value: true, description: 'Premium voice features available' },
    { tier: 'pro', feature_name: 'voice_models', feature_value: ['thalia', 'asteria', 'helena', 'arcas', 'apollo', 'zeus'], description: 'All voice models available' },
    { tier: 'pro', feature_name: 'branding_removal', feature_value: true, description: 'Can remove "Powered by BotFusion X"' },
  ]

  const { error } = await supabase
    .from('tier_features')
    .insert(features)

  if (error) {
    console.error('❌ Failed to insert tier features:', error)
  } else {
    console.log('✅ Tier features inserted')
  }
}

// Run the migration
runTierMigration()
