// Deepgram STT Configuration Types
export interface DeepgramSTTConfig {
  model: 'nova-2' | 'nova-3';
  language: string;
  smart_format: boolean;
  interim_results: boolean;
  endpointing: number | false;
  vad_events: boolean;
  keyterm?: string[];
  profanity_filter?: boolean;
  punctuate?: boolean;
  numerals?: boolean;
}

// WebSocket Message Types
export interface STTStartMessage {
  type: 'start';
  config: DeepgramSTTConfig;
}

export interface STTAudioMessage {
  type: 'audio';
  data: string; // base64 encoded audio data
}

export interface STTStopMessage {
  type: 'stop';
}

export interface STTKeepAliveMessage {
  type: 'keepalive';
}

export type STTMessage = STTStartMessage | STTAudioMessage | STTStopMessage | STTKeepAliveMessage;

// Response Types
export interface STTTranscriptResponse {
  type: 'transcript';
  text: string;
  is_final: boolean;
  confidence: number;
  timestamp: number;
  words?: Array<{
    word: string;
    start: number;
    end: number;
    confidence: number;
    punctuated_word?: string;
  }>;
}

export interface STTErrorResponse {
  type: 'error';
  message: string;
  code: string;
  recoverable: boolean;
  timestamp: number;
}

export interface STTReadyResponse {
  type: 'ready';
  session_id: string;
  timestamp: number;
}

export interface STTVADResponse {
  type: 'vad';
  speech_started: boolean;
  timestamp: number;
}

export interface STTCloseResponse {
  type: 'close';
  reason: string;
  timestamp: number;
}

export type STTResponse = 
  | STTTranscriptResponse 
  | STTErrorResponse 
  | STTReadyResponse 
  | STTVADResponse 
  | STTCloseResponse;

// Provider Types
export enum STTProvider {
  DEEPGRAM = 'deepgram',
  WEB_SPEECH = 'web_speech',
  AUTO = 'auto',
  DISABLED = 'disabled'
}

// Session Management
export interface STTSession {
  id: string;
  provider: STTProvider;
  websocket?: WebSocket;
  deepgramSocket?: WebSocket;
  startTime: number;
  lastActivity: number;
  config: DeepgramSTTConfig;
  isActive: boolean;
}

// Error Types
export enum STTErrorCode {
  WEBSOCKET_ERROR = 'WEBSOCKET_ERROR',
  DEEPGRAM_ERROR = 'DEEPGRAM_ERROR',
  AUDIO_ERROR = 'AUDIO_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface STTError {
  code: STTErrorCode;
  message: string;
  recoverable: boolean;
  timestamp: number;
  details?: any;
}

// Audio Processing Types
export interface AudioChunk {
  data: ArrayBuffer;
  timestamp: number;
  sampleRate: number;
  channels: number;
}

// Component Props (same as current SpeechToText)
export interface DeepgramSpeechToTextProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
  color?: string;
  onMicrophoneActiveChange?: (active: boolean) => void;
  autoSend?: boolean;
  onSend?: (transcript?: string) => void;
}

// Component Ref (same as current SpeechToText)
export interface DeepgramSpeechToTextRef {
  resetMicrophoneActive: () => void;
}

// Provider Context Types
export interface SpeechToTextContextValue {
  provider: STTProvider;
  setProvider: (provider: STTProvider) => void;
  isDeepgramAvailable: boolean;
  isWebSpeechAvailable: boolean;
  currentProvider: STTProvider;
  fallbackReason?: string;
}

// Configuration Types
export interface STTEnvironmentConfig {
  enabled: boolean;
  model: string;
  language: string;
  smart_format: boolean;
  interim_results: boolean;
  endpointing: number;
  vad_events: boolean;
  provider: STTProvider;
  fallback_enabled: boolean;
  rate_limit_per_minute: number;
}

// Metrics and Monitoring
export interface STTMetrics {
  totalRequests: number;
  successfulRequests: number;
  errorRequests: number;
  averageLatency: number;
  fallbackCount: number;
  providerUsage: Record<STTProvider, number>;
  lastUpdated: number;
}

// Rate Limiting
export interface RateLimitInfo {
  requests: number;
  windowStart: number;
  windowSize: number;
  limit: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
}
