import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

// Get current directory path in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables from .env.local
dotenv.config({ path: path.join(__dirname, '../.env.local') })

async function createTables() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase URL or Service Role Key')
    process.exit(1)
  }

  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('Connected to Supabase')
  
  // Create webhooks table
  console.log('Creating webhooks table...')
  const webhooksSQL = `
    CREATE TABLE IF NOT EXISTS webhooks (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name TEXT NOT NULL,
      url TEXT NOT NULL,
      description TEXT,
      createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON webhooks
      FOR SELECT USING (true);
    
    CREATE POLICY IF NOT EXISTS "Enable insert access for authenticated users" ON webhooks
      FOR INSERT WITH CHECK (auth.role() = 'authenticated');
  `
  
  try {
    const { error: webhooksError } = await supabase.rpc('execute_sql', { sql: webhooksSQL })
    if (webhooksError) {
      console.error('Error creating webhooks table:', webhooksError)
    } else {
      console.log('Webhooks table created successfully')
    }
  } catch (err) {
    console.error('Error executing SQL for webhooks table:', err)
    
    // Try direct SQL execution
    try {
      const { error } = await supabase.from('webhooks').select('count').limit(1)
      if (error && error.code === '42P01') {
        console.log('Trying direct SQL execution for webhooks table...')
        const { error: directError } = await supabase.sql(webhooksSQL)
        if (directError) {
          console.error('Direct SQL execution failed:', directError)
        } else {
          console.log('Webhooks table created successfully via direct SQL')
        }
      }
    } catch (directErr) {
      console.error('Direct SQL execution failed:', directErr)
    }
  }
  
  // Create chat_interfaces table
  console.log('Creating chat_interfaces table...')
  const chatInterfacesSQL = `
    CREATE TABLE IF NOT EXISTS chat_interfaces (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name TEXT NOT NULL,
      description TEXT,
      webhookUrl TEXT,
      user_id UUID,
      createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    ALTER TABLE chat_interfaces ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON chat_interfaces
      FOR SELECT USING (true);
    
    CREATE POLICY IF NOT EXISTS "Enable insert access for authenticated users" ON chat_interfaces
      FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    
    CREATE POLICY IF NOT EXISTS "Enable update access for authenticated users" ON chat_interfaces
      FOR UPDATE USING (auth.role() = 'authenticated');
  `
  
  try {
    const { error: chatInterfacesError } = await supabase.rpc('execute_sql', { sql: chatInterfacesSQL })
    if (chatInterfacesError) {
      console.error('Error creating chat_interfaces table:', chatInterfacesError)
    } else {
      console.log('Chat interfaces table created successfully')
    }
  } catch (err) {
    console.error('Error executing SQL for chat_interfaces table:', err)
    
    // Try direct SQL execution
    try {
      const { error } = await supabase.from('chat_interfaces').select('count').limit(1)
      if (error && error.code === '42P01') {
        console.log('Trying direct SQL execution for chat_interfaces table...')
        const { error: directError } = await supabase.sql(chatInterfacesSQL)
        if (directError) {
          console.error('Direct SQL execution failed:', directError)
        } else {
          console.log('Chat interfaces table created successfully via direct SQL')
        }
      }
    } catch (directErr) {
      console.error('Direct SQL execution failed:', directErr)
    }
  }
  
  // Create chat_stats table
  console.log('Creating chat_stats table...')
  const chatStatsSQL = `
    CREATE TABLE IF NOT EXISTS chat_stats (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      session_id TEXT NOT NULL,
      user_id UUID,
      message_count INTEGER DEFAULT 0,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    ALTER TABLE chat_stats ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON chat_stats
      FOR SELECT USING (true);
    
    CREATE POLICY IF NOT EXISTS "Enable insert access for authenticated users" ON chat_stats
      FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    DROP TRIGGER IF EXISTS update_chat_stats_updated_at ON chat_stats;
    CREATE TRIGGER update_chat_stats_updated_at
    BEFORE UPDATE ON chat_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `
  
  try {
    const { error: chatStatsError } = await supabase.rpc('execute_sql', { sql: chatStatsSQL })
    if (chatStatsError) {
      console.error('Error creating chat_stats table:', chatStatsError)
    } else {
      console.log('Chat stats table created successfully')
    }
  } catch (err) {
    console.error('Error executing SQL for chat_stats table:', err)
    
    // Try direct SQL execution
    try {
      const { error } = await supabase.from('chat_stats').select('count').limit(1)
      if (error && error.code === '42P01') {
        console.log('Trying direct SQL execution for chat_stats table...')
        const { error: directError } = await supabase.sql(chatStatsSQL)
        if (directError) {
          console.error('Direct SQL execution failed:', directError)
        } else {
          console.log('Chat stats table created successfully via direct SQL')
        }
      }
    } catch (directErr) {
      console.error('Direct SQL execution failed:', directErr)
    }
  }
  
  // Create RPC function
  console.log('Creating create_chat_interface RPC function...')
  const rpcSQL = `
    CREATE OR REPLACE FUNCTION create_chat_interface(
      p_name text,
      p_description text,
      p_webhook_url text
    ) RETURNS jsonb
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      result jsonb;
      user_id uuid;
    BEGIN
      -- Get current authenticated user
      user_id := auth.uid();
      
      IF user_id IS NULL THEN
        RETURN jsonb_build_object(
          'success', false,
          'error', 'Not authenticated'
        );
      END IF;

      INSERT INTO chat_interfaces(name, description, "webhookUrl", createdat, user_id)
      VALUES (p_name, p_description, p_webhook_url, now(), user_id)
      RETURNING to_jsonb(chat_interfaces.*) INTO result;
      
      RETURN jsonb_build_object(
        'success', true,
        'data', result
      );
    EXCEPTION WHEN OTHERS THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM
      );
    END;
    $$;
  `
  
  try {
    const { error: rpcError } = await supabase.rpc('execute_sql', { sql: rpcSQL })
    if (rpcError) {
      console.error('Error creating RPC function:', rpcError)
    } else {
      console.log('RPC function created successfully')
    }
  } catch (err) {
    console.error('Error executing SQL for RPC function:', err)
    
    // Try direct SQL execution
    try {
      console.log('Trying direct SQL execution for RPC function...')
      const { error: directError } = await supabase.sql(rpcSQL)
      if (directError) {
        console.error('Direct SQL execution failed:', directError)
      } else {
        console.log('RPC function created successfully via direct SQL')
      }
    } catch (directErr) {
      console.error('Direct SQL execution failed:', directErr)
    }
  }
  
  console.log('Database setup completed')
}

createTables().catch(err => {
  console.error('Database setup failed:', err)
  process.exit(1)
})
