'use client';

import { useState, useEffect, useRef } from 'react';

export default function BrowserTTSPage() {
  const [text, setText] = useState('This is a test of the browser\'s built-in speech synthesis API. If you can hear this message, the API is working correctly.');
  const [voice, setVoice] = useState('');
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  
  const logRef = useRef<HTMLDivElement>(null);
  const synth = useRef<SpeechSynthesis | null>(null);
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString().split('T')[1].split('.')[0]} - ${message}`]);
  };
  
  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);
  
  useEffect(() => {
    // Initialize speech synthesis
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      synth.current = window.speechSynthesis;
      
      // Get available voices
      const getVoices = () => {
        const availableVoices = synth.current?.getVoices() || [];
        setVoices(availableVoices);
        
        if (availableVoices.length > 0 && !voice) {
          // Set default voice (prefer English voices)
          const englishVoice = availableVoices.find(v => v.lang.includes('en'));
          setVoice(englishVoice?.name || availableVoices[0].name);
        }
        
        addLog(`Loaded ${availableVoices.length} voices`);
      };
      
      // Chrome loads voices asynchronously
      if (synth.current.onvoiceschanged !== undefined) {
        synth.current.onvoiceschanged = getVoices;
      }
      
      // Initial voice loading
      getVoices();
      
      addLog('Speech synthesis initialized');
    } else {
      setError('Speech synthesis is not supported in this browser');
      addLog('Speech synthesis not supported');
    }
    
    // Cleanup
    return () => {
      if (synth.current) {
        synth.current.cancel();
      }
    };
  }, []);
  
  const handlePlay = () => {
    if (!text) {
      setError('Please enter some text to convert to speech.');
      return;
    }
    
    if (!synth.current) {
      setError('Speech synthesis is not available');
      return;
    }
    
    // Cancel any ongoing speech
    synth.current.cancel();
    
    setError(null);
    setIsPlaying(true);
    addLog(`Starting speech synthesis with voice: ${voice}`);
    
    try {
      // Create utterance
      const utterance = new SpeechSynthesisUtterance(text);
      
      // Set voice
      if (voice) {
        const selectedVoice = voices.find(v => v.name === voice);
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
      }
      
      // Event handlers
      utterance.onend = () => {
        setIsPlaying(false);
        addLog('Speech playback completed');
      };
      
      utterance.onerror = (e) => {
        setIsPlaying(false);
        setError(`Speech synthesis error: ${e.error}`);
        addLog(`Error: ${e.error}`);
      };
      
      // Start speaking
      synth.current.speak(utterance);
      addLog('Speech synthesis started');
    } catch (error: any) {
      setIsPlaying(false);
      setError(error.message || 'An error occurred during speech synthesis');
      addLog(`Error: ${error.message || 'Unknown error'}`);
    }
  };
  
  return (
    <div className="container mx-auto p-4 max-w-3xl">
      <h1 className="text-2xl font-bold mb-4">Browser Speech Synthesis Test</h1>
      
      <div className="mb-4">
        <label className="block mb-2">Text to convert to speech:</label>
        <textarea
          className="w-full p-2 border rounded"
          rows={4}
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
      </div>
      
      <div className="mb-4">
        <label className="block mb-2">Voice:</label>
        <select
          className="p-2 border rounded"
          value={voice}
          onChange={(e) => setVoice(e.target.value)}
        >
          {voices.map((v) => (
            <option key={v.name} value={v.name}>
              {v.name} ({v.lang})
            </option>
          ))}
        </select>
      </div>
      
      <div className="mb-4">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-400"
          onClick={handlePlay}
          disabled={isPlaying || !synth.current}
        >
          {isPlaying ? 'Speaking...' : 'Speak'}
        </button>
        
        <button
          className="ml-2 px-4 py-2 bg-red-500 text-white rounded disabled:bg-gray-400"
          onClick={() => synth.current?.cancel()}
          disabled={!isPlaying || !synth.current}
        >
          Stop
        </button>
      </div>
      
      {error && (
        <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">Available Voices ({voices.length})</h2>
        <div className="h-32 overflow-y-auto p-2 bg-gray-100">
          <ul>
            {voices.map((v) => (
              <li key={v.name} className={v.name === voice ? 'font-bold' : ''}>
                {v.name} - {v.lang} {v.default ? '(Default)' : ''}
              </li>
            ))}
          </ul>
        </div>
      </div>
      
      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">Logs</h2>
        <div 
          ref={logRef}
          className="h-64 overflow-y-auto p-2 bg-gray-100 font-mono text-sm"
        >
          {logs.map((log, index) => (
            <div key={index} className="mb-1">{log}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
