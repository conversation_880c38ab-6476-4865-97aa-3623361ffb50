<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Embed with Custom Chat ID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
        .debug-info pre {
            max-height: 200px;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <h1>Test Direct Embed with Custom Chat ID</h1>

    <div class="important">
        <strong>Important:</strong> This page is for testing the direct embed script with a custom chat ID. Please open the browser console (F12) to see any errors.
    </div>

    <div class="content">
        <h2>Direct Embed Script</h2>
        <p>This test uses our new direct embed script approach with a custom chat ID:</p>
        <pre>&lt;script src="https://roo-bot-fusion-kgfs.vercel.app/api/direct-embed?chatId=YOUR_CHAT_ID"&gt;&lt;/script&gt;</pre>
        
        <div class="form-group">
            <label for="chat-id">Enter Chat ID:</label>
            <input type="text" id="chat-id" placeholder="Enter your chat ID here" value="b76a3980-9f8e-47cd-ae7d-f02747552c4d">
        </div>
        
        <button id="load-chat">Load Chat Widget</button>
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <div id="script-container"></div>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Handle load chat button click
        document.getElementById('load-chat').addEventListener('click', function() {
            const chatId = document.getElementById('chat-id').value.trim();
            if (!chatId) {
                console.error('Please enter a valid chat ID');
                return;
            }
            
            // Remove any existing script
            const scriptContainer = document.getElementById('script-container');
            scriptContainer.innerHTML = '';
            
            // Create and append the new script
            const script = document.createElement('script');
            script.src = `https://roo-bot-fusion-kgfs.vercel.app/api/direct-embed?chatId=${chatId}`;
            scriptContainer.appendChild(script);
            
            console.log(`Loading chat widget with ID: ${chatId}`);
        });

        // Capture console output
        (function() {
            var oldConsoleLog = console.log;
            var oldConsoleError = console.error;
            var oldConsoleWarn = console.warn;
            var consoleOutput = document.getElementById('console-output');

            function appendToOutput(type, args) {
                var message = Array.from(args).map(arg => {
                    if (typeof arg === 'object') {
                        try {
                            return JSON.stringify(arg);
                        } catch (e) {
                            return String(arg);
                        }
                    }
                    return String(arg);
                }).join(' ');

                var pre = document.createElement('pre');
                pre.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
                pre.textContent = type.toUpperCase() + ': ' + message;
                consoleOutput.appendChild(pre);
            }

            console.log = function() {
                oldConsoleLog.apply(console, arguments);
                appendToOutput('log', arguments);
            };

            console.error = function() {
                oldConsoleError.apply(console, arguments);
                appendToOutput('error', arguments);
            };

            console.warn = function() {
                oldConsoleWarn.apply(console, arguments);
                appendToOutput('warn', arguments);
            };

            // Log any errors
            window.addEventListener('error', function(event) {
                appendToOutput('error', [event.message + ' at ' + event.filename + ':' + event.lineno]);
            });
        })();
    </script>
</body>
</html>
