// Test script for natural TTS API
async function testNaturalTTS() {
  try {
    console.log('Testing Natural TTS API...');
    
    const response = await fetch('http://localhost:3000/api/natural-tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: 'Hello, this is a test of the natural speech processing system.',
        voiceModel: 'aura-2-zeus-en',
        enableNaturalSpeech: true,
        streamResponse: false
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('audio')) {
        console.log('✓ Natural TTS API returned audio successfully');
        const arrayBuffer = await response.arrayBuffer();
        console.log('Audio size:', arrayBuffer.byteLength, 'bytes');
      } else {
        const text = await response.text();
        console.log('Response text:', text);
      }
    } else {
      const errorText = await response.text();
      console.error('✗ API Error:', response.status, errorText);
    }
  } catch (error) {
    console.error('✗ Test failed:', error);
  }
}

// Test standard TTS for comparison
async function testStandardTTS() {
  try {
    console.log('Testing Standard TTS API...');
    
    const response = await fetch('http://localhost:3000/api/deepgram-tts?' + new URLSearchParams({
      text: 'Hello, this is a test of the standard speech system.',
      voice: 'aura-2-zeus-en',
      t: Date.now()
    }));

    console.log('Response status:', response.status);

    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      console.log('✓ Standard TTS API returned audio successfully');
      console.log('Audio size:', arrayBuffer.byteLength, 'bytes');
    } else {
      const errorText = await response.text();
      console.error('✗ API Error:', response.status, errorText);
    }
  } catch (error) {
    console.error('✗ Test failed:', error);
  }
}

// Run tests
async function runTests() {
  console.log('=== TTS API Tests ===\n');
  
  await testStandardTTS();
  console.log('\n---\n');
  await testNaturalTTS();
  
  console.log('\n=== Tests Complete ===');
}

runTests();
