<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embed Script Test Updated</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Embed Script Test Updated</h1>
    
    <div class="note">
        <strong>Important:</strong> This page tests the embed script instead of directly embedding an iframe.
        Please open your browser's developer console (F12 or right-click > Inspect > Console) 
        to check for any errors during the script loading process.
    </div>
    
    <h2>What to Check For:</h2>
    <ol>
        <li><strong>Script loading errors</strong> - Look for any errors related to loading the embed script</li>
        <li><strong>X-Frame-Options errors</strong> - When the chat button is clicked, check if the iframe loads without X-Frame-Options errors</li>
        <li><strong>Content Security Policy (CSP) errors</strong> - Look for messages about CSP blocking scripts or resources</li>
        <li><strong>401 Unauthorized errors</strong> - Check if any API requests are returning 401 status codes</li>
        <li><strong>CORS errors</strong> - Look for messages about cross-origin requests being blocked</li>
    </ol>
    
    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget and check for any errors in the console.</p>
        <p>Scroll down to see more content...</p>
        
        <div style="height: 500px;"></div>
        
        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
    </div>
    
    <!-- Embed Script -->
    <script>
        (function(w, d, s, o, f, js, fjs) {
            w['BotFusion-Widget'] = o;
            w[o] = w[o] || function() {
                (w[o].q = w[o].q || []).push(arguments);
            };
            js = d.createElement(s);
            fjs = d.getElementsByTagName(s)[0];
            js.id = o;
            js.src = f;
            js.async = 1;
            fjs.parentNode.insertBefore(js, fjs);
        }(window, document, 'script', 'bf', 'https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/api/embed-script'));
        
        bf('init', {
            chatId: '1',
            position: 'right',
            primaryColor: '#3b82f6',
            greeting: 'Hello! How can I help you today?'
        });
    </script>
    
    <!-- Error Tracking -->
    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });
        
        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all scripts and resources');
        });
    </script>
</body>
</html>
