/**
 * Mobile-Optimized Chat Widget Embed
 * 
 * Fixes critical mobile issues:
 * - Touch event blocking on parent page
 * - Scrolling prevention 
 * - Button interaction blocking
 * - Navigation menu issues
 * - Form input problems
 * 
 * Universal compatibility: WordPress, Durable.co, Shopify, custom sites
 */

(function() {
  'use strict';
  
  // Configuration
  const config = {
    chatId: window.BOTFUSION_CHAT_ID || 'default',
    baseUrl: window.BOTFUSION_BASE_URL || 'https://roo-bot-fusion-kgfs.vercel.app',
    primaryColor: window.BOTFUSION_PRIMARY_COLOR || '#3b82f6',
    mobileBreakpoint: 768, // Use 768px instead of 480px for better mobile detection
    debug: window.BOTFUSION_DEBUG || false
  };

  // Debug logging
  function log(...args) {
    if (config.debug) {
      console.log('[BotFusion Mobile]', ...args);
    }
  }

  // Mobile detection
  function isMobile() {
    return window.innerWidth <= config.mobileBreakpoint || 
           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  // Prevent duplicate initialization
  if (window.BotFusionMobileWidget) {
    log('Widget already initialized');
    return;
  }

  // Universal compatibility checks and fixes
  function ensureCompatibility() {
    // Fix for WordPress and other CMS that might interfere
    if (typeof jQuery !== 'undefined' && jQuery.fn.ready) {
      // Ensure our widget loads after jQuery is ready
      jQuery(document).ready(function() {
        log('jQuery detected, ensuring compatibility');
      });
    }

    // Fix for Durable.co and similar page builders
    if (window.location.hostname.includes('durable.co') ||
        document.querySelector('[data-durable]') ||
        document.querySelector('.durable-')) {
      log('Durable.co detected, applying compatibility fixes');
      // Durable.co specific fixes
      document.body.style.position = 'relative';
    }

    // Fix for Shopify themes
    if (window.Shopify || document.querySelector('[data-shopify]')) {
      log('Shopify detected, applying compatibility fixes');
      // Shopify specific fixes
    }

    // Fix for Squarespace
    if (window.Static && window.Static.SQUARESPACE_CONTEXT) {
      log('Squarespace detected, applying compatibility fixes');
      // Squarespace specific fixes
    }

    // Fix for Wix
    if (window.wixDevelopersAnalytics || document.querySelector('[data-wix-editor]')) {
      log('Wix detected, applying compatibility fixes');
      // Wix specific fixes
    }

    // Generic CMS compatibility
    const commonCMSSelectors = [
      '.wp-site-blocks', // WordPress
      '.elementor-page', // Elementor
      '.fl-page', // Beaver Builder
      '.vc_row', // Visual Composer
      '.fusion-page', // Avada
      '.et_pb_section' // Divi
    ];

    for (const selector of commonCMSSelectors) {
      if (document.querySelector(selector)) {
        log(`CMS detected via selector: ${selector}`);
        // Apply generic CMS fixes
        break;
      }
    }
  }

  // Run compatibility checks
  ensureCompatibility();

  // Create mobile-optimized styles
  const styles = document.createElement('style');
  styles.textContent = `
    /* Mobile-Optimized Chat Widget Styles */
    #botfusion-mobile-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: ${config.primaryColor};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      z-index: 9998; /* Lower than overlay */
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: none;
      outline: none;
      -webkit-tap-highlight-color: transparent;
    }

    #botfusion-mobile-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    #botfusion-mobile-button svg {
      width: 24px;
      height: 24px;
      fill: white;
      pointer-events: none;
    }

    /* Mobile Chat Container - NO OVERLAY NEEDED */
    #botfusion-mobile-chat {
      position: fixed;
      background: transparent;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: transform 0.3s ease, opacity 0.3s ease;
      /* CRITICAL: Prevent touch events from affecting parent */
      touch-action: manipulation;
      -webkit-overflow-scrolling: touch;
      display: none;
      opacity: 0;
      z-index: 9999;
    }

    #botfusion-mobile-chat.visible {
      display: block;
      opacity: 1;
    }

    /* Desktop and Tablet styles - Position like normal chat widget */
    @media (min-width: ${config.mobileBreakpoint + 1}px) {
      #botfusion-mobile-chat {
        bottom: 20px; /* Align with chat button at bottom-right */
        right: 20px;
        width: 400px;
        height: 600px;
        border-radius: 12px;
        max-width: 90vw;
        max-height: 80vh;
        transform: translateY(0);
      }
    }

    /* Mobile styles - Responsive height that adapts to viewport and keyboard */
    @media (max-width: ${config.mobileBreakpoint}px) {
      #botfusion-mobile-chat {
        bottom: 20px; /* Align with chat button at bottom-right */
        right: 20px;
        left: 20px;
        width: auto;
        height: min(500px, calc(100dvh - 120px)); /* Use dynamic viewport height minus space for button and padding */
        max-height: min(500px, calc(100vh - 120px)); /* Fallback for browsers without dvh support */
        border-radius: 12px;
        transform: translateY(0);
      }

      /* When keyboard is likely open (very small height), make widget smaller */
      @media (max-height: 500px) {
        #botfusion-mobile-chat {
          height: calc(100dvh - 100px) !important;
          max-height: calc(100vh - 100px) !important;
          bottom: 10px !important;
        }
      }
    }

    /* Chat iframe styles */
    #botfusion-mobile-iframe {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: inherit;
      background: transparent;
    }



    /* Close button */
    #botfusion-mobile-close {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.1);
      border: none;
      cursor: pointer;
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease;
      -webkit-tap-highlight-color: transparent;
    }

    #botfusion-mobile-close:hover {
      background: rgba(0, 0, 0, 0.2);
    }

    #botfusion-mobile-close svg {
      width: 16px;
      height: 16px;
      fill: #666;
    }

    /* Accessibility improvements */
    @media (prefers-reduced-motion: reduce) {
      #botfusion-mobile-button,
      #botfusion-mobile-overlay,
      #botfusion-mobile-chat {
        transition: none;
      }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      #botfusion-mobile-button {
        border: 2px solid white;
      }
      
      #botfusion-mobile-chat {
        border: 2px solid black;
      }
    }
  `;
  document.head.appendChild(styles);

  // Create chat button
  const button = document.createElement('button');
  button.id = 'botfusion-mobile-button';
  button.setAttribute('aria-label', 'Open chat');
  button.setAttribute('type', 'button');
  button.innerHTML = `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
    </svg>
  `;

  // Create chat container (NO OVERLAY NEEDED)
  const chatContainer = document.createElement('div');
  chatContainer.id = 'botfusion-mobile-chat';
  chatContainer.setAttribute('role', 'dialog');
  chatContainer.setAttribute('aria-modal', 'true');
  chatContainer.setAttribute('aria-label', 'Chat widget');

  // Create close button
  const closeButton = document.createElement('button');
  closeButton.id = 'botfusion-mobile-close';
  closeButton.setAttribute('aria-label', 'Close chat');
  closeButton.setAttribute('type', 'button');
  closeButton.innerHTML = `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  `;

  // Create iframe
  const iframe = document.createElement('iframe');
  iframe.id = 'botfusion-mobile-iframe';
  iframe.setAttribute('allow', 'microphone; autoplay');
  iframe.setAttribute('title', 'Chat Widget');
  iframe.setAttribute('loading', 'lazy');
  iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox');

  // Assemble the widget
  chatContainer.appendChild(closeButton);
  chatContainer.appendChild(iframe);

  // State management
  let isOpen = false;
  let isInitialized = false;

  // Touch event management for mobile
  function manageTouchEvents(enable) {
    if (!isMobile()) return;
    
    if (enable) {
      // Prevent parent page scrolling when chat is open
      document.body.classList.add('botfusion-chat-open');
      document.body.style.top = `-${window.scrollY}px`;
    } else {
      // Restore parent page scrolling
      document.body.classList.remove('botfusion-chat-open');
      const scrollY = document.body.style.top;
      document.body.style.top = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }
  }

  // Open chat function
  function openChat() {
    if (isOpen) return;

    log('Opening chat');
    isOpen = true;

    // Initialize iframe source on first open
    if (!isInitialized) {
      const params = new URLSearchParams({
        mobile: isMobile() ? '1' : '0',
        embed: '1',
        t: Date.now().toString()
      });

      // Use chat-only mode for the iframe (no button, just chat interface)
      params.set('mode', 'chat-only');
      iframe.src = `${config.baseUrl}/embed/${config.chatId}?${params.toString()}`;
      isInitialized = true;
    }

    // Show chat
    chatContainer.classList.add('visible');

    // Manage touch events for mobile
    manageTouchEvents(true);

    // Update mobile height
    updateMobileHeight();

    // Hide button
    button.style.display = 'none';

    // Accessibility: Announce to screen readers
    announceToScreenReader('Chat widget opened');

    // Focus management for accessibility
    setTimeout(() => {
      closeButton.focus();
    }, 300);
  }

  // Close chat function
  function closeChat() {
    if (!isOpen) return;

    log('Closing chat');
    isOpen = false;

    // Hide chat
    chatContainer.classList.remove('visible');

    // Restore touch events for mobile
    manageTouchEvents(false);

    // Show button
    button.style.display = 'flex';

    // Accessibility: Announce to screen readers
    announceToScreenReader('Chat widget closed');

    // Focus management for accessibility
    button.focus();
  }

  // Event listeners
  button.addEventListener('click', openChat);
  closeButton.addEventListener('click', closeChat);

  // Close on overlay click (but not on chat container)
  overlay.addEventListener('click', function(e) {
    if (e.target === overlay) {
      closeChat();
    }
  });

  // Touch zone click handler for mobile
  if (isMobile()) {
    touchZone.addEventListener('click', closeChat);
  }

  // Listen for messages from iframe
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      closeChat();
    }
  });

  // Dynamic height calculation for mobile
  function updateMobileHeight() {
    if (!isMobile() || !isOpen) return;

    const viewportHeight = window.innerHeight;
    const isKeyboardOpen = viewportHeight < 500; // Likely keyboard is open

    if (isKeyboardOpen) {
      // Keyboard is open - make widget smaller and position higher
      chatContainer.style.height = `${Math.max(300, viewportHeight - 100)}px`;
      chatContainer.style.bottom = '10px';
    } else {
      // Normal mobile view
      const maxHeight = Math.min(500, viewportHeight - 120);
      chatContainer.style.height = `${maxHeight}px`;
      chatContainer.style.bottom = '20px';
    }

    log('Updated mobile height:', {
      viewportHeight,
      isKeyboardOpen,
      chatHeight: chatContainer.style.height
    });
  }

  // Handle resize events
  window.addEventListener('resize', function() {
    // Update mobile detection on resize
    const wasMobile = chatContainer.querySelector('#botfusion-mobile-touch-zone') !== null;
    const nowMobile = isMobile();

    if (wasMobile !== nowMobile) {
      // Mobile state changed, update touch zone
      if (nowMobile && !wasMobile) {
        chatContainer.appendChild(touchZone);
      } else if (!nowMobile && wasMobile) {
        touchZone.remove();
      }
    }

    // Update mobile height on resize (including keyboard open/close)
    updateMobileHeight();
  });

  // Enhanced keyboard accessibility
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isOpen) {
      closeChat();
    }

    // Tab trapping when chat is open
    if (isOpen && e.key === 'Tab') {
      const focusableElements = chatContainer.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }
  });

  // ARIA live region for screen readers
  const ariaLiveRegion = document.createElement('div');
  ariaLiveRegion.setAttribute('aria-live', 'polite');
  ariaLiveRegion.setAttribute('aria-atomic', 'true');
  ariaLiveRegion.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
  document.body.appendChild(ariaLiveRegion);

  // Announce widget state changes to screen readers
  function announceToScreenReader(message) {
    ariaLiveRegion.textContent = message;
    setTimeout(() => {
      ariaLiveRegion.textContent = '';
    }, 1000);
  }

  // Performance optimization: Intersection Observer for button visibility
  let buttonObserver;
  if ('IntersectionObserver' in window) {
    buttonObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Button is visible, ensure it's interactive
          button.style.pointerEvents = 'auto';
        } else {
          // Button is not visible, reduce interactions
          button.style.pointerEvents = 'none';
        }
      });
    });
    buttonObserver.observe(button);
  }

  // Performance optimization: Reduce motion for users who prefer it
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  if (prefersReducedMotion) {
    // Disable animations for users who prefer reduced motion
    const style = document.createElement('style');
    style.textContent = `
      #botfusion-mobile-button,
      #botfusion-mobile-overlay,
      #botfusion-mobile-chat {
        transition: none !important;
        animation: none !important;
      }
    `;
    document.head.appendChild(style);
  }

  // Add elements to DOM
  document.body.appendChild(button);
  document.body.appendChild(chatContainer);

  // Expose API
  window.BotFusionMobileWidget = {
    open: openChat,
    close: closeChat,
    isOpen: () => isOpen,
    isMobile: isMobile,
    version: '2.0.0-mobile-optimized'
  };

  // Advanced touch event delegation for mobile
  function setupTouchEventDelegation() {
    if (!isMobile()) return;

    let touchStartY = 0;
    let touchStartX = 0;

    // Handle touch events on the chat container
    chatContainer.addEventListener('touchstart', function(e) {
      touchStartY = e.touches[0].clientY;
      touchStartX = e.touches[0].clientX;
    }, { passive: false });

    // Prevent default touch behavior on chat container to avoid conflicts
    chatContainer.addEventListener('touchmove', function(e) {
      e.stopPropagation();
    }, { passive: true });

    // Handle swipe down to close on mobile
    chatContainer.addEventListener('touchend', function(e) {
      const touchEndY = e.changedTouches[0].clientY;
      const deltaY = touchEndY - touchStartY;

      // Swipe down gesture to close (only if swipe is significant)
      if (deltaY > 100 && Math.abs(e.changedTouches[0].clientX - touchStartX) < 100) {
        closeChat();
      }
    }, { passive: true });
  }

  // Initialize touch event delegation
  setupTouchEventDelegation();

  log('Mobile-optimized widget initialized', {
    chatId: config.chatId,
    isMobile: isMobile(),
    version: window.BotFusionMobileWidget.version
  });

})();
