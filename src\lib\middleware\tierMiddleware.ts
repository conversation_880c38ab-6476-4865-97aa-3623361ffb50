// Tier-based access control middleware for BotFusion X
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { UserTier, TIER_CONFIGS } from '@/lib/tiers'

export interface TierCheckResult {
  allowed: boolean
  userTier: UserTier
  reason?: string
  upgradeRequired?: UserTier
}

// Middleware to check tier permissions for API routes
export async function checkTierPermission(
  request: NextRequest,
  requiredFeature: string,
  minimumTier?: UserTier
): Promise<TierCheckResult> {
  try {
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return {
        allowed: false,
        userTier: 'free',
        reason: 'Authentication required'
      }
    }

    // Get user profile with tier information
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('tier, subscription_status')
      .eq('id', user.id)
      .single()

    let userTier: UserTier = 'free'

    if (profileError || !profile) {
      // Create default profile if it doesn't exist
      const { error: createError } = await supabase
        .from('user_profiles')
        .insert({
          id: user.id,
          tier: 'free',
          subscription_status: 'active'
        })

      if (createError) {
        console.error('Error creating user profile:', createError)
      }
    } else {
      userTier = profile.tier as UserTier
      
      // Check if subscription is active
      if (profile.subscription_status !== 'active') {
        return {
          allowed: false,
          userTier,
          reason: 'Subscription not active'
        }
      }
    }

    // Check specific feature access
    const tierConfig = TIER_CONFIGS[userTier]
    const hasFeatureAccess = checkFeatureAccess(tierConfig, requiredFeature)

    if (!hasFeatureAccess) {
      // Determine minimum tier required for this feature
      const requiredTier = getMinimumTierForFeature(requiredFeature)
      
      return {
        allowed: false,
        userTier,
        reason: `Feature requires ${requiredTier} tier or higher`,
        upgradeRequired: requiredTier
      }
    }

    // Check minimum tier requirement if specified
    if (minimumTier && !isTierSufficient(userTier, minimumTier)) {
      return {
        allowed: false,
        userTier,
        reason: `Minimum ${minimumTier} tier required`,
        upgradeRequired: minimumTier
      }
    }

    return {
      allowed: true,
      userTier
    }

  } catch (error) {
    console.error('Error checking tier permission:', error)
    return {
      allowed: false,
      userTier: 'free',
      reason: 'Internal error'
    }
  }
}

// Check if user has access to a specific feature
function checkFeatureAccess(tierConfig: any, feature: string): boolean {
  switch (feature) {
    case 'voice_features':
      return tierConfig.voiceFeaturesEnabled
    case 'branding_removal':
      return tierConfig.brandingRemoval
    case 'white_labeling':
      return tierConfig.whiteLabelingEnabled
    case 'custom_branding':
      return tierConfig.customBrandingText
    case 'advanced_customization':
      return tierConfig.advancedCustomization
    case 'analytics_dashboard':
      return tierConfig.analyticsDashboard
    case 'priority_support':
      return tierConfig.prioritySupport
    case 'unlimited_interfaces':
      return tierConfig.maxChatInterfaces === -1
    default:
      return true // Allow unknown features by default
  }
}

// Get minimum tier required for a feature
function getMinimumTierForFeature(feature: string): UserTier {
  switch (feature) {
    case 'voice_features':
    case 'branding_removal':
    case 'advanced_customization':
    case 'unlimited_interfaces':
      return 'standard'
    case 'white_labeling':
    case 'custom_branding':
    case 'analytics_dashboard':
    case 'priority_support':
      return 'pro'
    default:
      return 'free'
  }
}

// Check if user tier is sufficient for requirement
function isTierSufficient(userTier: UserTier, requiredTier: UserTier): boolean {
  const tierOrder: UserTier[] = ['free', 'standard', 'pro']
  const userIndex = tierOrder.indexOf(userTier)
  const requiredIndex = tierOrder.indexOf(requiredTier)
  
  return userIndex >= requiredIndex
}

// Middleware wrapper for API routes
export function withTierCheck(
  handler: (req: NextRequest, tierResult: TierCheckResult) => Promise<NextResponse>,
  requiredFeature: string,
  minimumTier?: UserTier
) {
  return async (req: NextRequest) => {
    const tierResult = await checkTierPermission(req, requiredFeature, minimumTier)
    
    if (!tierResult.allowed) {
      return NextResponse.json(
        {
          error: 'Access denied',
          reason: tierResult.reason,
          userTier: tierResult.userTier,
          upgradeRequired: tierResult.upgradeRequired
        },
        { status: 403 }
      )
    }
    
    return handler(req, tierResult)
  }
}

// Check chat interface creation limit
export async function checkChatInterfaceLimit(userId: string): Promise<TierCheckResult> {
  try {
    const supabase = createClient()
    
    // Get user tier
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tier')
      .eq('id', userId)
      .single()

    const userTier: UserTier = profile?.tier || 'free'
    const tierConfig = TIER_CONFIGS[userTier]

    // If unlimited, allow creation
    if (tierConfig.maxChatInterfaces === -1) {
      return { allowed: true, userTier }
    }

    // Count current interfaces
    const { count, error } = await supabase
      .from('chat_interfaces')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    if (error) {
      console.error('Error counting chat interfaces:', error)
      return {
        allowed: false,
        userTier,
        reason: 'Error checking interface count'
      }
    }

    const currentCount = count || 0
    const allowed = currentCount < tierConfig.maxChatInterfaces

    return {
      allowed,
      userTier,
      reason: allowed ? undefined : `Maximum ${tierConfig.maxChatInterfaces} chat interfaces allowed for ${userTier} tier`,
      upgradeRequired: allowed ? undefined : 'standard'
    }

  } catch (error) {
    console.error('Error checking chat interface limit:', error)
    return {
      allowed: false,
      userTier: 'free',
      reason: 'Internal error'
    }
  }
}

// Check voice model access
export async function checkVoiceModelAccess(userId: string, voiceModel: string): Promise<TierCheckResult> {
  try {
    const supabase = createClient()
    
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tier')
      .eq('id', userId)
      .single()

    const userTier: UserTier = profile?.tier || 'free'
    const tierConfig = TIER_CONFIGS[userTier]

    // Check if voice features are enabled
    if (!tierConfig.voiceFeaturesEnabled) {
      return {
        allowed: false,
        userTier,
        reason: 'Voice features not available for free tier',
        upgradeRequired: 'standard'
      }
    }

    // Check if specific voice model is available
    const hasModelAccess = tierConfig.voiceModels.includes(voiceModel)

    return {
      allowed: hasModelAccess,
      userTier,
      reason: hasModelAccess ? undefined : `Voice model '${voiceModel}' requires ${userTier === 'standard' ? 'pro' : 'standard'} tier`,
      upgradeRequired: hasModelAccess ? undefined : (userTier === 'standard' ? 'pro' : 'standard')
    }

  } catch (error) {
    console.error('Error checking voice model access:', error)
    return {
      allowed: false,
      userTier: 'free',
      reason: 'Internal error'
    }
  }
}
