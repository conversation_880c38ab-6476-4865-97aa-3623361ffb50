'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'

export default function ClientOnlyDirectEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  const directEmbedCode = `<script src="${baseUrl}/api/direct-embed?chatId=${chatId}"></script>`

  const handleCopy = () => {
    navigator.clipboard.writeText(directEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-white">Direct Embed Code</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs"
          >
            {copied ? "Copied" : "Copy"}
          </Button>
        </div>
        <p className="text-sm text-gray-400">
          Add this script to your website to embed the chat widget. This is the most reliable method and works with strict Content Security Policies.
        </p>
      </div>
      <div className="relative">
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300">
            <code>{directEmbedCode}</code>
          </pre>
        </div>
      </div>
    </div>
  )
}
