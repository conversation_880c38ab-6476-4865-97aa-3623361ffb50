import React, { useEffect, useRef, useState, useCallback } from 'react';
import SimpleAudioPlayer from './SimpleAudioPlayer';
import StreamingTTS from './StreamingTTS';
import { MAX_TTS_TEXT_LENGTH } from '@/lib/tts-utils';

// Define Deepgram voice types - Updated for Aura voices
export type DeepgramVoice = 'thalia' | 'asteria' | 'helena' | 'arcas' | 'apollo' | 'zeus' | 'nova' | 'alloy' | 'echo' | 'fable' | 'onyx' | 'shimmer';

interface TextToSpeechProps {
  text: string;
  autoPlay?: boolean;
  voice?: DeepgramVoice;
  onStart?: () => void;
  onEnd?: () => void;
  disabled?: boolean;
  microphoneActive?: boolean;
  onAudioReady?: (messageId?: string) => void;
  messageId?: string; // Add messageId prop to use original bot message ID
  enableNaturalSpeech?: boolean; // Add natural speech processing option
}

const TextToSpeech: React.FC<TextToSpeechProps> = ({
  text,
  autoPlay = false,
  voice = 'thalia', // Changed to <PERSON>halia for female voice
  onStart,
  onEnd,
  disabled = false,
  microphoneActive = false,
  onAudioReady,
  messageId: providedMessageId, // Accept messageId prop
  enableNaturalSpeech = true, // Enable natural speech processing by default
}) => {
  const micActiveRef = useRef(microphoneActive);
  const lastProcessedText = useRef('');
  const [ttsError, setTtsError] = useState<string | null>(null);
  const [isProcessingLongText, setIsProcessingLongText] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [useStreamingTTS, setUseStreamingTTS] = useState(false);

  // Add unique instance ID to track multiple TextToSpeech components
  const instanceId = useRef(`tts-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);

  // Global state to prevent multiple TTS components from running simultaneously
  const globalTTSLock = useRef<string | null>(null);

// True global processing mode lock (outside component scope)
let globalProcessingMode: boolean | null = null;
  // Track audio ready callbacks by message ID instead of a single boolean
  const audioReadyCalledRef = useRef<{[messageId: string]: boolean}>({});
  // Store all timeouts for proper cleanup
  const timeoutsRef = useRef<{[key: string]: NodeJS.Timeout}>({});

  // Track if we've already processed this text with this microphone state
  const processedWithMicState = useRef<{text: string, micActive: boolean, timestamp: number} | null>(null);
  const initialRenderRef = useRef(true);

  // Track whether the current message originated from a voice session for autoplay decisions
  const voiceSessionOriginRef = useRef<{[messageId: string]: boolean}>({});

  // Update the ref when the prop changes
  useEffect(() => {
    // Skip logging on initial render to reduce console noise
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      return;
    }

    // Only log if the state actually changed
    if (micActiveRef.current !== microphoneActive) {
      console.log('Microphone active state changed:', microphoneActive);
      micActiveRef.current = microphoneActive;
    }
  }, [microphoneActive]);

  // Ultra-Fast TTS Selection Logic - WebSocket Streaming for Immediate Audio Start
  // Always use WebSocket streaming TTS for fastest possible audio response
  useEffect(() => {
    // OPTIMIZATION: Always use WebSocket streaming for ultra-fast audio start (< 1 second)
    const shouldUseWebSocketStreaming = true; // Enable ultra-fast WebSocket streaming TTS

    console.log(`[${instanceId.current}] TTS Selection Logic: microphoneActive=${microphoneActive}, useStreamingTTS=${useStreamingTTS}, shouldUseWebSocketStreaming=${shouldUseWebSocketStreaming}`);

    if (shouldUseWebSocketStreaming !== useStreamingTTS) {
      console.log(`[${instanceId.current}] TTS Mode Switch: ${shouldUseWebSocketStreaming ? '⚡ WebSocket Streaming (Ultra-Fast)' : '📡 REST'} (micActive: ${microphoneActive}, autoPlay: ${autoPlay})`);
      setUseStreamingTTS(shouldUseWebSocketStreaming);
    }
  }, [microphoneActive, autoPlay, useStreamingTTS]);

  // Only process text when:
  // 1. There is text to process
  // 2. We're not already processing
  // We no longer require microphone to be active - we want TTS to work regardless
  const shouldProcessText = text && !isProcessing;

  // Store the last processed text with timestamp to prevent duplicate processing
  const lastProcessedStateRef = useRef<{text: string, timestamp: number, messageId: string} | null>(null);
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Generate a unique ID for each text to track it, but prefer provided messageId
  const getMessageId = useCallback((text: string) => {
    // Use provided messageId if available, otherwise generate one
    return providedMessageId || `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }, [providedMessageId]);

  // Function to set a timeout with automatic tracking for cleanup
  const setManagedTimeout = useCallback((callback: () => void, delay: number, timeoutKey: string) => {
    // Clear any existing timeout with this key
    if (timeoutsRef.current[timeoutKey]) {
      clearTimeout(timeoutsRef.current[timeoutKey]);
      delete timeoutsRef.current[timeoutKey];
    }

    // Set the new timeout and store it in the ref
    const timeoutId = setTimeout(() => {
      // Remove the timeout from the ref when it completes
      delete timeoutsRef.current[timeoutKey];

      // Call the callback
      callback();
    }, delay);

    // Store the timeout ID in the ref
    timeoutsRef.current[timeoutKey] = timeoutId;

    return timeoutId;
  }, []);

  // Function to clear all timeouts
  const clearAllTimeouts = useCallback(() => {
    Object.values(timeoutsRef.current).forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    timeoutsRef.current = {};
  }, []);

  // Update the last processed text when it changes
  useEffect(() => {
    // Add logging here to debug the hook call issue
    console.log('TextToSpeech useEffect triggered');
    console.log('Dependencies:', {
      text: text.substring(0, 50) + '...', // Log truncated text
      onStart: typeof onStart,
      microphoneActive,
      isProcessing,
      onAudioReady: typeof onAudioReady,
      getMessageId: typeof getMessageId,
    });
    console.log('Conditions:', {
      hasText: !!text,
      notProcessing: !isProcessing,
      shouldProcessText: text && !isProcessing,
    });

    // If text is empty (chat closed), clear all state
    if (!text || text.trim() === '') {
      console.log('TextToSpeech: Empty text detected, clearing all state');
      clearAllTimeouts();
      audioReadyCalledRef.current = {};
      lastProcessedText.current = '';
      setIsProcessing(false);
      setIsProcessingLongText(false);
      setTtsError(null);

      // CRITICAL FIX: Do NOT call onAudioReady when there's no text content
      // This prevents premature microphone reset when STT is actively listening
      console.log('TextToSpeech: Skipping onAudioReady callback for empty text to prevent microphone interference');
      return;
    }

    // Clear all existing timeouts to prevent race conditions
    clearAllTimeouts();

    // Also clear the legacy timeout for backward compatibility
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }

    // Skip if there's no text
    if (!text) {
      return;
    }

    // Generate a unique message ID for this text
    const messageId = getMessageId(text);
    console.log(`Processing message with ID: ${messageId}`);

    // Reset audio ready state for this new message
    // This ensures we don't skip calling onAudioReady for new messages
    if (audioReadyCalledRef.current[messageId] === undefined) {
      audioReadyCalledRef.current[messageId] = false;
    }

    // Add more granular logging for state values
    console.log('Checking processing conditions:', { isProcessing, microphoneActive });

    // Check if we've already processed this exact text very recently (within 1 second)
    // This is a much shorter window than before to prevent legitimate repeated messages from being skipped
    const now = Date.now();
    const timeSinceLastProcess = lastProcessedStateRef.current ? (now - lastProcessedStateRef.current.timestamp) : Infinity;
    const isDuplicate = lastProcessedStateRef.current &&
                        lastProcessedStateRef.current.text === text &&
                        timeSinceLastProcess < 1000;

    if (isDuplicate) {
      console.log('Detected duplicate text processing attempt within 1 second, skipping:', text.substring(0, 30) + '...');
      console.log(`Time since last process: ${timeSinceLastProcess}ms`);

      // Only call onAudioReady for duplicates if there's actual text content
      // This prevents calling onAudioReady for empty text duplicates
      if (text && text.trim()) {
        // Even when skipping, ensure we call onAudioReady to reveal the message
        // But add a small delay to ensure any previous audio has time to complete
        setManagedTimeout(() => {
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
            console.log(`Calling onAudioReady for message ${messageId} despite skipping to ensure UI updates`);
            handleAudioReady(messageId);
          }
        }, 500, `skip-delay-${messageId}`);
      } else {
        console.log('Skipping onAudioReady for duplicate empty text to prevent microphone interference');
      }

      return;
    }

    // Check if another TTS instance is already processing
    if (globalTTSLock.current && globalTTSLock.current !== instanceId.current) {
      console.log(`[${instanceId.current}] Another TTS instance (${globalTTSLock.current}) is processing, skipping`);

      // Only call onAudioReady if there's actual text content
      if (text && text.trim()) {
        // Even when skipping, ensure we call onAudioReady to reveal the message after a delay
        setManagedTimeout(() => {
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
            console.log(`[${instanceId.current}] Calling onAudioReady for message ${messageId} after global lock timeout`);
            handleAudioReady(messageId);
          }
        }, 2000, `global-lock-delay-${messageId}`);
      } else {
        console.log(`[${instanceId.current}] Skipping onAudioReady for empty text during global lock to prevent microphone interference`);
      }

      return;
    }

    // If we're already processing, don't start again
    if (isProcessing) {
      console.log(`[${instanceId.current}] Already processing TTS, skipping new request`);

      // Only call onAudioReady if there's actual text content
      if (text && text.trim()) {
        // Even when skipping, ensure we call onAudioReady to reveal the message after a delay
        setManagedTimeout(() => {
          if (onAudioReady && !audioReadyCalledRef.current[messageId]) {
            console.log(`[${instanceId.current}] Calling onAudioReady for message ${messageId} after timeout to ensure UI updates`);
            handleAudioReady(messageId);
          }
        }, 2000, `processing-delay-${messageId}`);
      } else {
        console.log(`[${instanceId.current}] Skipping onAudioReady for empty text during processing to prevent microphone interference`);
      }

      return;
    }

    // Update the last processed state
    lastProcessedStateRef.current = {
      text,
      timestamp: now,
      messageId
    };

    console.log(`[${instanceId.current}] Processing message with messageId: ${messageId}, backup timeout will use: ${messageId}`);

    // Prevent infinite loops by limiting the number of consecutive TTS calls
    // If we've processed more than 5 messages in the last 5 seconds, something might be wrong
    const recentMessages = [];

    // Get all message states from the last 5 seconds
    const fiveSecondsAgo = now - 5000;
    // We can't easily track all processed messages, so we'll use a heuristic
    // If the current message timestamp is very close to the last one, count it as suspicious
    if (lastProcessedStateRef.current && lastProcessedStateRef.current.timestamp > fiveSecondsAgo) {
      recentMessages.push(lastProcessedStateRef.current);

      // If the time between messages is very short (less than 200ms), this might be an infinite loop
      if (now - lastProcessedStateRef.current.timestamp < 200) {
        // Add extra "virtual" messages to trigger the loop detection
        recentMessages.push({...lastProcessedStateRef.current});
        recentMessages.push({...lastProcessedStateRef.current});
      }
    }

    // Add the current message
    recentMessages.push({text, timestamp: now, messageId});

    // Check if we have too many messages in a short period
    if (recentMessages.length > 5) {
      console.warn('Too many TTS requests in a short period, possible infinite loop detected');
      console.warn('Recent messages:', recentMessages.length);

      // Only call onAudioReady if there's actual text content
      if (text && text.trim() && onAudioReady && !audioReadyCalledRef.current[messageId]) {
        console.log(`Breaking potential infinite loop by calling onAudioReady for message ${messageId}`);
        handleAudioReady(messageId);
        setIsProcessing(false);
      } else {
        console.log('Skipping onAudioReady for empty text during loop detection to prevent microphone interference');
        setIsProcessing(false);
      }

      // Add a cooldown period to prevent further processing for a short time
      const cooldownPeriod = 5000; // 5 seconds
      console.warn(`Entering cooldown period for ${cooldownPeriod}ms to break potential infinite loop`);

      return;
    }

    console.log(`[${instanceId.current}] New bot message detected:`, text.substring(0, 30) + '...');
    console.log(`[${instanceId.current}] Current microphone state:`, microphoneActive);

    // Acquire global lock to prevent other TTS instances from processing
    globalTTSLock.current = instanceId.current;
    console.log(`[${instanceId.current}] Acquired global TTS lock`);

    // Lock the current TTS mode globally to prevent component switching during processing
    globalProcessingMode = useStreamingTTS;
    console.log(`[${instanceId.current}] Locked global processing mode: ${useStreamingTTS ? '⚡ WebSocket LINEAR16+WAV' : '📡 REST'}`);

    // Track whether this message originated from a voice session for autoplay decisions
    voiceSessionOriginRef.current[messageId] = micActiveRef.current;
    console.log(`[${instanceId.current}] Message ${messageId} voice session origin: ${micActiveRef.current}`);

    // Set processing state to prevent duplicate processing
    setIsProcessing(true);

    // Reset audio ready flag for this specific message
    audioReadyCalledRef.current[messageId] = false;

    // Clear any previous errors when processing new text
    setTtsError(null);

    // Check if this is a long text that will be processed in chunks
    if (text.length > MAX_TTS_TEXT_LENGTH) {
      setIsProcessingLongText(true);

      // Call onStart if provided
      if (onStart) {
        onStart();
      }
    } else {
      setIsProcessingLongText(false);
    }

    // CRITICAL FIX: Use the same messageId that will be passed to StreamingTTS/SimpleAudioPlayer
    const actualMessageId = lastProcessedStateRef.current?.messageId || getMessageId(text);

    // Set a backup timeout to ensure onAudioReady is called even if something goes wrong
    // Reduced from 5000ms to 3000ms to break potential loops faster
    setManagedTimeout(() => {
      if (!audioReadyCalledRef.current[actualMessageId] && onAudioReady) {
        console.log(`Backup timeout triggered, calling onAudioReady for message ${actualMessageId}`);
        handleAudioReady(actualMessageId);
        setIsProcessing(false);
      } else if (audioReadyCalledRef.current[actualMessageId]) {
        console.log(`Backup timeout skipped - onAudioReady already called for message ${actualMessageId}`);
      }
    }, 3000, `backup-${actualMessageId}`);
  }, [text, microphoneActive, onAudioReady, getMessageId]);

  // Handle TTS errors
  const handleTtsError = (errorMessage: string) => {
    console.warn(`[${instanceId.current}] TTS Error:`, errorMessage);
    setTtsError(errorMessage);
    setIsProcessingLongText(false);
    setIsProcessing(false);

    // Release global lock on error
    if (globalTTSLock.current === instanceId.current) {
      globalTTSLock.current = null;
      console.log(`[${instanceId.current}] Released global TTS lock due to error`);
    }

    // Release global processing mode lock on error
    if (globalProcessingMode !== null) {
      globalProcessingMode = null;
      console.log(`[${instanceId.current}] Released global processing mode lock due to error`);
    }

    // If there's an onEnd callback, call it to signal that TTS processing is done (with error)
    if (onEnd) {
      onEnd();
    }
  };

  // Handle audio ready callback
  const handleAudioReady = useCallback((messageId?: string) => {
    console.log(`[${instanceId.current}] Audio ready callback received`);
    setIsProcessing(false);

    // Release global lock when audio is ready
    if (globalTTSLock.current === instanceId.current) {
      globalTTSLock.current = null;
      console.log(`[${instanceId.current}] Released global TTS lock after audio ready`);
    }

    // Release global processing mode lock when audio is ready
    if (globalProcessingMode !== null) {
      globalProcessingMode = null;
      console.log(`[${instanceId.current}] Released global processing mode lock after audio ready`);
    }

    // Use the provided messageId or get it from the last processed state
    const msgId = messageId || (lastProcessedStateRef.current?.messageId || 'unknown');

    // Only call the parent callback if we haven't already called it for this message
    if (!audioReadyCalledRef.current[msgId]) {
      console.log(`[${instanceId.current}] Marking audio ready for message: ${msgId}`);
      audioReadyCalledRef.current[msgId] = true;

      // Call the parent's onAudioReady callback if provided
      if (onAudioReady) {
        console.log(`[${instanceId.current}] Calling parent onAudioReady callback`);
        onAudioReady(msgId);
      }

      // Clean up old message states after a delay
      setManagedTimeout(() => {
        // Clean up message IDs older than 5 minutes to prevent memory leaks
        const now = Date.now();
        const fiveMinutesAgo = now - 5 * 60 * 1000;

        Object.keys(audioReadyCalledRef.current).forEach(id => {
          // We don't have timestamps for each ID, so we extract it from the ID if possible
          const timestampMatch = id.match(/msg_(\d+)_/);
          if (timestampMatch && parseInt(timestampMatch[1]) < fiveMinutesAgo) {
            delete audioReadyCalledRef.current[id];
          }
        });
      }, 10 * 60 * 1000, `cleanup-${msgId}`); // Clean up after 10 minutes
    } else {
      console.log(`Audio ready callback already called for message: ${msgId}, skipping duplicate call`);
    }
  }, [onAudioReady, setManagedTimeout]);

  // Cleanup function to clear timeouts when component unmounts
  useEffect(() => {
    return () => {
      // Release global lock if this instance holds it
      if (globalTTSLock.current === instanceId.current) {
        globalTTSLock.current = null;
        console.log(`[${instanceId.current}] Released global TTS lock on component unmount`);
      }

      // Release global processing mode lock on unmount
      if (globalProcessingMode !== null) {
        globalProcessingMode = null;
        console.log(`[${instanceId.current}] Released global processing mode lock on component unmount`);
      }

      // Clear all managed timeouts
      clearAllTimeouts();

      // Also clear the legacy timeout for backward compatibility
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
        processingTimeoutRef.current = null;
      }

      // Clear all audio ready states
      audioReadyCalledRef.current = {};
      voiceSessionOriginRef.current = {};
      lastProcessedText.current = '';
      setIsProcessing(false);
      setIsProcessingLongText(false);
      setTtsError(null);
    };
  }, [clearAllTimeouts]);

  // Only skip rendering if explicitly disabled
  if (disabled) {
    return null;
  }

  // Determine autoplay based on voice session origin, not current microphone state
  // This ensures TTS autoplays for messages that originated from voice sessions
  // even if the microphone is turned off during processing
  // PRIORITY: Voice session messages ALWAYS autoplay, regardless of autoPlay prop
  const currentMessageId = lastProcessedStateRef.current?.messageId || getMessageId(text);
  const isVoiceSessionMessage = voiceSessionOriginRef.current[currentMessageId] || false;
  const shouldAutoPlay = isVoiceSessionMessage || autoPlay;

  console.log(`[${instanceId.current}] Autoplay decision: autoPlay=${autoPlay}, isVoiceSessionMessage=${isVoiceSessionMessage}, shouldAutoPlay=${shouldAutoPlay}`);

  return (
    <>
      {isProcessingLongText && (
        <div
          style={{
            color: '#4a90e2',
            fontSize: '0.8rem',
            marginTop: '0.5rem',
            padding: '0.5rem',
            borderRadius: '4px',
            backgroundColor: 'rgba(74, 144, 226, 0.1)',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <div
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              border: '2px solid #4a90e2',
              borderTopColor: 'transparent',
              animation: 'spin 1s linear infinite'
            }}
          />
          <style jsx>{`
            @keyframes spin {
              to { transform: rotate(360deg); }
            }
          `}</style>
          Processing long text for speech...
        </div>
      )}

      {/* TTS Mode Indicator - Phase 2 */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            color: useStreamingTTS ? '#10b981' : '#6b7280',
            fontSize: '0.7rem',
            marginTop: '0.25rem',
            padding: '0.25rem 0.5rem',
            borderRadius: '3px',
            backgroundColor: useStreamingTTS ? 'rgba(16, 185, 129, 0.1)' : 'rgba(107, 114, 128, 0.1)',
            display: 'inline-block',
            fontWeight: '500'
          }}
        >
          {useStreamingTTS ? '⚡ WebSocket LINEAR16+WAV TTS' : '📡 REST TTS'}
        </div>
      )}

      {ttsError && (
        <div
          style={{
            color: '#ff5555',
            fontSize: '0.8rem',
            marginTop: '0.5rem',
            padding: '0.5rem',
            borderRadius: '4px',
            backgroundColor: 'rgba(255, 0, 0, 0.1)',
            display: 'none' // Hidden by default, but available for debugging
          }}
        >
          TTS Error: {ttsError}
        </div>
      )}

      {/* Exclusive TTS Component Rendering - Only one should render at a time */}
      {(() => {
        // Use global locked processing mode during processing to prevent component switching across ALL instances
        const renderingMode = globalProcessingMode !== null ? globalProcessingMode : useStreamingTTS;
        console.log(`[${instanceId.current}] Conditional rendering check - useStreamingTTS=${useStreamingTTS}, globalProcessingMode=${globalProcessingMode}, renderingMode=${renderingMode}, microphoneActive=${microphoneActive}`);
        return renderingMode ? (
          <>
            {console.log(`[${instanceId.current}] Rendering StreamingTTS component`)}
            <StreamingTTS
              key="streaming-tts" // Force React to unmount SimpleAudioPlayer when switching
              text={text}
              voiceModel={`aura-2-${voice}-en`}
              autoPlay={shouldAutoPlay}
              onError={handleTtsError}
              onAudioReady={handleAudioReady}
              onStart={onStart}
              onEnd={onEnd}
              messageId={lastProcessedStateRef.current?.messageId || getMessageId(text)}
              enableNaturalSpeech={enableNaturalSpeech}
            />
          </>
        ) : (
          <>
            {console.log(`[${instanceId.current}] Rendering SimpleAudioPlayer component`)}
            <SimpleAudioPlayer
              key="simple-audio-player" // Force React to unmount StreamingTTS when switching
              text={text}
              voice={voice}
              autoPlay={shouldAutoPlay}
              onError={handleTtsError}
              onAudioReady={handleAudioReady}
              messageId={lastProcessedStateRef.current?.messageId || getMessageId(text)}
              enableNaturalSpeech={enableNaturalSpeech}
            />
          </>
        );
      })()}
    </>
  );
};

export default TextToSpeech;
