<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Performance Test - Phase 2 Validation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-result.streaming {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .test-result.rest {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .test-result.comparison {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        .metric {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            background: #f0f0f0;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .test-controls {
            margin: 20px 0;
            text-align: center;
        }
        .test-controls button {
            margin: 10px;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }
        .test-controls .streaming {
            background: #4caf50;
            color: white;
        }
        .test-controls .rest {
            background: #2196f3;
            color: white;
        }
        .test-controls .compare {
            background: #ff9800;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .hidden-iframe {
            position: absolute;
            left: -9999px;
            width: 1px;
            height: 1px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 TTS Performance Test - Phase 2 Validation</h1>
        <p><strong>Measuring streaming TTS vs REST TTS performance</strong></p>
        
        <div class="test-controls">
            <button class="streaming" onclick="testStreamingTTS()">🚀 Test Streaming TTS</button>
            <button class="rest" onclick="testRestTTS()">📡 Test REST TTS</button>
            <button class="compare" onclick="runComparison()">⚡ Run Comparison</button>
        </div>
        
        <div id="status" class="status">Ready to test. Click a button above to start.</div>
        
        <div id="results"></div>
        
        <div class="test-result comparison">
            <h3>📊 Expected Performance Targets:</h3>
            <div class="metric">Streaming TTFB: &lt;200ms</div>
            <div class="metric">REST TTFB: ~600ms</div>
            <div class="metric">Improvement: 70-80%</div>
            <div class="metric">Mode: Auto-switching</div>
        </div>
    </div>

    <!-- Hidden iframes for testing -->
    <iframe id="streaming-iframe" class="hidden-iframe" src="about:blank"></iframe>
    <iframe id="rest-iframe" class="hidden-iframe" src="about:blank"></iframe>

    <script>
        let testResults = {
            streaming: [],
            rest: []
        };

        function updateStatus(message, type = 'loading') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addResult(type, latency, details = '') {
            const results = document.getElementById('results');
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            
            const icon = type === 'streaming' ? '🚀' : '📡';
            const mode = type === 'streaming' ? 'Streaming TTS' : 'REST TTS';
            
            result.innerHTML = `
                <h3>${icon} ${mode} Result</h3>
                <div class="metric">Latency: ${latency}ms</div>
                <div class="metric">Mode: ${type}</div>
                ${details ? `<div class="metric">${details}</div>` : ''}
                <div class="metric">Timestamp: ${new Date().toLocaleTimeString()}</div>
            `;
            
            results.appendChild(result);
            testResults[type].push(latency);
            
            // Scroll to show new result
            result.scrollIntoView({ behavior: 'smooth' });
        }

        function calculateImprovement() {
            if (testResults.streaming.length === 0 || testResults.rest.length === 0) {
                return null;
            }
            
            const avgStreaming = testResults.streaming.reduce((a, b) => a + b, 0) / testResults.streaming.length;
            const avgRest = testResults.rest.reduce((a, b) => a + b, 0) / testResults.rest.length;
            const improvement = ((avgRest - avgStreaming) / avgRest * 100).toFixed(1);
            
            return {
                avgStreaming: Math.round(avgStreaming),
                avgRest: Math.round(avgRest),
                improvement: improvement
            };
        }

        function showComparison() {
            const comparison = calculateImprovement();
            if (!comparison) return;
            
            const results = document.getElementById('results');
            const result = document.createElement('div');
            result.className = 'test-result comparison';
            
            const success = comparison.improvement >= 50 ? '✅' : '⚠️';
            const status = comparison.improvement >= 50 ? 'Target achieved!' : 'Below target';
            
            result.innerHTML = `
                <h3>⚡ Performance Comparison ${success}</h3>
                <div class="metric">Streaming Avg: ${comparison.avgStreaming}ms</div>
                <div class="metric">REST Avg: ${comparison.avgRest}ms</div>
                <div class="metric">Improvement: ${comparison.improvement}%</div>
                <div class="metric">Status: ${status}</div>
            `;
            
            results.appendChild(result);
            result.scrollIntoView({ behavior: 'smooth' });
        }

        async function testStreamingTTS() {
            updateStatus('Testing Streaming TTS performance...', 'loading');
            
            try {
                const startTime = performance.now();
                
                // Test streaming TTS by loading iframe with microphone active
                const iframe = document.getElementById('streaming-iframe');
                iframe.src = '/embed/test-streaming-tts?microphoneActive=true&autoPlay=true&test=performance';
                
                // Wait for iframe to load and TTS to start
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => reject(new Error('Timeout')), 10000);
                    
                    iframe.onload = () => {
                        clearTimeout(timeout);
                        // Simulate TTS trigger
                        setTimeout(() => {
                            const endTime = performance.now();
                            const latency = Math.round(endTime - startTime);
                            addResult('streaming', latency, 'WebSocket connection');
                            updateStatus('Streaming TTS test completed!', 'success');
                            resolve();
                        }, 2000); // Wait for TTS to process
                    };
                    
                    iframe.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error('Failed to load iframe'));
                    };
                });
                
            } catch (error) {
                console.error('Streaming TTS test error:', error);
                updateStatus(`Streaming TTS test failed: ${error.message}`, 'error');
            }
        }

        async function testRestTTS() {
            updateStatus('Testing REST TTS performance...', 'loading');
            
            try {
                const startTime = performance.now();
                
                // Test REST TTS by calling API directly
                const response = await fetch('/api/deepgram-tts?text=Hello%20world%20this%20is%20a%20performance%20test&voice=aura-2-thalia-en&t=' + Date.now());
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const endTime = performance.now();
                const latency = Math.round(endTime - startTime);
                
                addResult('rest', latency, 'REST API call');
                updateStatus('REST TTS test completed!', 'success');
                
            } catch (error) {
                console.error('REST TTS test error:', error);
                updateStatus(`REST TTS test failed: ${error.message}`, 'error');
            }
        }

        async function runComparison() {
            updateStatus('Running comprehensive comparison...', 'loading');
            
            try {
                // Run multiple tests for better accuracy
                for (let i = 0; i < 3; i++) {
                    await testRestTTS();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
                    
                    await testStreamingTTS();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
                }
                
                showComparison();
                updateStatus('Comparison completed! Check results below.', 'success');
                
            } catch (error) {
                console.error('Comparison test error:', error);
                updateStatus(`Comparison failed: ${error.message}`, 'error');
            }
        }

        // Log test start
        console.log('🚀 TTS Performance Test Ready');
        console.log('📊 Target: Streaming <200ms, REST ~600ms, 70-80% improvement');
    </script>
</body>
</html>
