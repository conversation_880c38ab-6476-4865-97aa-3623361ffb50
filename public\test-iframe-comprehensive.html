<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Iframe Embedding Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        iframe {
            border: 2px solid #007bff;
            border-radius: 8px;
            width: 100%;
            height: 600px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .console-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Comprehensive Iframe Embedding Test</h1>
    <p>This page tests various iframe embedding scenarios to ensure the deployment protection bypass is working correctly.</p>

    <div class="test-section">
        <h2>Test 1: Direct Embed URL</h2>
        <div class="test-info">
            <strong>Testing:</strong> Direct iframe embedding of /embed/[id] route<br>
            <strong>Expected:</strong> Should load without 401 errors or X-Frame-Options issues
        </div>
        <div id="test1-status" class="status loading">Loading...</div>
        <iframe
            id="test1-iframe"
            src=""
            allow="microphone; autoplay"
            title="Direct Embed Test">
        </iframe>
        <div class="console-log" id="test1-console"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: API Chat Endpoint</h2>
        <div class="test-info">
            <strong>Testing:</strong> Direct API call to /api/chat/[id]<br>
            <strong>Expected:</strong> Should return JSON data without 401 errors
        </div>
        <div id="test2-status" class="status loading">Loading...</div>
        <div class="console-log" id="test2-console"></div>
        <pre id="test2-result" style="background: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
    </div>

    <div class="test-section">
        <h2>Test 3: Public Chat API Fallback</h2>
        <div class="test-info">
            <strong>Testing:</strong> Fallback API call to /api/public-chat/[id]<br>
            <strong>Expected:</strong> Should return JSON data as backup route
        </div>
        <div id="test3-status" class="status loading">Loading...</div>
        <div class="console-log" id="test3-console"></div>
        <pre id="test3-result" style="background: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
    </div>

    <div class="test-section">
        <h2>Test 4: Cross-Origin Headers Check</h2>
        <div class="test-info">
            <strong>Testing:</strong> Checking response headers for proper CORS and iframe support<br>
            <strong>Expected:</strong> Should have proper Access-Control headers and no X-Frame-Options
        </div>
        <div id="test4-status" class="status loading">Loading...</div>
        <div class="console-log" id="test4-console"></div>
    </div>

    <script>
        const chatId = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d'; // Using valid test chat ID
        // Use current domain to avoid cross-origin issues
        const baseUrl = window.location.origin;
        // Vercel protection bypass secret
        const bypassSecret = 'Q3W0KiXNSiZt6HCFH0e1zYfT2MwpCR2F';

        function log(testId, message) {
            const consoleEl = document.getElementById(`${testId}-console`);
            const timestamp = new Date().toLocaleTimeString();
            consoleEl.innerHTML += `[${timestamp}] ${message}\n`;
            consoleEl.scrollTop = consoleEl.scrollHeight;
        }

        function setStatus(testId, status, message) {
            const statusEl = document.getElementById(`${testId}-status`);
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        // Test 1: Iframe loading
        function test1() {
            const iframe = document.getElementById('test1-iframe');

            // Set the iframe src with bypass secret
            iframe.src = `${baseUrl}/embed/${chatId}?x-vercel-protection-bypass=${bypassSecret}`;
            log('test1', `Loading iframe: ${iframe.src}`);

            iframe.onload = function() {
                log('test1', 'Iframe loaded successfully');
                setStatus('test1', 'success', '✅ Iframe loaded successfully');
            };

            iframe.onerror = function() {
                log('test1', 'Iframe failed to load');
                setStatus('test1', 'error', '❌ Iframe failed to load');
            };

            // Timeout check
            setTimeout(() => {
                if (document.getElementById('test1-status').textContent === 'Loading...') {
                    log('test1', 'Iframe loading timeout - may indicate X-Frame-Options or 401 issues');
                    setStatus('test1', 'error', '❌ Loading timeout - check console for errors');
                }
            }, 10000);
        }

        // Test 2: API Chat endpoint
        async function test2() {
            try {
                log('test2', 'Testing /api/chat endpoint...');
                const response = await fetch(`${baseUrl}/api/chat/${chatId}?embed=true&t=${Date.now()}&x-vercel-protection-bypass=${bypassSecret}`, {
                    headers: {
                        'x-vercel-protection-bypass': bypassSecret
                    }
                });

                log('test2', `Response status: ${response.status}`);
                log('test2', `Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('test2-result').textContent = JSON.stringify(data, null, 2);
                    log('test2', 'API call successful');
                    setStatus('test2', 'success', '✅ API call successful');
                } else {
                    const errorText = await response.text();
                    document.getElementById('test2-result').textContent = errorText;
                    log('test2', `API call failed: ${response.status} ${response.statusText}`);
                    setStatus('test2', 'error', `❌ API call failed: ${response.status}`);
                }
            } catch (error) {
                log('test2', `Error: ${error.message}`);
                setStatus('test2', 'error', `❌ Error: ${error.message}`);
            }
        }

        // Test 3: Public Chat API
        async function test3() {
            try {
                log('test3', 'Testing /api/public-chat endpoint...');
                const response = await fetch(`${baseUrl}/api/public-chat/${chatId}?embed=true&t=${Date.now()}&x-vercel-protection-bypass=${bypassSecret}`, {
                    headers: {
                        'x-vercel-protection-bypass': bypassSecret
                    }
                });

                log('test3', `Response status: ${response.status}`);
                log('test3', `Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('test3-result').textContent = JSON.stringify(data, null, 2);
                    log('test3', 'Public API call successful');
                    setStatus('test3', 'success', '✅ Public API call successful');
                } else {
                    const errorText = await response.text();
                    document.getElementById('test3-result').textContent = errorText;
                    log('test3', `Public API call failed: ${response.status} ${response.statusText}`);
                    setStatus('test3', 'error', `❌ Public API call failed: ${response.status}`);
                }
            } catch (error) {
                log('test3', `Error: ${error.message}`);
                setStatus('test3', 'error', `❌ Error: ${error.message}`);
            }
        }

        // Test 4: Headers check
        async function test4() {
            try {
                log('test4', 'Checking response headers...');
                const response = await fetch(`${baseUrl}/embed/${chatId}?x-vercel-protection-bypass=${bypassSecret}`, {
                    method: 'HEAD',
                    headers: {
                        'x-vercel-protection-bypass': bypassSecret
                    }
                });

                const headers = Object.fromEntries(response.headers.entries());
                log('test4', `Response headers: ${JSON.stringify(headers, null, 2)}`);

                const hasXFrameOptions = headers['x-frame-options'];
                const hasAccessControl = headers['access-control-allow-origin'];
                const hasCSP = headers['content-security-policy'];

                if (!hasXFrameOptions && hasAccessControl && hasCSP) {
                    log('test4', 'Headers look good for iframe embedding');
                    setStatus('test4', 'success', '✅ Headers configured correctly');
                } else {
                    log('test4', 'Headers may have issues for iframe embedding');
                    setStatus('test4', 'error', '❌ Headers may have issues');
                }
            } catch (error) {
                log('test4', `Error: ${error.message}`);
                setStatus('test4', 'error', `❌ Error: ${error.message}`);
            }
        }

        // Run all tests
        window.onload = function() {
            test1();
            test2();
            test3();
            test4();
        };
    </script>
</body>
</html>
