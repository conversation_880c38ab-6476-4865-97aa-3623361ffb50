// deploy-vercel.js
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure the git user is configured correctly
try {
  // Check if git user is configured
  const userName = execSync('git config user.name').toString().trim();
  const userEmail = execSync('git config user.email').toString().trim();

  console.log(`Current git user: ${userName} <${userEmail}>`);

  // Set git user if not already set to Tellivision
  if (userName !== 'Tellivision') {
    console.log('Setting git user to Tellivision...');
    execSync('git config user.name "Tellivision"');
  }

  if (userEmail !== '<EMAIL>') {
    console.log('Setting git email...');
    execSync('git config user.email "<EMAIL>"');
  }

  // Verify the changes
  const newUserName = execSync('git config user.name').toString().trim();
  const newUserEmail = execSync('git config user.email').toString().trim();
  console.log(`Updated git user: ${newUserName} <${newUserEmail}>`);

  // Make a small change to trigger a new commit
  const readmePath = path.join(__dirname, '..', 'README.md');
  let readmeContent = fs.readFileSync(readmePath, 'utf8');

  // Add a timestamp to the README
  const timestamp = new Date().toISOString();
  if (!readmeContent.includes('Last deployed:')) {
    readmeContent += `\n\nLast deployed: ${timestamp}`;
  } else {
    readmeContent = readmeContent.replace(
      /Last deployed: .*/,
      `Last deployed: ${timestamp}`
    );
  }

  fs.writeFileSync(readmePath, readmeContent);

  // Commit and push the changes
  console.log('Committing changes...');
  execSync('git add README.md');
  execSync(`git commit -m "Update deployment timestamp: ${timestamp}"`);

  console.log('Pushing to GitHub...');
  execSync('git push origin main');

  console.log('Deployment triggered successfully!');
} catch (error) {
  console.error('Error during deployment:', error.message);
  process.exit(1);
}
