import { NextRequest, NextResponse } from 'next/server';
import { processTextForNaturalSpeech } from '@/lib/natural-speech-utils';
import { createClient } from '@deepgram/sdk';
import { checkVoiceModelAccess } from '@/lib/middleware/tierMiddleware';
import { createClient as createSupabaseClient } from '@/lib/supabase/server';
import { applySampleAlignment, buildDeepgramTTSUrl, DEEPGRAM_HEADERS } from '@/lib/tts-config-standard';

const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;

// CRITICAL FIX: Stack-safe base64 encoding for large audio data
function uint8ArrayToBase64(uint8Array: Uint8Array): string {
  const CHUNK_SIZE = 8192; // Process in 8KB chunks to prevent stack overflow
  let result = '';

  for (let i = 0; i < uint8Array.length; i += CHUNK_SIZE) {
    const chunk = uint8Array.slice(i, i + CHUNK_SIZE);
    const chunkString = String.fromCharCode.apply(null, Array.from(chunk));
    result += chunkString;
  }

  return btoa(result);
}

// OPTIMIZATION: Smaller chunks for faster first response
function chunkTextOptimized(text: string, maxChunkSize: number = 100): string[] {
  // Split by sentences first for natural breaks
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const chunks: string[] = [];
  let currentChunk = '';

  for (const sentence of sentences) {
    const trimmedSentence = sentence.trim();
    if (!trimmedSentence) continue;

    // If adding this sentence would exceed max size, save current chunk
    if (currentChunk.length + trimmedSentence.length + 1 > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = trimmedSentence;
    } else {
      currentChunk += (currentChunk ? ' ' : '') + trimmedSentence;
    }
  }

  // Add the last chunk if it has content
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  // If no sentences were found, split by words as fallback
  if (chunks.length === 0) {
    const words = text.split(' ');
    let wordChunk = '';
    for (const word of words) {
      if (wordChunk.length + word.length + 1 > maxChunkSize && wordChunk.length > 0) {
        chunks.push(wordChunk.trim());
        wordChunk = word;
      } else {
        wordChunk += (wordChunk ? ' ' : '') + word;
      }
    }
    if (wordChunk.trim()) {
      chunks.push(wordChunk.trim());
    }
  }

  return chunks.length > 0 ? chunks : [text];
}

export async function POST(request: NextRequest) {
  try {
    const {
      text,
      voiceModel = 'aura-2-thalia-en', // Changed to female voice
      format = 'linear16',
      sampleRate = 24000,
      enableNaturalSpeech = false,
      naturalSpeechTemperature = 0.7
    } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    if (!DEEPGRAM_API_KEY) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    // Check user authentication and tier permissions (optional for backward compatibility)
    const supabase = createSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    // If user is authenticated, check tier permissions
    if (user && !authError) {
      // Extract voice model name from full model string
      const voiceModelName = voiceModel.replace('aura-2-', '').replace('-en', '')

      // Check tier permissions for voice features
      const tierCheck = await checkVoiceModelAccess(user.id, voiceModelName)

      if (!tierCheck.allowed) {
        return NextResponse.json({
          error: 'Voice feature access denied',
          reason: tierCheck.reason,
          userTier: tierCheck.userTier,
          upgradeRequired: tierCheck.upgradeRequired,
          details: `Voice features require ${tierCheck.upgradeRequired} tier or higher.`
        }, { status: 403 })
      }
    }
    // If no user authentication, allow for backward compatibility with existing chat interfaces

    console.log('🚀 OPTIMIZED Streaming TTS - Starting parallel processing...');
    console.log('📝 Text length:', text.length);
    console.log('🎤 Voice model:', voiceModel);
    console.log('🧠 Natural speech processing:', enableNaturalSpeech ? 'ENABLED' : 'DISABLED');

    // SPEED OPTIMIZATION: Process natural speech but don't block first chunk
    let finalText = text;
    let processingResult = null;

    if (enableNaturalSpeech) {
      console.log('🧠 Processing text through gpt-4o-mini for natural speech...');
      try {
        processingResult = await processTextForNaturalSpeech(text, true, 'gpt-4o-mini', naturalSpeechTemperature);
        if (processingResult.processed) {
          finalText = processingResult.processedText;
          console.log('✅ Natural speech processing completed');
          console.log('📝 Original:', text.substring(0, 100) + '...');
          console.log('🎭 Enhanced:', finalText.substring(0, 100) + '...');
        } else {
          console.log('⚠️ Natural speech processing failed, using original text');
        }
      } catch (error) {
        console.log('⚠️ Natural speech processing error:', error);
      }
    }

    // OPTIMIZATION: Split text into smaller chunks for faster first response
    const chunks = chunkTextOptimized(finalText, 100);
    console.log(`📦 Split into ${chunks.length} optimized chunks for parallel processing`);

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          console.log('🚀 Starting OPTIMIZED parallel processing pipeline...');

          // Send initial status
          const statusData = JSON.stringify({ 
            type: 'status', 
            message: 'connected',
            totalChunks: chunks.length,
            optimization: 'parallel_pipeline'
          });
          controller.enqueue(encoder.encode(`data: ${statusData}\n\n`));

          // COMPLETELY FIXED: Promise-based parallel processing (NO RECURSION)
          const maxParallel = Math.min(3, chunks.length);
          let completedChunks = 0;
          let streamClosed = false;

          // Process single chunk function
          const processChunk = async (index: number) => {
            if (streamClosed) return;

            const chunk = chunks[index];
            console.log(`🔄 Processing chunk ${index + 1}/${chunks.length}: "${chunk.substring(0, 30)}..."`);

            try {
              // Make TTS request for this chunk
              const response = await fetch(`https://api.deepgram.com/v1/speak?model=${encodeURIComponent(voiceModel)}&encoding=${format}&sample_rate=${sampleRate}&smart_format=true`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Token ${DEEPGRAM_API_KEY}`,
                  'Accept': 'audio/wav'
                },
                body: JSON.stringify({ text: chunk })
              });

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }

              const audioBuffer = await response.arrayBuffer();
              const audioArray = new Uint8Array(audioBuffer);

              // CRITICAL FIX: Apply sample alignment to prevent partial sample artifacts
              const alignedAudioArray = applySampleAlignment(audioArray);

              // CRITICAL FIX: Use stack-safe base64 conversion
              const base64Data = uint8ArrayToBase64(alignedAudioArray);

              // Send audio chunk immediately (if stream not closed)
              if (!streamClosed) {
                const audioMessage = JSON.stringify({
                  type: 'audio',
                  data: base64Data,
                  encoding: format,
                  sampleRate: sampleRate,
                  chunkNumber: index + 1,
                  totalChunks: chunks.length,
                  isLastChunk: index === chunks.length - 1,
                  chunkSize: alignedAudioArray.length,
                  timestamp: Date.now()
                });

                controller.enqueue(encoder.encode(`data: ${audioMessage}\n\n`));
                console.log(`✅ Sent chunk ${index + 1}/${chunks.length} (${alignedAudioArray.length} bytes)`);
              }

              return { success: true, index };

            } catch (error) {
              console.error(`❌ Error processing chunk ${index + 1}:`, error);

              if (!streamClosed) {
                const errorMessage = JSON.stringify({
                  type: 'error',
                  message: `Chunk ${index + 1} failed: ${error}`,
                  chunkNumber: index + 1,
                  timestamp: Date.now()
                });
                controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
              }

              return { success: false, index, error };
            }
          };

          // FIXED: Promise-based parallel processing with sliding window
          const processAllChunks = async () => {
            const results: Promise<any>[] = [];

            for (let i = 0; i < chunks.length; i += maxParallel) {
              // Process chunks in batches of maxParallel
              const batch = chunks.slice(i, i + maxParallel);
              const batchPromises = batch.map((_, batchIndex) =>
                processChunk(i + batchIndex)
              );

              // Wait for current batch to complete before starting next batch
              const batchResults = await Promise.allSettled(batchPromises);
              results.push(...batchResults);

              // Update completed count
              completedChunks += batch.length;
              console.log(`📊 Completed batch: ${completedChunks}/${chunks.length} chunks`);
            }

            // Send completion message
            if (!streamClosed) {
              const closeMessage = JSON.stringify({
                type: 'close',
                message: 'Optimized streaming completed',
                totalChunks: chunks.length,
                timestamp: Date.now()
              });
              controller.enqueue(encoder.encode(`data: ${closeMessage}\n\n`));
              controller.close();
              streamClosed = true;
              console.log('✅ All chunks completed - stream closed');
            }
          };

          // Start processing all chunks
          console.log(`🚀 Starting Promise-based parallel processing (${maxParallel} concurrent)`);
          processAllChunks().catch(error => {
            console.error('❌ Error in processAllChunks:', error);
            if (!streamClosed) {
              const errorMessage = JSON.stringify({
                type: 'error',
                message: `Processing error: ${error}`,
                timestamp: Date.now()
              });
              controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
              controller.close();
              streamClosed = true;
            }
          });

        } catch (error) {
          console.error('❌ Error in optimized streaming:', error);
          
          const errorMessage = JSON.stringify({
            type: 'error',
            message: `Streaming error: ${error}`,
            timestamp: Date.now()
          });
          controller.enqueue(encoder.encode(`data: ${errorMessage}\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log('🚀 Optimized streaming cancelled by client');
      }
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Accel-Buffering': 'no',
        'X-TTS-Engine': 'deepgram-optimized',
        'X-TTS-Version': '2.1.0',
        'X-Natural-Speech-Processed': processingResult?.processed ? 'true' : 'false',
        'X-Tokens-Used': processingResult?.tokensUsed?.toString() || '0',
        'X-Original-Length': text.length.toString(),
        'X-Processed-Length': finalText.length.toString()
      },
    });

  } catch (error: any) {
    console.error('❌ Optimized TTS request failed:', error);
    
    return NextResponse.json(
      {
        error: 'TTS request failed',
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}
