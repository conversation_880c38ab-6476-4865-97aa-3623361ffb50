/**
 * BotFusion Direct Embed Script
 * This script creates a chat widget directly in the DOM without using iframes
 * Version: 1.0.0
 */

(function() {
  // Get the script tag that loaded this script
  var scripts = document.getElementsByTagName('script');
  var script = null;

  // Find the script tag with our attributes
  for (var i = 0; i < scripts.length; i++) {
    if (scripts[i].src.indexOf('direct-embed.js') !== -1) {
      script = scripts[i];
      break;
    }
  }

  if (!script) {
    console.error('BotFusion: Could not find script tag');
    return;
  }

  // Get the chat ID from the data-chat-id attribute
  var chatId = script.getAttribute('data-chat-id');
  if (!chatId) {
    console.error('BotFusion: Missing data-chat-id attribute');
    return;
  }

  // Get the base URL from the data-base-url attribute
  var baseUrl = script.getAttribute('data-base-url');
  if (!baseUrl) {
    console.error('BotFusion: Missing data-base-url attribute');
    return;
  }

  console.log('BotFusion: Initializing with chatId=' + chatId + ' and baseUrl=' + baseUrl);

  // Create the CSS styles
  var styles = document.createElement('style');
  styles.textContent = `
    #botfusion-chat-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    #botfusion-chat-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    #botfusion-chat-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 400px;
      height: 600px;
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      display: none;
      background-color: white;
      overflow: hidden;
    }

    #botfusion-chat-header {
      background-color: #3b82f6;
      color: white;
      padding: 10px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    #botfusion-chat-title {
      font-weight: bold;
      font-size: 16px;
    }

    #botfusion-chat-close {
      cursor: pointer;
      font-size: 20px;
    }

    #botfusion-chat-messages {
      padding: 15px;
      height: calc(100% - 120px);
      overflow-y: auto;
    }

    .botfusion-message {
      margin-bottom: 15px;
      max-width: 80%;
      padding: 10px 15px;
      border-radius: 18px;
    }

    .botfusion-user-message {
      background-color: #3b82f6;
      color: white;
      margin-left: auto;
      border-bottom-right-radius: 5px;
    }

    .botfusion-bot-message {
      background-color: #f1f5f9;
      color: #333;
      border-bottom-left-radius: 5px;
    }

    #botfusion-chat-input-container {
      padding: 15px;
      border-top: 1px solid #e5e7eb;
      display: flex;
      position: absolute;
      bottom: 0;
      width: calc(100% - 30px);
      background-color: white;
    }

    #botfusion-chat-input {
      flex: 1;
      padding: 10px 15px;
      border: 1px solid #e5e7eb;
      border-radius: 20px;
      outline: none;
    }

    #botfusion-chat-send {
      background-color: #3b82f6;
      color: white;
      border: none;
      border-radius: 20px;
      padding: 10px 15px;
      margin-left: 10px;
      cursor: pointer;
    }

    @media (max-width: 480px) {
      #botfusion-chat-container {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
      }
    }
  `;
  document.head.appendChild(styles);

  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Create the chat container
  var container = document.createElement('div');
  container.id = 'botfusion-chat-container';
  container.innerHTML = `
    <div id="botfusion-chat-header">
      <div id="botfusion-chat-title">BotFusion Chat</div>
      <div id="botfusion-chat-close">×</div>
    </div>
    <div id="botfusion-chat-messages"></div>
    <div id="botfusion-chat-input-container">
      <input type="text" id="botfusion-chat-input" placeholder="Type your message...">
      <button id="botfusion-chat-send">Send</button>
    </div>
  `;

  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(container);

  // Get the elements
  var closeButton = document.getElementById('botfusion-chat-close');
  var input = document.getElementById('botfusion-chat-input');
  var sendButton = document.getElementById('botfusion-chat-send');
  var messages = document.getElementById('botfusion-chat-messages');

  // Add click event to button
  button.onclick = function() {
    container.style.display = 'block';
    this.style.display = 'none';

    // Add welcome message if no messages yet
    if (messages.children.length === 0) {
      addMessage('Hello! How can I help you today?', 'bot');
    }
  };

  // Add click event to close button
  closeButton.onclick = function() {
    container.style.display = 'none';
    button.style.display = 'flex';
  };

  // Add click event to send button
  sendButton.onclick = sendMessage;

  // Add keypress event to input
  input.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      sendMessage();
    }
  });

  // Function to send a message
  function sendMessage() {
    var text = input.value.trim();
    if (text) {
      addMessage(text, 'user');
      input.value = '';

      // Send the message to the server
      fetch(baseUrl + '/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId: chatId,
          message: text
        })
      })
      .then(response => response.json())
      .then(data => {
        addMessage(data.response || 'Sorry, I could not process your request.', 'bot');
      })
      .catch(error => {
        console.error('Error:', error);
        addMessage('Sorry, there was an error processing your request.', 'bot');
      });
    }
  }

  // Function to add a message to the chat
  function addMessage(text, type) {
    var message = document.createElement('div');
    message.className = 'botfusion-message botfusion-' + type + '-message';
    message.textContent = text;
    messages.appendChild(message);
    messages.scrollTop = messages.scrollHeight;
  }
})();
