import { NextRequest, NextResponse } from 'next/server';

// Configure as an edge function
export const runtime = 'edge';

/**
 * Echo TTS API - Last-resort fallback TTS service
 *
 * This API returns a pre-generated WAV file with a simple beep sound.
 * It's used as a fallback when other TTS services (Deepgram) fail.
 * This ensures that the client always receives a valid audio response,
 * even when primary TTS services are unavailable.
 */
export async function GET(request: NextRequest) {
  console.log('Echo TTS API: Request received');

  try {
    // Get the text and voice from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const text = searchParams.get('text');
    const voice = searchParams.get('voice') || 'nova';
    const isEmbed = searchParams.get('embed') === 'true';
    const isDirect = searchParams.get('direct') === 'true';

    // Log request details (truncate text for privacy)
    const truncatedText = text && text.length > 50 ? `${text.substring(0, 50)}...` : text;
    console.log(`Echo TTS API: Processing request with voice=${voice}, text="${truncatedText}", embed=${isEmbed}, direct=${isDirect}`);
    console.log(`Text length: ${text ? text.length : 0} characters`);

    // This is a valid WAV file with a short beep sound
    // WAV format is more universally supported than MP3 in browsers
    // This is a minimal valid WAV file with a simple beep
    const wavBytes = new Uint8Array([
      // "RIFF" chunk descriptor
      0x52, 0x49, 0x46, 0x46, // "RIFF" in ASCII
      0x34, 0x00, 0x00, 0x00, // Chunk size (36 + data size = 52)
      0x57, 0x41, 0x56, 0x45, // "WAVE" in ASCII

      // "fmt " sub-chunk
      0x66, 0x6d, 0x74, 0x20, // "fmt " in ASCII
      0x10, 0x00, 0x00, 0x00, // Sub-chunk size (16 bytes)
      0x01, 0x00,             // Audio format (1 = PCM)
      0x01, 0x00,             // Number of channels (1 = mono)
      0x44, 0xac, 0x00, 0x00, // Sample rate (44100 Hz)
      0x88, 0x58, 0x01, 0x00, // Byte rate (44100 * 1 * 2)
      0x02, 0x00,             // Block align (NumChannels * BitsPerSample/8)
      0x10, 0x00,             // Bits per sample (16)

      // "data" sub-chunk
      0x64, 0x61, 0x74, 0x61, // "data" in ASCII
      0x10, 0x00, 0x00, 0x00, // Sub-chunk size (16 bytes of data)

      // Actual audio data - a simple beep (8 samples of 16-bit PCM)
      0x00, 0x00, 0xFF, 0x7F, // Sample 1 and 2
      0x00, 0x00, 0xFF, 0x7F, // Sample 3 and 4
      0x00, 0x00, 0xFF, 0x7F, // Sample 5 and 6
      0x00, 0x00, 0xFF, 0x7F  // Sample 7 and 8
    ]);

    console.log(`Echo TTS API: Returning audio data (${wavBytes.buffer.byteLength} bytes)`);

    // Create comprehensive headers with enhanced security settings for all contexts
    const headers: HeadersInit = {
      'Content-Type': 'audio/wav',
      'Content-Length': wavBytes.buffer.byteLength.toString(),
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
      'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type',
      'Access-Control-Max-Age': '86400',
      'Cross-Origin-Resource-Policy': 'cross-origin',
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
      'Accept-Ranges': 'bytes',
      'Timing-Allow-Origin': '*',
      'X-Content-Type-Options': 'nosniff'
    };

    // For embedded contexts, log that we're handling an embedded request
    if (isEmbed) {
      console.log('Echo TTS API: Handling embedded context request');

      // Add a special header for debugging embedded contexts
      headers['X-Embed-Context'] = 'true';
    }

    // For direct contexts, log that we're handling a direct request
    if (isDirect) {
      console.log('Echo TTS API: Handling direct context request');

      // Add a special header for debugging direct contexts
      headers['X-Direct-Context'] = 'true';
    }

    // Explicitly remove X-Frame-Options header to ensure it's not set
    // This is critical for iframe embedding to work properly
    // Using delete operator on headers object
    if ('X-Frame-Options' in headers) {
      delete headers['X-Frame-Options'];
    }

    // Add Content-Security-Policy header to allow embedding
    headers['Content-Security-Policy'] = "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:;";

    // Log the headers we're sending
    console.log('Echo TTS API: Sending headers:', JSON.stringify(headers));

    // Return the audio as a response with appropriate headers
    return new NextResponse(wavBytes.buffer, { headers });
  } catch (error: unknown) {
    // Log the detailed error
    console.error('Echo TTS API: Error generating speech:', error);

    // Extract error message safely
    const errorMessage = error instanceof Error
      ? error.message
      : 'An unexpected error occurred while processing the TTS request.';

    // Generic error response with comprehensive headers
    return NextResponse.json(
      {
        error: 'Failed to generate speech',
        message: errorMessage,
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept',
          'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type',
          'Access-Control-Max-Age': '86400',
          'Cross-Origin-Resource-Policy': 'cross-origin',
          'Cross-Origin-Embedder-Policy': 'unsafe-none',
          'Cross-Origin-Opener-Policy': 'unsafe-none',
          'Timing-Allow-Origin': '*',
          'X-Content-Type-Options': 'nosniff'
        }
      }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS preflight
 *
 * This is essential for cross-origin requests, especially in embedded contexts.
 * It ensures that browsers can make requests to this API from different origins.
 */
export async function OPTIONS(request: NextRequest) {
  console.log('Echo TTS API: OPTIONS request received (CORS preflight)');

  // Check if this is an embedded context request
  const searchParams = request.nextUrl.searchParams;
  const isEmbed = searchParams.get('embed') === 'true';
  const isDirect = searchParams.get('direct') === 'true';

  console.log(`Echo TTS API: OPTIONS request for ${isEmbed ? 'embedded' : (isDirect ? 'direct' : 'standard')} context`);

  // Create comprehensive headers for CORS
  const headers: HeadersInit = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, HEAD',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma, Expires, Range, X-Requested-With, Accept',
    'Access-Control-Expose-Headers': 'Content-Length, Content-Range, Content-Type',
    'Access-Control-Max-Age': '86400',
    'Access-Control-Allow-Credentials': 'true',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Accept-Ranges': 'bytes',
    'Timing-Allow-Origin': '*',
    'Cross-Origin-Resource-Policy': 'cross-origin',
    'Cross-Origin-Embedder-Policy': 'unsafe-none',
    'Cross-Origin-Opener-Policy': 'unsafe-none',
    'X-Content-Type-Options': 'nosniff'
  };

  // For embedded contexts, log that we're handling an embedded request
  if (isEmbed) {
    console.log('Echo TTS API: Handling embedded context OPTIONS request');

    // Add a special header for debugging embedded contexts
    headers['X-Embed-Context'] = 'true';
  }

  // For direct contexts, log that we're handling a direct request
  if (isDirect) {
    console.log('Echo TTS API: Handling direct context OPTIONS request');

    // Add a special header for debugging direct contexts
    headers['X-Direct-Context'] = 'true';
  }

  // Explicitly remove X-Frame-Options header to ensure it's not set
  // This is critical for iframe embedding to work properly
  // Using delete operator on headers object
  if ('X-Frame-Options' in headers) {
    delete headers['X-Frame-Options'];
  }

  // Add Content-Security-Policy header to allow embedding
  headers['Content-Security-Policy'] = "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss: blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:;";

  // Log the headers we're sending
  console.log('Echo TTS API: Sending OPTIONS headers:', JSON.stringify(headers));

  // Return an empty response with status 200 (OK) instead of 204 (No Content)
  // This ensures better compatibility with some browsers and frameworks
  return new NextResponse(null, {
    status: 200,
    headers
  });
}
