'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'

interface PricingButtonProps {
  text: string
  href?: string
  disabled?: boolean
}

export function PricingButton({ text, href, disabled = false }: PricingButtonProps) {
  const buttonClasses = "glass-card hover:shadow-glow-blue button-glow-hover w-32 h-10"
  
  // If disabled, render a disabled button
  if (disabled) {
    return (
      <span className="inline-block">
        <Button 
          disabled 
          variant="outline" 
          className={buttonClasses}
        >
          {text}
        </Button>
      </span>
    )
  }
  
  // If href is provided, render a button with a link
  if (href) {
    return (
      <a href={href} className="inline-block">
        <Button 
          variant="outline" 
          className={buttonClasses}
        >
          {text}
        </Button>
      </a>
    )
  }
  
  // Default case: render a regular button
  return (
    <span className="inline-block">
      <Button 
        variant="outline" 
        className={buttonClasses}
      >
        {text}
      </Button>
    </span>
  )
}
