/**
 * Unified TTS Configuration Standard
 * 
 * This file defines the standardized configuration for all TTS endpoints
 * to eliminate audio pops/clicks while preserving all voice models and 
 * smart formatting functionality.
 */

// Standard audio configuration to eliminate artifacts
export const STANDARD_AUDIO_CONFIG = {
  // Sample rate - consistent across all endpoints
  sampleRate: 24000,
  
  // Audio format - linear16 for streaming, mp3 for non-streaming
  format: 'linear16' as const,
  
  // Container - none for raw PCM to avoid format conversion artifacts
  container: 'none' as const,
  
  // Smart format - ALWAYS true for proper number pronunciation
  smartFormat: true,
  
  // Encoding - linear16 for streaming
  encoding: 'linear16' as const,
  
  // Chunk processing settings
  chunkAlignment: true, // Ensure even-length chunks
  
  // Anti-click settings
  fadeInDuration: 0.005, // 5ms fade-in (increased from 2ms)
  fadeOutDuration: 0.005, // 5ms fade-out (increased from 2ms)
  dcOffsetRemoval: true, // Always remove DC offset
  
  // Timing precision
  useSampleAccurateTiming: true, // Use sample-accurate calculations
  
  // Buffer settings
  bufferConcatenation: true, // Use proper buffer concatenation
} as const;

// Deepgram TTS API standard parameters
export const DEEPGRAM_TTS_PARAMS = {
  encoding: STANDARD_AUDIO_CONFIG.encoding,
  sample_rate: STANDARD_AUDIO_CONFIG.sampleRate,
  smart_format: STANDARD_AUDIO_CONFIG.smartFormat,
  container: STANDARD_AUDIO_CONFIG.container,
} as const;

// Standard headers for Deepgram API calls
export const DEEPGRAM_HEADERS = {
  'Content-Type': 'application/json',
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
} as const;

/**
 * Build standardized Deepgram TTS URL
 */
export function buildDeepgramTTSUrl(voiceModel: string, format: 'linear16' | 'mp3' = 'linear16'): string {
  const baseUrl = 'https://api.deepgram.com/v1/speak';
  const params = new URLSearchParams({
    model: voiceModel,
    smart_format: STANDARD_AUDIO_CONFIG.smartFormat.toString(),
  });
  
  // Add format-specific parameters
  if (format === 'linear16') {
    params.append('encoding', 'linear16');
    params.append('sample_rate', STANDARD_AUDIO_CONFIG.sampleRate.toString());
    params.append('container', 'none');
  } else if (format === 'mp3') {
    params.append('encoding', 'mp3');
    // Note: sample_rate not applicable for MP3 encoding
  }
  
  return `${baseUrl}?${params.toString()}`;
}

/**
 * Build standardized Deepgram WebSocket TTS options
 */
export function buildDeepgramWebSocketOptions(voiceModel: string) {
  return {
    model: voiceModel,
    encoding: STANDARD_AUDIO_CONFIG.encoding,
    sample_rate: STANDARD_AUDIO_CONFIG.sampleRate,
    smart_format: STANDARD_AUDIO_CONFIG.smartFormat,
    container: STANDARD_AUDIO_CONFIG.container,
  };
}

/**
 * Apply sample alignment to ensure even-length chunks
 * This prevents partial sample artifacts that cause clicks
 */
export function applySampleAlignment(chunk: Uint8Array): Uint8Array {
  // Ensure chunk length is even (complete samples for 16-bit audio)
  return chunk.length % 2 === 0 ? chunk : chunk.slice(0, -1);
}

/**
 * Calculate sample-accurate duration to prevent timing precision errors
 */
export function calculateSampleAccurateDuration(audioBuffer: AudioBuffer): number {
  return audioBuffer.length / audioBuffer.sampleRate;
}

/**
 * Remove DC offset from PCM data to prevent level jumps
 */
export function removeDCOffset(pcmData: Int16Array): Int16Array {
  // Calculate DC offset (average value)
  let sum = 0;
  for (let i = 0; i < pcmData.length; i++) {
    sum += pcmData[i];
  }
  const dcOffset = sum / pcmData.length;
  
  // Remove DC offset
  const correctedData = new Int16Array(pcmData.length);
  for (let i = 0; i < pcmData.length; i++) {
    correctedData[i] = pcmData[i] - dcOffset;
  }
  
  return correctedData;
}

/**
 * Standard audio processing pipeline
 */
export function processAudioChunk(chunk: Uint8Array): {
  alignedChunk: Uint8Array;
  pcmData: Int16Array;
  processedPcmData: Int16Array;
} {
  // Step 1: Apply sample alignment
  const alignedChunk = applySampleAlignment(chunk);
  
  // Step 2: Convert to PCM
  const pcmData = new Int16Array(alignedChunk.buffer, alignedChunk.byteOffset, alignedChunk.byteLength / 2);
  
  // Step 3: Remove DC offset
  const processedPcmData = removeDCOffset(pcmData);
  
  return {
    alignedChunk,
    pcmData,
    processedPcmData,
  };
}

/**
 * Validation function to ensure configuration compliance
 */
export function validateTTSConfiguration(config: any): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check sample rate
  if (config.sampleRate !== STANDARD_AUDIO_CONFIG.sampleRate) {
    issues.push(`Sample rate should be ${STANDARD_AUDIO_CONFIG.sampleRate}, found ${config.sampleRate}`);
    recommendations.push('Use consistent 24kHz sample rate across all endpoints');
  }
  
  // Check smart format
  if (config.smartFormat !== true) {
    issues.push('Smart format should be enabled for proper number pronunciation');
    recommendations.push('Set smart_format=true for natural number pronunciation');
  }
  
  // Check format consistency
  if (config.format && config.format !== 'linear16' && config.format !== 'mp3') {
    issues.push(`Unsupported format: ${config.format}`);
    recommendations.push('Use linear16 for streaming or mp3 for non-streaming');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}

// Export all voice models for reference
export { AVAILABLE_VOICES, DEFAULT_VOICE, getVoiceById, getVoiceModel } from './voice-config';

// Note: Tier configurations are available in './middleware/tierMiddleware' for server-side use
