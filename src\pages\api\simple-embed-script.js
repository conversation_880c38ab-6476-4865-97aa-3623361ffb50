// This is a special route that bypasses all authentication
module.exports = function handler(req, res) {
  // Handle OPTIONS requests for CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
    res.setHeader('Access-Control-Max-Age', '86400');
    res.setHeader('X-Frame-Options', '');
    res.setHeader('Content-Security-Policy', "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;");
    return res.status(204).end();
  }
  try {
    // Get the chat ID from the query parameters
    const { chatId } = req.query;

    if (!chatId) {
      return res.status(400).json({ error: 'Chat ID is required' });
    }

    // Get the origin from the request or use the host
    const host = req.headers.host || 'localhost:3000';
    const protocol = host.includes('localhost') ? 'http' : 'https';
    const origin = `${protocol}://${host}`;

    // Create the embed script content directly
    const scriptContent = `/**
 * BotFusion Simple Embed Script
 * This script is designed to be as simple as possible for maximum compatibility
 * Version: 1.0.0
 */

(function() {
  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#3b82f6';
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '9999';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

  // Create the SVG icon
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Add hover effect
  button.onmouseover = function() {
    this.style.transform = 'scale(1.05)';
    this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  };

  button.onmouseout = function() {
    this.style.transform = 'scale(1)';
    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  };

  // Create the iframe element (hidden initially)
  var iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'none';
  iframe.allow = 'microphone';

  // Add click event to button
  button.onclick = function() {
    iframe.style.display = 'block';
    this.style.display = 'none';

    // Only set the src when the iframe is first opened
    if (!iframe.src) {
      iframe.src = '${origin}/embed/${chatId}';
    }
  };

  // Add message listener for close events
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      iframe.style.display = 'none';
      button.style.display = 'flex';
    }
  });

  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(iframe);

  // Add responsive styles for mobile
  var style = document.createElement('style');
  style.textContent = '@media (max-width: 480px) { ' +
    '#botfusion-chat-iframe { width: 100%; height: 100%; bottom: 0; right: 0; border-radius: 0; }' +
  '}';
  document.head.appendChild(style);
})();`;

    // Set CORS headers to allow embedding from any domain
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
    res.setHeader('Content-Type', 'application/javascript');
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('X-Frame-Options', '');
    res.setHeader('Content-Security-Policy', "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;");

    // Return the script
    return res.status(200).send(scriptContent);
  } catch (error) {
    console.error('Error serving embed script:', error);
    return res.status(500).json({ error: 'Failed to serve embed script' });
  }
}
