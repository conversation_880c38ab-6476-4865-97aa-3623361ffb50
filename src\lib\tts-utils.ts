/**
 * Utility functions for text-to-speech processing
 */

// Maximum text length for TTS processing (characters) - optimized for faster processing
export const MAX_TTS_TEXT_LENGTH = 1800;

// Supported languages and their codes
export const SUPPORTED_LANGUAGES = {
  'en': 'English',
  'es': 'Spanish',
  'fr': 'French',
  'de': 'German',
  'it': 'Italian',
  'pt': 'Portuguese',
  'nl': 'Dutch',
  'pl': 'Polish',
  'ru': 'Russian',
  'ja': 'Japanese',
  'ko': 'Korean',
  'zh': 'Chinese'
} as const;

// Voice model mapping by language
export const VOICE_MODELS_BY_LANGUAGE: Record<keyof typeof SUPPORTED_LANGUAGES, readonly string[]> = {
  'en': ['aura-2-thalia-en', 'aura-2-arcas-en', 'aura-2-apollo-en', 'aura-2-helena-en', 'aura-2-zeus-en', 'aura-2-asteria-en'],
  'es': ['aura-2-helena-es'],
  'fr': ['aura-2-helena-fr'],
  'de': ['aura-2-helena-de'],
  'it': ['aura-2-helena-it'],
  'pt': ['aura-2-helena-pt'],
  'nl': ['aura-2-helena-nl'],
  'pl': ['aura-2-helena-pl'],
  'ru': ['aura-2-helena-ru'],
  'ja': ['aura-2-helena-ja'],
  'ko': ['aura-2-helena-ko'],
  'zh': ['aura-2-helena-zh']
} as const;

/**
 * Normalizes text for TTS processing
 * 
 * @param text The text to normalize
 * @returns Normalized text
 */
export function normalizeText(text: string): string {
  if (!text) return '';
  
  return text
    // Replace multiple spaces with a single space
    .replace(/\s+/g, ' ')
    // Replace multiple newlines with a single space
    .replace(/\n+/g, ' ')
    // Replace multiple punctuation marks with a single one
    .replace(/([.,!?])\1+/g, '$1')
    // Add space after punctuation if missing
    .replace(/([.,!?])([^\s])/g, '$1 $2')
    // Remove leading/trailing whitespace
    .trim();
}

/**
 * Detects the language of the text
 * 
 * @param text The text to detect language for
 * @returns Detected language code or 'en' as fallback
 */
export function detectLanguage(text: string): keyof typeof SUPPORTED_LANGUAGES {
  // Simple language detection based on character frequency
  const charFreq: Record<string, number> = {};
  for (const char of text) {
    charFreq[char] = (charFreq[char] || 0) + 1;
  }

  // Language-specific character frequency patterns
  const patterns = {
    'zh': /[\u4e00-\u9fff]/g,
    'ja': /[\u3040-\u309f\u30a0-\u30ff]/g,
    'ko': /[\uac00-\ud7af\u1100-\u11ff]/g,
    'ru': /[\u0400-\u04FF]/g,
    'es': /[áéíóúñ¿¡]/g,
    'fr': /[àâçéèêëîïôûùüÿœæ]/g,
    'de': /[äöüß]/g,
    'it': /[àèéìíîòóùú]/g,
    'pt': /[áàâãéêíóôõúç]/g,
    'nl': /[éëïöü]/g,
    'pl': /[ąćęłńóśźż]/g
  };

  // Check each language pattern
  for (const [lang, pattern] of Object.entries(patterns)) {
    const matches = text.match(pattern);
    if (matches && matches.length > text.length * 0.1) {
      return lang as keyof typeof SUPPORTED_LANGUAGES;
    }
  }

  // Default to English if no specific pattern is detected
  return 'en';
}

/**
 * Gets appropriate voice model for the text
 * 
 * @param text The text to get voice model for
 * @param preferredVoice Optional preferred voice model
 * @returns Appropriate voice model
 */
export function getVoiceModel(text: string, preferredVoice?: string): string {
  const detectedLang = detectLanguage(text);
  const availableVoices = VOICE_MODELS_BY_LANGUAGE[detectedLang];
  
  // If preferred voice is available for the detected language, use it
  if (preferredVoice && availableVoices.includes(preferredVoice as any)) {
    return preferredVoice;
  }
  
  // Otherwise use the first available voice for the detected language
  return availableVoices[0];
}

/**
 * Validates if text is suitable for TTS processing
 * 
 * @param text The text to validate
 * @returns Object with validation result and error message if any
 */
export function validateTTSText(text: string): { valid: boolean; error?: string; message?: string } {
  if (!text || text.trim() === '') {
    return { 
      valid: false, 
      error: 'Text is required',
      message: 'Please provide text to convert to speech'
    };
  }
  
  if (text.length > MAX_TTS_TEXT_LENGTH) {
    return { 
      valid: false, 
      error: 'Text is too long for TTS processing',
      message: `Maximum allowed length is ${MAX_TTS_TEXT_LENGTH} characters, but received ${text.length} characters.`
    };
  }
  
  return { valid: true };
}

/**
 * Processes text for TTS
 * 
 * @param text The text to process
 * @param preferredVoice Optional preferred voice model
 * @returns Processed text and appropriate voice model
 */
export function processTextForTTS(text: string, preferredVoice?: string): { 
  processedText: string; 
  voiceModel: string;
  language: string;
} {
  const normalizedText = normalizeText(text);
  const detectedLang = detectLanguage(normalizedText);
  const voiceModel = getVoiceModel(normalizedText, preferredVoice);
  
  return {
    processedText: normalizedText,
    voiceModel,
    language: detectedLang
  };
}

/**
 * Splits long text into smaller chunks for TTS processing
 * 
 * @param text The text to split
 * @returns Array of text chunks
 */
export function splitTextIntoChunks(text: string): string[] {
  if (!text || text.length <= MAX_TTS_TEXT_LENGTH) {
    return [text || ''];
  }

  const chunks: string[] = [];
  let currentText = text;

  while (currentText.length > 0) {
    // Find the last space or punctuation within MAX_TTS_TEXT_LENGTH
    let splitIndex = currentText.substring(0, MAX_TTS_TEXT_LENGTH).lastIndexOf(' ');
    if (splitIndex === -1) {
      splitIndex = currentText.substring(0, MAX_TTS_TEXT_LENGTH).lastIndexOf('.');
    }
    if (splitIndex === -1) {
      splitIndex = currentText.substring(0, MAX_TTS_TEXT_LENGTH).lastIndexOf('!');
    }
    if (splitIndex === -1) {
      splitIndex = currentText.substring(0, MAX_TTS_TEXT_LENGTH).lastIndexOf('?');
    }
    if (splitIndex === -1 || splitIndex === 0) {
      // If no space/punctuation found or it's at the beginning, split at max length
      splitIndex = MAX_TTS_TEXT_LENGTH;
    }

    const chunk = currentText.substring(0, splitIndex).trim();
    if (chunk) {
      chunks.push(chunk);
    }
    currentText = currentText.substring(splitIndex).trim();
  }

  return chunks;
}
