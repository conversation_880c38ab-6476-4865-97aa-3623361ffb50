/**
 * BotFusion Standalone Embed Script
 * This script is designed to be as simple as possible for maximum compatibility
 * Version: 1.0.0
 */

(function() {
  // Get the script tag
  var scripts = document.getElementsByTagName('script');
  var script = scripts[scripts.length - 1];
  
  // Get the chat ID from the data-chat-id attribute
  var chatId = script.getAttribute('data-chat-id');
  if (!chatId) {
    console.error('BotFusion: Missing data-chat-id attribute');
    return;
  }
  
  // Get the base URL from the data-base-url attribute
  var baseUrl = script.getAttribute('data-base-url');
  if (!baseUrl) {
    console.error('BotFusion: Missing data-base-url attribute');
    return;
  }
  
  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#3b82f6';
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '9999';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
  
  // Create the SVG icon
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';
  
  // Add hover effect
  button.onmouseover = function() {
    this.style.transform = 'scale(1.05)';
    this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  };
  
  button.onmouseout = function() {
    this.style.transform = 'scale(1)';
    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  };
  
  // Create the iframe element (hidden initially)
  var iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'none';
  iframe.allow = 'microphone';
  
  // Add click event to button
  button.onclick = function() {
    iframe.style.display = 'block';
    this.style.display = 'none';
    
    // Only set the src when the iframe is first opened - use chat-only mode for widget
    if (!iframe.src) {
      iframe.src = baseUrl + '/embed/' + chatId + '?mode=chat-only&embed=true';
    }
  };
  
  // Add message listener for close events
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      iframe.style.display = 'none';
      button.style.display = 'flex';
    }
  });
  
  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(iframe);
  
  // Add responsive styles for mobile
  var style = document.createElement('style');
  style.textContent = '@media (max-width: 480px) { ' +
    '#botfusion-chat-iframe { width: 100%; height: 100%; bottom: 0; right: 0; border-radius: 0; }' +
  '}';
  document.head.appendChild(style);
})();
