'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function SimpleEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  const completeEmbedCode = `<!-- BotFusion Chat Widget -->
<style>
  #botfusion-chat-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    z-index: 9999;
    cursor: pointer;
    display: block;
  }

  #botfusion-chat-iframe {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    border: none;
    border-radius: 12px;
    box-shadow: none !important;
    z-index: 9999;
    display: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  #botfusion-chat-iframe.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
  }

  @media (max-width: 480px) {
    #botfusion-chat-iframe {
      width: 100%;
      height: 100%;
      bottom: 0;
      right: 0;
      border-radius: 0;
    }

    #botfusion-chat-button {
      bottom: 20px;
      right: 20px;
    }
  }
</style>

<!-- Chat Button -->
<iframe id="botfusion-chat-button" src="${baseUrl}/embed/${chatId}?floating=true" allow="microphone" loading="lazy" title="Chat Button"></iframe>

<!-- Chat Window -->
<iframe id="botfusion-chat-iframe" src="${baseUrl}/embed/${chatId}" allow="microphone" loading="lazy" importance="high" title="Chat Widget"></iframe>

<script>
(function() {
  var chatButton = document.getElementById('botfusion-chat-button');
  var chatIframe = document.getElementById('botfusion-chat-iframe');
  var isOpen = false;

  // Listen for messages from the button iframe
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-toggle') {
      if (isOpen) {
        // Close the chat
        chatIframe.style.display = 'none';
        chatIframe.classList.remove('show');
        isOpen = false;
      } else {
        // Open the chat
        chatIframe.style.display = 'block';
        chatIframe.classList.add('show');
        isOpen = true;
      }
    }
  });
})();
</script>`

  const handleCopy = () => {
    navigator.clipboard.writeText(completeEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-neon-blue">Chat Widget Embed</h3>
        </div>
        <p className="text-sm text-gray-400">
          This creates a complete chat widget with a floating button that users can click to open/close the chat. Perfect for any website - just copy and paste this code into your HTML.
        </p>
      </div>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs z-10 bg-gray-800 hover:bg-gray-700"
          >
            {copied ? "Copied!" : "Copy Code"}
          </Button>
        </div>
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300 overflow-x-auto max-h-[300px] whitespace-pre-wrap">
            <code>{completeEmbedCode}</code>
          </pre>
        </div>
      </div>
      <div className="text-sm text-gray-400 mt-2">
        <p>This solution creates a floating chat button that users can click to open/close the chat. It works with any website and includes all necessary HTML, CSS, and JavaScript in one code block.</p>
      </div>
    </div>
  )
}
