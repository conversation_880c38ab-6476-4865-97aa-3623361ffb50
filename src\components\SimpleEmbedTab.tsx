'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function SimpleEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  // NEW MOBILE-OPTIMIZED EMBED CODE (FIXES ALL MOBILE ISSUES)
  const completeEmbedCode = `<!-- BotFusion Mobile-Optimized Chat Widget -->
<!-- FIXES: Mobile scrolling, touch events, button blocking, navigation issues -->
<!-- UNIVERSAL COMPATIBILITY: WordPress, Durable.co, Shopify, custom sites -->
<script nonce="REPLACE_WITH_YOUR_NONCE" type="text/javascript">
// Set configuration before loading the widget
window.BOTFUSION_CHAT_ID = '${chatId}';
window.BOTFUSION_BASE_URL = '${baseUrl}';
window.BOTFUSION_PRIMARY_COLOR = '#3b82f6';
window.BOTFUSION_DEBUG = false; // Set to true for debugging

// Mobile-optimized widget loader
(function() {
  'use strict';

  // Prevent multiple instances
  if (window.BotFusionWidgetLoaded) return;
  window.BotFusionWidgetLoaded = true;

  // Configuration
  const config = {
    chatId: window.BOTFUSION_CHAT_ID,
    baseUrl: window.BOTFUSION_BASE_URL,
    primaryColor: window.BOTFUSION_PRIMARY_COLOR || '#3b82f6',
    debug: window.BOTFUSION_DEBUG || false
  };

  if (config.debug) console.log('BotFusion: Initializing mobile-optimized widget', config);

  // Create styles with mobile optimizations
  const styles = document.createElement('style');
  styles.textContent = \`
    #botfusion-widget-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 2147483647;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #botfusion-chat-button {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: \${config.primaryColor};
      border: none;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      position: relative;
    }

    #botfusion-chat-button:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }

    #botfusion-chat-button svg {
      width: 24px;
      height: 24px;
      fill: white;
    }

    #botfusion-chat-iframe {
      position: fixed;
      bottom: 90px;
      right: 20px;
      width: 400px;
      height: 600px;
      border: none;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      background: white;
      transform: scale(0.8) translateY(20px);
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 2147483646;
    }

    #botfusion-chat-iframe.open {
      transform: scale(1) translateY(0);
      opacity: 1;
      visibility: visible;
    }

    /* Mobile optimizations - CRITICAL FOR MOBILE COMPATIBILITY */
    @media (max-width: 768px) {
      #botfusion-widget-container {
        bottom: 10px;
        right: 10px;
      }

      #botfusion-chat-button {
        width: 56px;
        height: 56px;
      }

      #botfusion-chat-iframe {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        border-radius: 0 !important;
        z-index: 2147483647 !important;
      }

      #botfusion-chat-iframe.open {
        transform: none !important;
      }
    }

    /* Prevent body scroll when chat is open on mobile */
    body.botfusion-chat-open {
      overflow: hidden !important;
      position: fixed !important;
      width: 100% !important;
    }
  \`;
  document.head.appendChild(styles);

  // Create widget container
  const container = document.createElement('div');
  container.id = 'botfusion-widget-container';

  // Create chat button
  const button = document.createElement('button');
  button.id = 'botfusion-chat-button';
  button.innerHTML = \`
    <svg viewBox="0 0 24 24">
      <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
    </svg>
  \`;

  // Create iframe
  const iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.src = \`\${config.baseUrl}/embed/\${config.chatId}?mode=chat-only&embed=true\`;
  iframe.allow = 'microphone; autoplay';
  iframe.title = 'Chat Widget';

  // Mobile-optimized toggle functionality
  let isOpen = false;

  function toggleChat() {
    isOpen = !isOpen;

    if (isOpen) {
      iframe.classList.add('open');
      // Prevent body scroll on mobile
      if (window.innerWidth <= 768) {
        document.body.classList.add('botfusion-chat-open');
      }
      if (config.debug) console.log('BotFusion: Chat opened');
    } else {
      iframe.classList.remove('open');
      // Restore body scroll
      document.body.classList.remove('botfusion-chat-open');
      if (config.debug) console.log('BotFusion: Chat closed');
    }
  }

  // Button click handler
  button.addEventListener('click', toggleChat);

  // Listen for close messages from iframe
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-close-chat') {
      if (isOpen) toggleChat();
    }
  });

  // Handle escape key
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isOpen) {
      toggleChat();
    }
  });

  // Assemble and inject widget
  container.appendChild(button);
  container.appendChild(iframe);
  document.body.appendChild(container);

  if (config.debug) console.log('BotFusion: Mobile-optimized widget loaded successfully');
})();
</script>

<!-- CSP-Friendly Alternative (if the above script doesn't work due to CSP) -->
<!-- Remove the script above and uncomment this iframe version: -->
<!--
<iframe
  src="${baseUrl}/embed/${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; z-index: 9999; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
  allow="microphone; autoplay"
  title="${chatName} Chat">
</iframe>

<style>
  @media (max-width: 768px) {
    iframe[title="${chatName} Chat"] {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      border-radius: 0 !important;
      z-index: 2147483647 !important;
    }
  }
</style>
-->`

  const handleCopy = () => {
    navigator.clipboard.writeText(completeEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-neon-blue">Chat Widget Embed</h3>
        </div>
        <p className="text-sm text-gray-400">
          <strong>🚀 NEW Mobile-Optimized Version!</strong> This creates a chat widget with a floating button that users can click to open/close the chat.
          <br /><br />
          <strong>✅ FIXES:</strong> Mobile scrolling issues, touch events, button blocking, navigation problems
          <br />
          <strong>📱 MOBILE:</strong> Full-screen on mobile, desktop-sized on desktop/tablet
          <br />
          <strong>🌐 UNIVERSAL:</strong> Works on WordPress, Durable.co, Shopify, custom sites
          <br /><br />
          Perfect for any website - just copy and paste this code into your HTML. If the script version doesn't work due to Content Security Policy, use the commented iframe alternative at the bottom.
        </p>
      </div>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs z-10 bg-gray-800 hover:bg-gray-700"
          >
            {copied ? "Copied!" : "Copy Code"}
          </Button>
        </div>
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300 overflow-x-auto max-h-[300px] whitespace-pre-wrap">
            <code>{completeEmbedCode}</code>
          </pre>
        </div>
      </div>
      <div className="text-sm text-gray-400 mt-2">
        <p><strong>How it works:</strong> Creates a floating chat button that users can click to toggle the chat open/closed. On desktop/tablet, the chat window opens next to the button. On mobile, it opens full-screen for optimal user experience.</p>
        <br />
        <p><strong>Two versions included:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li><strong>Script version (recommended):</strong> Advanced mobile optimizations, better performance</li>
          <li><strong>iframe version (fallback):</strong> Use if your site has strict Content Security Policy</li>
        </ul>
      </div>
    </div>
  )
}
