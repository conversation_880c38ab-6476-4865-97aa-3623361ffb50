'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function SimpleEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  const completeEmbedCode = `<!-- BotFusion Chat Widget -->
<!-- This works exactly like the iframe and direct embed - uses the chat widget's own button -->
<iframe
  src="${baseUrl}/embed/${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); z-index: 9999;"
  allow="microphone; camera; autoplay"
  title="${chatName} Chat">
</iframe>

<style>
  @media (max-width: 480px) {
    iframe[title="${chatName} Chat"] {
      width: 100% !important;
      height: 100% !important;
      bottom: 0 !important;
      right: 0 !important;
      border-radius: 0 !important;
    }
  }
</style>`

  const handleCopy = () => {
    navigator.clipboard.writeText(completeEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-neon-blue">Chat Widget Embed</h3>
        </div>
        <p className="text-sm text-gray-400">
          This creates a chat widget with a floating button that users can click to open/close the chat. The chat window opens next to the button so it doesn't cover it. Perfect for any website - just copy and paste this code into your HTML.
        </p>
      </div>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs z-10 bg-gray-800 hover:bg-gray-700"
          >
            {copied ? "Copied!" : "Copy Code"}
          </Button>
        </div>
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300 overflow-x-auto max-h-[300px] whitespace-pre-wrap">
            <code>{completeEmbedCode}</code>
          </pre>
        </div>
      </div>
      <div className="text-sm text-gray-400 mt-2">
        <p>This solution creates a floating chat button that users can click to toggle the chat open/closed. The chat window opens to the left of the button so it doesn't cover it. Works with any website and is highly compatible.</p>
      </div>
    </div>
  )
}
