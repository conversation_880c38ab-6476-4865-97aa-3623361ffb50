'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function SimpleEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  const completeEmbedCode = `<!-- BotFusion Chat Widget -->
<style>
  #botfusion-chat-iframe {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    border: none;
    border-radius: 12px;
    box-shadow: none !important;
    z-index: 9999;
  }

  @media (max-width: 480px) {
    #botfusion-chat-iframe {
      width: 100%;
      height: 100%;
      bottom: 0;
      right: 0;
      border-radius: 0;
    }
  }
</style>

<iframe id="botfusion-chat-iframe" src="${baseUrl}/embed/${chatId}" allow="microphone" loading="lazy" importance="high" title="Chat Widget"></iframe>`

  const handleCopy = () => {
    navigator.clipboard.writeText(completeEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-neon-blue">Chat Widget Embed</h3>
        </div>
        <p className="text-sm text-gray-400">
          This is a simple, self-contained solution for embedding the chat widget on any website. Just copy and paste this code into your HTML.
        </p>
      </div>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs z-10 bg-gray-800 hover:bg-gray-700"
          >
            {copied ? "Copied!" : "Copy Code"}
          </Button>
        </div>
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300 overflow-x-auto max-h-[300px] whitespace-pre-wrap">
            <code>{completeEmbedCode}</code>
          </pre>
        </div>
      </div>
      <div className="text-sm text-gray-400 mt-2">
        <p>This solution works with any website and doesn't require any external scripts or API calls. The chat widget will be embedded directly in your page.</p>
      </div>
    </div>
  )
}
