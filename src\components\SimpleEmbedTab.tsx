'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function SimpleEmbedTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [copied, setCopied] = useState(false)
  const [baseUrl, setBaseUrl] = useState('')

  useEffect(() => {
    // Get the base URL from the environment or use the current origin
    setBaseUrl(process.env.NEXT_PUBLIC_BASE_URL || window.location.origin)
  }, [])

  const completeEmbedCode = `<!-- BotFusion Chat Widget -->
<style>
  #botfusion-chat-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
  }

  #botfusion-chat-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  #botfusion-chat-iframe {
    position: fixed;
    bottom: 20px;
    right: 90px;
    width: 400px;
    height: 600px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  @media (max-width: 480px) {
    #botfusion-chat-iframe {
      width: 100%;
      height: 100%;
      bottom: 0;
      right: 0;
      border-radius: 0;
    }
  }
</style>

<!-- Chat Button -->
<div id="botfusion-chat-button">
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
</div>

<!-- Chat Window -->
<iframe
  id="botfusion-chat-iframe"
  src="${baseUrl}/embed/${chatId}"
  allow="microphone; camera; autoplay"
  title="${chatName} Chat">
</iframe>

<script>
(function() {
  var button = document.getElementById('botfusion-chat-button');
  var iframe = document.getElementById('botfusion-chat-iframe');
  var isOpen = false;

  // Add click event to button
  button.onclick = function() {
    if (isOpen) {
      // Close the chat
      iframe.style.opacity = '0';
      iframe.style.transform = 'translateY(20px)';
      setTimeout(function() {
        iframe.style.display = 'none';
      }, 300);
      isOpen = false;
    } else {
      // Open the chat
      iframe.style.display = 'block';
      iframe.style.opacity = '0';
      iframe.style.transform = 'translateY(20px)';
      setTimeout(function() {
        iframe.style.opacity = '1';
        iframe.style.transform = 'translateY(0)';
      }, 10);
      isOpen = true;
    }
  };

  // Listen for close messages from the iframe
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      iframe.style.opacity = '0';
      iframe.style.transform = 'translateY(20px)';
      setTimeout(function() {
        iframe.style.display = 'none';
      }, 300);
      isOpen = false;
    }
  });
})();
</script>`

  const handleCopy = () => {
    navigator.clipboard.writeText(completeEmbedCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-neon-blue">Chat Widget Embed</h3>
        </div>
        <p className="text-sm text-gray-400">
          This creates a simple chat widget that displays directly on your website. Perfect for any website - just copy and paste this code into your HTML.
        </p>
      </div>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="h-8 gap-1 text-xs z-10 bg-gray-800 hover:bg-gray-700"
          >
            {copied ? "Copied!" : "Copy Code"}
          </Button>
        </div>
        <div className="overflow-x-auto rounded-md bg-gray-950 p-4">
          <pre className="text-sm text-gray-300 overflow-x-auto max-h-[300px] whitespace-pre-wrap">
            <code>{completeEmbedCode}</code>
          </pre>
        </div>
      </div>
      <div className="text-sm text-gray-400 mt-2">
        <p>This solution creates a simple chat widget that displays directly on your website. It works with any website and is the most compatible option.</p>
      </div>
    </div>
  )
}
