// <PERSON>ript to update the chat_stats table with the latest data from chat_messages
import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables from .env.local
config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateChatStatsTable() {
  try {
    console.log('Starting chat_stats table update...');

    // First, run the migration to add the chat_interface_id column if it doesn't exist
    console.log('Adding chat_interface_id column to chat_stats table if it doesn\'t exist...');
    const { error: alterError } = await supabase.rpc('execute_sql', {
      sql: `
        ALTER TABLE IF EXISTS chat_stats
        ADD COLUMN IF NOT EXISTS chat_interface_id UUID REFERENCES chat_interfaces(id) ON DELETE CASCADE;

        CREATE INDEX IF NOT EXISTS idx_chat_stats_chat_interface_id ON chat_stats(chat_interface_id);

        ALTER TABLE IF EXISTS chat_stats
        ADD COLUMN IF NOT EXISTS last_message_at TIMESTAMP WITH TIME ZONE;
      `
    });

    if (alterError) {
      console.error('Error altering chat_stats table:', alterError);
      return;
    }

    // Get all chat interfaces
    console.log('Fetching all chat interfaces...');
    const { data: chatInterfaces, error: chatError } = await supabase
      .from('chat_interfaces')
      .select('id');

    if (chatError) {
      console.error('Error fetching chat interfaces:', chatError);
      return;
    }

    console.log(`Found ${chatInterfaces.length} chat interfaces`);

    // For each chat interface, update the stats
    for (const chat of chatInterfaces) {
      const chatId = chat.id;
      console.log(`Processing chat interface ${chatId}...`);

      // Get all sessions for this chat interface
      const { data: sessions, error: sessionsError } = await supabase
        .from('chat_messages')
        .select('session_id')
        .eq('chat_interface_id', chatId)
        .eq('sender', 'user');

      if (sessionsError) {
        console.error(`Error getting sessions for chat ${chatId}:`, sessionsError);
        continue;
      }

      // Get unique session IDs
      const uniqueSessionIds = sessions && sessions.length > 0
        ? [...new Set(sessions.map(msg => msg.session_id))]
        : [];

      console.log(`Found ${uniqueSessionIds.length} unique sessions for chat ${chatId}`);

      // For each session, update or insert stats
      for (const sessionId of uniqueSessionIds) {
        // Count messages for this session
        const { count: messageCount, error: countError } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('chat_interface_id', chatId)
          .eq('session_id', sessionId);

        if (countError) {
          console.error(`Error counting messages for session ${sessionId}:`, countError);
          continue;
        }

        // Get last activity for this session
        const { data: lastActivity, error: lastActivityError } = await supabase
          .from('chat_messages')
          .select('created_at')
          .eq('chat_interface_id', chatId)
          .eq('session_id', sessionId)
          .order('created_at', { ascending: false })
          .limit(1);

        if (lastActivityError) {
          console.error(`Error getting last activity for session ${sessionId}:`, lastActivityError);
          continue;
        }

        const lastActivityTime = lastActivity && lastActivity.length > 0 ? lastActivity[0].created_at : null;

        // Check if stats already exist for this session
        const { data: existingStats, error: existingStatsError } = await supabase
          .from('chat_stats')
          .select('id')
          .eq('chat_interface_id', chatId)
          .eq('session_id', sessionId)
          .limit(1);

        if (existingStatsError) {
          console.error(`Error checking existing stats for session ${sessionId}:`, existingStatsError);
          continue;
        }

        if (existingStats && existingStats.length > 0) {
          // Update existing stats
          const { error: updateError } = await supabase
            .from('chat_stats')
            .update({
              message_count: messageCount,
              last_message_at: lastActivityTime,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingStats[0].id);

          if (updateError) {
            console.error(`Error updating stats for session ${sessionId}:`, updateError);
          } else {
            console.log(`Updated stats for session ${sessionId}: ${messageCount} messages, last activity: ${lastActivityTime}`);
          }
        } else {
          // Insert new stats
          const { error: insertError } = await supabase
            .from('chat_stats')
            .insert({
              chat_interface_id: chatId,
              session_id: sessionId,
              message_count: messageCount,
              last_message_at: lastActivityTime,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error(`Error inserting stats for session ${sessionId}:`, insertError);
          } else {
            console.log(`Inserted stats for session ${sessionId}: ${messageCount} messages, last activity: ${lastActivityTime}`);
          }
        }
      }
    }

    console.log('Chat stats table update completed successfully!');
  } catch (error) {
    console.error('Error updating chat stats table:', error);
  }
}

// Run the update as an immediately invoked async function
(async () => {
  try {
    await updateChatStatsTable();
    console.log('Script completed');
    process.exit(0);
  } catch (err) {
    console.error('Script failed:', err);
    process.exit(1);
  }
})();
