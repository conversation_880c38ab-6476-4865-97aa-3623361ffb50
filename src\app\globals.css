@import "tailwindcss";
@import "./hover-effects.css";
@import "./production-fixes.css";
@import "./landing-page-fixes.css";
@import "./pricing-fixes.css";

@custom-variant dark (&:is(.dark *));

/* Font smoothing for crisp text on all devices */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  font-variant-ligatures: common-ligatures;
  font-optical-sizing: auto;
}

/* Additional text smoothing for headings and important text */
h1, h2, h3, h4, h5, h6,
.text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl, .text-6xl,
.font-bold, .font-semibold, .font-medium {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Ensure buttons and interactive elements have smooth text */
button, [role="button"], .btn, .button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

:root {
  --background-dark: #0a0a14;
  --background-gradient-start: #0a0a14;
  --background-gradient-end: #121224;
  --card-bg: rgba(10, 10, 20, 0.6);
  --card-border: rgba(0, 50, 100, 0.5);
  --neon-blue: #3b82f6; /* Updated to blue-500 */
  --neon-purple: #9d00ff;
  --text-primary: #ffffff;
  --text-secondary: #c0c0c0;
  --glow-color: rgba(255, 255, 255, 0.4);
  --glow-shadow: 0 0 3px rgba(255, 255, 255, 0.3), 0 0 6px rgba(255, 255, 255, 0.1);
  --button-bg: rgba(0, 64, 128, 0.1);
  --button-hover-bg: rgba(0, 64, 128, 0.3);
  --button-border: #004080;
  --grid-line-color: rgba(0, 64, 128, 0.1);

  --color-background: var(--background-dark);
  --color-foreground: var(--text-primary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--card-border);
  --color-sidebar-border: var(--card-border);
  --color-sidebar-accent-foreground: var(--text-primary);
  --color-sidebar-accent: var(--neon-blue);
  --color-sidebar-primary-foreground: var(--text-primary);
  --color-sidebar-primary: var(--neon-blue);
  --color-sidebar-foreground: var(--text-primary);
  --color-sidebar: var(--background-dark);
  --color-chart-5: var(--neon-purple);
  --color-chart-4: var(--neon-blue);
  --color-chart-3: var(--neon-purple);
  --color-chart-2: var(--neon-blue);
  --color-chart-1: var(--neon-purple);
  --color-ring: var(--neon-blue);
  --color-input: var(--background-dark);
  --color-border: var(--card-border);
  --color-destructive: #ff0040;
  --color-accent-foreground: #ffffff;
  --color-accent: var(--neon-blue);
  --color-muted-foreground: var(--text-secondary);
  --color-muted: rgba(0, 255, 255, 0.2);
  --color-secondary-foreground: var(--text-secondary);
  --color-secondary: var(--neon-purple);
  --color-primary-foreground: #ffffff;
  --color-primary: var(--neon-blue);
  --color-popover-foreground: #ffffff;
  --color-popover: var(--background-dark);
}

@keyframes blob {
  0%, 100% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.03);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.97);
  }
}

.animate-blob {
  animation: blob 15s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-6000 {
  animation-delay: 6s;
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 4px var(--glow-color));
}

/* Ensure no shadow on any element */
iframe[title="Chat Widget"],
#botfusion-chat-iframe,
.chat-window,
#chat-iframe,
.chat-interface,
.chat-interface *,
.card,
[data-slot="card"],
[class*="shadow"],
[class*="shadow-sm"],
[class*="shadow-md"],
[class*="shadow-lg"],
[class*="shadow-xl"],
[class*="shadow-2xl"] {
  box-shadow: none !important;
  filter: none !important;
}

/* Make iframe backgrounds transparent */
iframe[title="Chat Widget"],
#botfusion-chat-iframe,
.chat-window,
#chat-iframe {
  background-color: transparent !important;
}

.text-glow-blue {
  text-shadow: 1px 1px 1px black, 0 0 2px var(--glow-color);
  color: var(--text-primary);
}

.shadow-glow-blue {
  box-shadow: 0 0 4px var(--glow-color);
}

.button-glow-hover:hover {
  box-shadow: 0 0 8px var(--glow-color);
  transition: box-shadow 0.5s ease-in-out;
}

.ambient-glow {
  box-shadow: 0 0 2px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.2);
}

.pulse-glow {
  animation: pulse-glow 4s infinite;
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
  50% { box-shadow: 0 0 6px rgba(59, 130, 246, 0.4); }
  100% { box-shadow: 0 0 2px rgba(59, 130, 246, 0.2); }
}

.glass-card {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--card-border);
  border-radius: 16px;
  box-shadow: var(--glow-shadow);
  transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out;
}

.glass-card:hover {
  box-shadow: 0 0 12px var(--glow-color);
  border-color: rgba(59, 130, 246, 0.5);
}

button, [role="button"] {
  cursor: pointer;
}

.glass-button {
  background: var(--card-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  box-shadow: var(--glow-shadow);
  transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out;
}

.glass-button:hover {
  box-shadow: 0 0 12px var(--glow-color);
  border-color: rgba(59, 130, 246, 0.5);
}

.tech-grid-bg {
  background-color: var(--background-dark);
  background-image:
    radial-gradient(circle at 0 0, var(--grid-line-color) 1px, transparent 2px),
    radial-gradient(circle at 20px 20px, var(--grid-line-color) 1px, transparent 2px);
  background-size: 40px 40px;
}

@keyframes neon-border {
  0% { border-color: rgba(255, 255, 255, 0.3); }
  50% { border-color: rgba(255, 255, 255, 0.8); }
  100% { border-color: rgba(255, 255, 255, 0.3); }
}

.neon-border {
  animation: neon-border 3s infinite;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
}

/* Default Select dropdown styling - only applies when no custom styling is provided */
[data-radix-select-content]:not([class*="bg-"]) {
  background-color: white;
  color: black;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

[data-radix-select-item]:not([class*="text-"]):not([class*="bg-"]) {
  color: black;
  background-color: white;
}

[data-radix-select-item]:not([class*="text-"]):not([class*="bg-"]):hover,
[data-radix-select-item]:not([class*="text-"]):not([class*="bg-"])[data-highlighted] {
  background-color: #f3f4f6;
  color: black;
}

[data-radix-select-viewport]:not([class*="bg-"]) {
  background-color: white;
}

/* Ensure Select components inherit proper colors when styled */
[data-radix-select-trigger] {
  color: inherit;
}

[data-radix-select-value] {
  color: inherit;
}

/* Ensure text is always visible in Select items */
[data-radix-select-item] {
  font-size: 14px;
  line-height: 1.4;
  cursor: pointer;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 2px;
}

[data-radix-select-item] *,
[data-radix-select-item] span {
  font-size: inherit;
  color: inherit;
}

[data-radix-select-label] {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 12px;
}

/* Force purple theme for Select components with purple classes */
[data-radix-select-content].bg-purple-600,
[data-radix-select-content][class*="bg-purple"] {
  background-color: rgb(147 51 234) !important;
  border-color: rgb(168 85 247) !important;
  color: white !important;
}

[data-radix-select-item].bg-purple-600,
[data-radix-select-item][class*="bg-purple"] {
  background-color: rgb(147 51 234) !important;
  color: white !important;
}

[data-radix-select-item].bg-purple-600:hover,
[data-radix-select-item][class*="bg-purple"]:hover,
[data-radix-select-item].bg-purple-600[data-highlighted],
[data-radix-select-item][class*="bg-purple"][data-highlighted] {
  background-color: rgb(126 34 206) !important;
  color: white !important;
}

/* Additional specificity for Radix UI Select components */
[data-radix-popper-content-wrapper] [data-radix-select-content][class*="bg-purple"] {
  background-color: rgb(147 51 234) !important;
  border-color: rgb(168 85 247) !important;
}

[data-radix-popper-content-wrapper] [data-radix-select-item][class*="bg-purple"] {
  background-color: rgb(147 51 234) !important;
  color: white !important;
}

[data-radix-popper-content-wrapper] [data-radix-select-item][class*="bg-purple"]:hover,
[data-radix-popper-content-wrapper] [data-radix-select-item][class*="bg-purple"][data-highlighted] {
  background-color: rgb(126 34 206) !important;
  color: white !important;
}
