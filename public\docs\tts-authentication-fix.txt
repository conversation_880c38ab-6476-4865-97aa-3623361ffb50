// TTS Client Implementation

// HTML Structure
/*
<div id="tts-container">
  <textarea id="text-to-speak" rows="4" placeholder="Enter text to speak..."></textarea>
  <div class="controls">
    <select id="voice-select">
      <option value="nova">Nova</option>
      <option value="alloy">Alloy</option>
      <option value="echo">Echo</option>
      <option value="fable">Fable</option>
      <option value="onyx">Onyx</option>
      <option value="shimmer">Shimmer</option>
    </select>
    <button id="speak-button">Generate Speech</button>
  </div>
  <audio id="tts-audio" controls></audio>
  <div id="status-message"></div>
</div>
*/

// JavaScript Implementation
document.addEventListener('DOMContentLoaded', () => {
  const textInput = document.getElementById('text-to-speak');
  const voiceSelect = document.getElementById('voice-select');
  const speakButton = document.getElementById('speak-button');
  const audioElement = document.getElementById('tts-audio');
  const statusMessage = document.getElementById('status-message');
  
  // Authentication token - should be provided by your backend
  // This could be fetched from localStorage, a cookie, or an API
  let authToken = ''; // You'll need to set this based on your auth system
  
  // Function to get auth token - implement according to your auth system
  async function getAuthToken() {
    // If you already have a token, return it
    if (authToken) return authToken;
    
    try {
      // Example: Fetch token from your auth endpoint
      const response = await fetch('/api/auth/token', {
        method: 'GET',
        credentials: 'include', // Include cookies for session-based auth
      });
      
      if (!response.ok) {
        throw new Error('Failed to get authentication token');
      }
      
      const data = await response.json();
      authToken = data.token;
      return authToken;
    } catch (error) {
      console.error('Auth error:', error);
      setStatus('Authentication failed. Please try again or refresh the page.', 'error');
      throw error;
    }
  }
  
  speakButton.addEventListener('click', async () => {
    const text = textInput.value.trim();
    if (!text) {
      setStatus('Please enter some text to speak', 'error');
      return;
    }
    
    const voice = voiceSelect.value;
    
    try {
      setStatus('Generating audio...', 'info');
      
      // Get auth token
      let token;
      try {
        token = await getAuthToken();
      } catch (authError) {
        // Auth error already handled in getAuthToken
        return;
      }
      
      // Add a cache-busting parameter (timestamp)
      const timestamp = new Date().getTime();
      
      // Create a URL with proper encoding
      const ttsUrl = new URL('/api/simple-tts', window.location.origin);
      ttsUrl.searchParams.append('text', text);
      ttsUrl.searchParams.append('voice', voice);
      ttsUrl.searchParams.append('t', timestamp);
      
      // Make the request with authentication
      const response = await fetch(ttsUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          // Add any other headers required by your API
        },
        credentials: 'include', // Include cookies if needed
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized specifically - clear token and potentially retry
          authToken = '';
          throw new Error('Authentication failed. Please log in again.');
        }
        throw new Error(`API request failed: ${response.status}`);
      }
      
      // Get audio data as blob
      const audioBlob = await response.blob();
      
      // Create a safe URL from the blob
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Clear any previous audio
      if (audioElement.src) {
        URL.revokeObjectURL(audioElement.src);
      }
      
      // Set the audio source
      audioElement.src = audioUrl;
      
      // Set up event listeners for the audio element
      audioElement.onloadedmetadata = () => {
        audioElement.play().catch(error => {
          console.error('Play error:', error);
          
          // Check if it's an autoplay restriction
          if (error.name === 'NotAllowedError') {
            setStatus('Autoplay blocked by browser. Please click play.', 'warning');
          } else {
            setStatus(`Error playing audio: ${error.message}`, 'error');
          }
        });
      };
      
      audioElement.onended = () => {
        console.log('Audio playback completed');
      };
      
      audioElement.onerror = (event) => {
        console.error('Audio error:', audioElement.error);
        setStatus(`Audio error: ${audioElement.error.message}`, 'error');
      };
      
      setStatus('Audio generated successfully', 'success');
      
    } catch (error) {
      console.error('Error generating speech:', error);
      setStatus(`Error: ${error.message}`, 'error');
    }
  });
  
  function setStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = type;
  }
});
