<!DOCTYPE html>
<html>
<head>
    <title>WebSocket TTS Test</title>
</head>
<body>
    <h1>WebSocket TTS Connection Test</h1>
    <button onclick="testWebSocket()">Test WebSocket Connection</button>
    <div id="status"></div>
    <div id="log"></div>

    <script type="module">
        import { createClient, LiveTTSEvents } from 'https://cdn.jsdelivr.net/npm/@deepgram/sdk/+esm';

        const DEEPGRAM_API_KEY = '****************************************';

        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<p>' + new Date().toISOString() + ': ' + message + '</p>';
            console.log(message);
        }

        function updateStatus(status) {
            document.getElementById('status').innerHTML = '<h2>Status: ' + status + '</h2>';
        }

        window.testWebSocket = function() {
            log('Starting Deepgram SDK WebSocket TTS test...');
            updateStatus('Connecting...');

            try {
                log('Creating Deepgram client...');
                const deepgram = createClient(DEEPGRAM_API_KEY);

                log('Establishing WebSocket connection...');
                const connection = deepgram.speak.live({
                    model: 'aura-2-thalia-en',
                    encoding: 'mp3',
                    smart_format: false
                });

                connection.on(LiveTTSEvents.Open, () => {
                    log('✅ WebSocket connection opened successfully!');
                    updateStatus('Connected');

                    // Send test text
                    const testText = 'Hello, this is a test of streaming text-to-speech using the Deepgram SDK.';
                    log('Sending test text: ' + testText);
                    connection.sendText(testText);

                    // Flush to signal end of input
                    connection.flush();
                });

                connection.on(LiveTTSEvents.Audio, (data) => {
                    log('📦 Received audio chunk: ' + (data.buffer ? data.buffer.byteLength : 'unknown size') + ' bytes');
                    updateStatus('Receiving audio');
                });

                connection.on(LiveTTSEvents.Error, (error) => {
                    log('❌ WebSocket error: ' + JSON.stringify(error));
                    updateStatus('Error');
                });

                connection.on(LiveTTSEvents.Close, () => {
                    log('🔌 WebSocket closed successfully');
                    updateStatus('Closed');
                });

            } catch (error) {
                log('❌ Failed to create WebSocket: ' + error.message);
                updateStatus('Failed');
            }
        }
    </script>
</body>
</html>
