# Vercel Deployment Guide

This guide will help you deploy your BotFusion X application to Vercel with the correct environment variables.

## Prerequisites

- A Vercel account
- A Supabase project with the following credentials:
  - Supabase URL
  - Supabase Anon Key
  - Supabase Service Role Key

## Deployment Steps

### 1. Import Your GitHub Repository

1. Log in to your Vercel account
2. Click "Add New" > "Project"
3. Select your GitHub repository
4. Click "Import"

### 2. Configure Project Settings

1. Keep the default framework preset as "Next.js"
2. Set the root directory to the project root (leave blank if your project is at the root)
3. Set the build command to `npm run build` (this should be detected automatically)
4. Set the output directory to `.next` (this should be detected automatically)

### 3. Add Environment Variables

This is the critical step where you need to add your Supabase credentials:

1. Scroll down to the "Environment Variables" section
2. Add the following environment variables:

   | Name | Value |
   |------|-------|
   | `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL (e.g., `https://yourproject.supabase.co`) |
   | `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Your Supabase anonymous key |
   | `SUPABASE_SERVICE_ROLE_KEY` | Your Supabase service role key |

   > **Important**: Make sure to copy the exact values from your Supabase project dashboard or your local `.env.local` file.

4. Click "Add" for each environment variable
5. Ensure all environment variables are correctly added before proceeding

### 4. Deploy

1. Click "Deploy"
2. Wait for the build and deployment to complete

### 5. Verify Deployment

1. Once deployment is complete, click on the deployment URL to open your application
2. Verify that your application is working correctly
3. Check the logs if you encounter any issues

## Troubleshooting

If you encounter issues with your deployment, check the following:

1. **Environment Variables**: Ensure all environment variables are correctly set in the Vercel project settings.
2. **Build Logs**: Check the build logs for any errors related to missing environment variables.
3. **Supabase Connection**: Verify that your Supabase project is accessible and that the credentials are correct.
4. **CORS Settings**: Make sure your Supabase project has the correct CORS settings to allow requests from your Vercel deployment URL.

## Supabase CORS Configuration

To configure CORS in your Supabase project:

1. Go to your Supabase project dashboard
2. Navigate to Project Settings > API
3. Under "CORS (Cross-Origin Resource Sharing)", add your Vercel deployment URL (e.g., `https://your-project.vercel.app`)
4. Save the changes

## Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
