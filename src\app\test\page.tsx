import React from 'react';

export default function TestPage() {
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">BotFusion Embed Test Page</h1>
      
      <div className="grid gap-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-blue-600">Method 1: Direct Script Embed</h2>
          <p className="mb-4">This method adds a floating chat button to your website that opens the chat in a popup.</p>
          
          <div className="bg-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto mb-4">
            {`<script 
    src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
    data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
    data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
    async 
    defer
></script>`}
          </div>
          
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <strong className="font-medium">Note:</strong> The chat button should appear in the bottom-right corner of this page.
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-blue-600">Method 2: Iframe Embed</h2>
          <p className="mb-4">This method embeds the chat directly in your page using an iframe.</p>
          
          <div className="w-full h-[500px] border border-gray-200 rounded-lg overflow-hidden mb-4">
            <iframe 
              src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
              allow="microphone" 
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
              loading="lazy"
              importance="high"
              referrerPolicy="origin"
              fetchPriority="high"
              title="BotFusion Chat"
              className="w-full h-full border-none"
            ></iframe>
          </div>
        </div>
      </div>

      {/* Direct Script Embed */}
      <script 
        src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
        data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
        data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
        async 
        defer
      ></script>
    </div>
  );
}
