import { readFileSync } from 'fs';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';

// This is a special API route that serves the direct embed script
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId') || '';
    
    if (!chatId) {
      return new NextResponse('console.error("Error: Missing chatId parameter");', {
        status: 400,
        headers: {
          'Content-Type': 'application/javascript; charset=utf-8',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }

    // Get the base URL from the environment or use the current origin
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;
    
    // Read the direct embed script from the public directory
    const scriptPath = path.join(process.cwd(), 'public', 'direct-embed.js');
    let scriptContent = readFileSync(scriptPath, 'utf8');

    // Build the embed URL with parameters
    let embedUrl = `${baseUrl}/embed/${chatId}`;
    
    // Add any additional parameters from the query string
    const params = new URLSearchParams();
    
    // Copy all parameters except chatId
    for (const [key, value] of request.nextUrl.searchParams.entries()) {
      if (key !== 'chatId') {
        params.append(key, value);
      }
    }
    
    // Add a timestamp to prevent caching
    params.append('t', Date.now().toString());
    
    // Append parameters to the URL if there are any
    if (params.toString()) {
      embedUrl += `?${params.toString()}`;
    }
    
    // Replace the placeholder with the actual URL
    scriptContent = scriptContent.replace('EMBED_URL_PLACEHOLDER', embedUrl);

    // Return the script with proper CORS headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Content-Type-Options': 'nosniff',
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    });
  } catch (error) {
    console.error('Error serving direct embed script:', error);
    return new NextResponse('console.error("Error loading BotFusion Chat Widget");', {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
    },
  });
}
