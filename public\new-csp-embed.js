/**
 * BotFusion CSP-friendly embed script
 * This script handles the communication between the parent page and the iframe
 * for sites with strict Content Security Policy (CSP) settings.
 * Version: 2.1.0 - Added toggle functionality for mobile-friendly UX
 */

(function() {
  // Configuration - will be replaced with actual values
  const config = {
    chatId: "CHAT_ID_PLACEHOLDER",
    origin: "ORIGIN_PLACEHOLDER",
    primaryColor: "#3b82f6",
    userBubbleColor: "#ffffff",
    botBubbleColor: "#3b82f6",
    userTextColor: "#000000",
    botTextColor: "#ffffff",
    logoUrl: "",
    darkMode: false,
    greeting: "Hello! How can I help you today?"
  };

  // State tracking for toggle functionality
  let chatIsOpen = false;

  // No button iframe needed - embed code handles the button

  // Create the chat iframe (initially hidden)
  const chatIframe = document.createElement('iframe');

  // Build the URL with parameters
  let chatUrl = config.origin + "/embed/" + config.chatId + "?";
  chatUrl += "primaryColor=" + encodeURIComponent(config.primaryColor);
  chatUrl += "&userBubbleColor=" + encodeURIComponent(config.userBubbleColor);
  chatUrl += "&botBubbleColor=" + encodeURIComponent(config.botBubbleColor);
  chatUrl += "&userTextColor=" + encodeURIComponent(config.userTextColor);
  chatUrl += "&botTextColor=" + encodeURIComponent(config.botTextColor);
  chatUrl += "&logoUrl=" + encodeURIComponent(config.logoUrl);
  chatUrl += "&darkMode=" + (config.darkMode ? 'true' : 'false');
  chatUrl += "&t=" + new Date().getTime(); // Cache busting

  // Set iframe properties
  chatIframe.src = chatUrl;
  chatIframe.style.position = 'fixed';
  chatIframe.style.bottom = '20px';
  chatIframe.style.right = '90px'; // Position to the left of the button
  chatIframe.style.width = '400px';
  chatIframe.style.height = '600px';
  chatIframe.style.border = 'none';
  chatIframe.style.borderRadius = '12px';
  chatIframe.style.zIndex = '9999';
  chatIframe.style.display = 'none';
  chatIframe.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
  chatIframe.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
  chatIframe.id = 'botfusion-chat-window';
  chatIframe.title = 'Chat Window';
  chatIframe.setAttribute('loading', 'lazy');
  chatIframe.setAttribute('allow', 'microphone');

  // Add message listener to handle messages from the iframes
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframes
    if (event.data === 'botfusion-chat-open') {
      // Show the chat iframe
      chatIframe.style.display = 'block';
      chatIsOpen = true;

      // Add animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';

      setTimeout(function() {
        chatIframe.style.transform = 'translateY(0)';
        chatIframe.style.opacity = '1';
      }, 10);
    } else if (event.data === 'botfusion-chat-close') {
      // Add closing animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';
      chatIsOpen = false;

      setTimeout(function() {
        chatIframe.style.display = 'none';
      }, 300);
    } else if (event.data === 'botfusion-chat-toggle') {
      // Handle toggle request from button iframe
      if (chatIsOpen) {
        // Close the chat
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';
        chatIsOpen = false;

        setTimeout(function() {
          chatIframe.style.display = 'none';
        }, 300);
      } else {
        // Open the chat
        chatIframe.style.display = 'block';
        chatIsOpen = true;

        // Add animation
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';

        setTimeout(function() {
          chatIframe.style.transform = 'translateY(0)';
          chatIframe.style.opacity = '1';
        }, 10);
      }
    }
  });

  // Add mobile responsiveness
  function updateMobileStyles() {
    if (window.innerWidth <= 480) {
      // Mobile: full screen chat
      chatIframe.style.width = '100%';
      chatIframe.style.height = '100%';
      chatIframe.style.bottom = '0';
      chatIframe.style.right = '0';
      chatIframe.style.borderRadius = '0';
    } else {
      // Desktop: positioned chat
      chatIframe.style.width = '400px';
      chatIframe.style.height = '600px';
      chatIframe.style.bottom = '20px';
      chatIframe.style.right = '90px';
      chatIframe.style.borderRadius = '12px';
    }
  }

  // Apply mobile styles on load and resize
  updateMobileStyles();
  window.addEventListener('resize', updateMobileStyles);

  // Append chat iframe to the document (no button iframe needed)
  document.body.appendChild(chatIframe);
})();
