/**
 * BotFusion CSP-friendly embed script
 * This script handles the communication between the parent page and the iframe
 * for sites with strict Content Security Policy (CSP) settings.
 * Version: 2.1.0 - Added toggle functionality for mobile-friendly UX
 */

(function() {
  // Configuration - will be replaced with actual values
  const config = {
    chatId: "CHAT_ID_PLACEHOLDER",
    origin: "ORIGIN_PLACEHOLDER",
    primaryColor: "#3b82f6",
    userBubbleColor: "#ffffff",
    botBubbleColor: "#3b82f6",
    userTextColor: "#000000",
    botTextColor: "#ffffff",
    logoUrl: "",
    darkMode: false,
    greeting: "Hello! How can I help you today?"
  };

  // State tracking for toggle functionality
  let chatIsOpen = false;

  // Create the button iframe
  const buttonIframe = document.createElement('iframe');

  // Build the URL with parameters
  let buttonUrl = config.origin + "/embed/" + config.chatId + "?floating=true";
  buttonUrl += "&primaryColor=" + encodeURIComponent(config.primaryColor);
  buttonUrl += "&t=" + new Date().getTime(); // Cache busting

  // Set iframe properties
  buttonIframe.src = buttonUrl;
  buttonIframe.style.position = 'fixed';
  buttonIframe.style.bottom = '20px';
  buttonIframe.style.right = '20px';
  buttonIframe.style.width = '60px';
  buttonIframe.style.height = '60px';
  buttonIframe.style.border = 'none';
  buttonIframe.style.borderRadius = '50%';
  buttonIframe.style.zIndex = '9999';
  buttonIframe.id = 'botfusion-chat-button';
  buttonIframe.title = 'Chat Button';
  buttonIframe.setAttribute('loading', 'lazy');
  buttonIframe.setAttribute('allow', 'microphone');

  // Create the chat iframe (initially hidden)
  const chatIframe = document.createElement('iframe');

  // Build the URL with parameters
  let chatUrl = config.origin + "/embed/" + config.chatId + "?";
  chatUrl += "primaryColor=" + encodeURIComponent(config.primaryColor);
  chatUrl += "&userBubbleColor=" + encodeURIComponent(config.userBubbleColor);
  chatUrl += "&botBubbleColor=" + encodeURIComponent(config.botBubbleColor);
  chatUrl += "&userTextColor=" + encodeURIComponent(config.userTextColor);
  chatUrl += "&botTextColor=" + encodeURIComponent(config.botTextColor);
  chatUrl += "&logoUrl=" + encodeURIComponent(config.logoUrl);
  chatUrl += "&darkMode=" + (config.darkMode ? 'true' : 'false');
  chatUrl += "&t=" + new Date().getTime(); // Cache busting

  // Set iframe properties
  chatIframe.src = chatUrl;
  chatIframe.style.position = 'fixed';
  chatIframe.style.bottom = '20px';
  chatIframe.style.right = '20px';
  chatIframe.style.width = '400px';
  chatIframe.style.height = '600px';
  chatIframe.style.border = 'none';
  chatIframe.style.borderRadius = '12px';
  chatIframe.style.zIndex = '9999';
  chatIframe.style.display = 'none';
  chatIframe.style.boxShadow = 'none';
  chatIframe.id = 'botfusion-chat-window';
  chatIframe.title = 'Chat Window';
  chatIframe.setAttribute('loading', 'lazy');
  chatIframe.setAttribute('allow', 'microphone');

  // Add message listener to handle messages from the iframes
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframes
    if (event.data === 'botfusion-chat-open') {
      // Show the chat iframe
      chatIframe.style.display = 'block';
      chatIsOpen = true;

      // Move button to top of chat when open
      buttonIframe.style.bottom = '640px'; // 600px chat height + 20px spacing + 20px original bottom
      buttonIframe.style.zIndex = '10000'; // Higher than chat

      // Add animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';

      setTimeout(function() {
        chatIframe.style.transform = 'translateY(0)';
        chatIframe.style.opacity = '1';
      }, 10);
    } else if (event.data === 'botfusion-chat-close') {
      // Add closing animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';
      chatIsOpen = false;

      // Move button back to original position
      buttonIframe.style.bottom = '20px';
      buttonIframe.style.zIndex = '9999';

      setTimeout(function() {
        chatIframe.style.display = 'none';
      }, 300);
    } else if (event.data === 'botfusion-chat-toggle') {
      // Handle toggle request from button iframe
      if (chatIsOpen) {
        // Close the chat
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';
        chatIsOpen = false;

        // Move button back to original position
        buttonIframe.style.bottom = '20px';
        buttonIframe.style.zIndex = '9999';

        setTimeout(function() {
          chatIframe.style.display = 'none';
        }, 300);
      } else {
        // Open the chat
        chatIframe.style.display = 'block';
        chatIsOpen = true;

        // Move button to top of chat when open
        buttonIframe.style.bottom = '640px'; // 600px chat height + 20px spacing + 20px original bottom
        buttonIframe.style.zIndex = '10000'; // Higher than chat

        // Add animation
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';

        setTimeout(function() {
          chatIframe.style.transform = 'translateY(0)';
          chatIframe.style.opacity = '1';
        }, 10);
      }
    }
  });

  // Append elements to the document
  document.body.appendChild(buttonIframe);
  document.body.appendChild(chatIframe);
})();
