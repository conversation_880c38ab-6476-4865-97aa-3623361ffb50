/**
 * BotFusion CSP-friendly embed script
 * This script handles the communication between the parent page and the iframe
 * for sites with strict Content Security Policy (CSP) settings.
 * Version: 2.1.0 - Added toggle functionality for mobile-friendly UX
 */

(function() {
  // Configuration - will be replaced with actual values
  const config = {
    chatId: "CHAT_ID_PLACEHOLDER",
    origin: "ORIGIN_PLACEHOLDER",
    primaryColor: "#3b82f6",
    userBubbleColor: "#ffffff",
    botBubbleColor: "#3b82f6",
    userTextColor: "#000000",
    botTextColor: "#ffffff",
    logoUrl: "",
    darkMode: false,
    greeting: "Hello! How can I help you today?"
  };

  // State tracking for toggle functionality
  let chatIsOpen = false;

  // Create the button element (simple HTML button instead of iframe)
  const button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = config.primaryColor;
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '10000';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
  button.style.border = 'none';
  button.title = 'Chat Button';

  // Add chat icon
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Add hover effects
  button.addEventListener('mouseover', function() {
    button.style.transform = 'scale(1.05)';
    button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  });

  button.addEventListener('mouseout', function() {
    button.style.transform = 'scale(1)';
    button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  });

  // Create the chat iframe (initially hidden)
  const chatIframe = document.createElement('iframe');

  // Build the URL with parameters
  let chatUrl = config.origin + "/embed/" + config.chatId + "?";
  chatUrl += "primaryColor=" + encodeURIComponent(config.primaryColor);
  chatUrl += "&userBubbleColor=" + encodeURIComponent(config.userBubbleColor);
  chatUrl += "&botBubbleColor=" + encodeURIComponent(config.botBubbleColor);
  chatUrl += "&userTextColor=" + encodeURIComponent(config.userTextColor);
  chatUrl += "&botTextColor=" + encodeURIComponent(config.botTextColor);
  chatUrl += "&logoUrl=" + encodeURIComponent(config.logoUrl);
  chatUrl += "&darkMode=" + (config.darkMode ? 'true' : 'false');
  chatUrl += "&t=" + new Date().getTime(); // Cache busting

  // Set iframe properties
  chatIframe.src = chatUrl;
  chatIframe.style.position = 'fixed';
  chatIframe.style.bottom = '20px';
  chatIframe.style.right = '90px'; // Position to the left of the button
  chatIframe.style.width = '400px';
  chatIframe.style.height = '600px';
  chatIframe.style.border = 'none';
  chatIframe.style.borderRadius = '12px';
  chatIframe.style.zIndex = '9999';
  chatIframe.style.display = 'none';
  chatIframe.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
  chatIframe.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
  chatIframe.id = 'botfusion-chat-window';
  chatIframe.title = 'Chat Window';
  chatIframe.setAttribute('loading', 'lazy');
  chatIframe.setAttribute('allow', 'microphone');

  // Add message listener to handle messages from the iframes
  window.addEventListener('message', function(event) {
    // Check if the message is from our iframes
    if (event.data === 'botfusion-chat-open') {
      // Show the chat iframe
      chatIframe.style.display = 'block';
      chatIsOpen = true;

      // Add animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';

      setTimeout(function() {
        chatIframe.style.transform = 'translateY(0)';
        chatIframe.style.opacity = '1';
      }, 10);
    } else if (event.data === 'botfusion-chat-close') {
      // Add closing animation
      chatIframe.style.transform = 'translateY(20px)';
      chatIframe.style.opacity = '0';
      chatIsOpen = false;

      setTimeout(function() {
        chatIframe.style.display = 'none';
      }, 300);
    } else if (event.data === 'botfusion-chat-toggle') {
      // Handle toggle request from button iframe
      if (chatIsOpen) {
        // Close the chat
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';
        chatIsOpen = false;

        setTimeout(function() {
          chatIframe.style.display = 'none';
        }, 300);
      } else {
        // Open the chat
        chatIframe.style.display = 'block';
        chatIsOpen = true;

        // Add animation
        chatIframe.style.transform = 'translateY(20px)';
        chatIframe.style.opacity = '0';

        setTimeout(function() {
          chatIframe.style.transform = 'translateY(0)';
          chatIframe.style.opacity = '1';
        }, 10);
      }
    }
  });

  // Add mobile responsiveness
  function updateMobileStyles() {
    if (window.innerWidth <= 480) {
      // Mobile: full screen chat
      chatIframe.style.width = '100%';
      chatIframe.style.height = '100%';
      chatIframe.style.bottom = '0';
      chatIframe.style.right = '0';
      chatIframe.style.borderRadius = '0';
    } else {
      // Desktop: positioned chat
      chatIframe.style.width = '400px';
      chatIframe.style.height = '600px';
      chatIframe.style.bottom = '20px';
      chatIframe.style.right = '90px';
      chatIframe.style.borderRadius = '12px';
    }
  }

  // Apply mobile styles on load and resize
  updateMobileStyles();
  window.addEventListener('resize', updateMobileStyles);

  // Append elements to the document
  document.body.appendChild(buttonIframe);
  document.body.appendChild(chatIframe);
})();
