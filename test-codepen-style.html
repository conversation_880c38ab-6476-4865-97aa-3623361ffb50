<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodePen Style Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .iframe-container {
            width: 400px;
            height: 600px;
            margin: 20px auto;
            border: 1px solid #ddd;
            border-radius: 12px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>CodePen Style Test</h1>

    <div class="important">
        <strong>Important:</strong> This page is for testing the CodePen style iframe embed. Please open the browser console (F12) to see any errors.
    </div>

    <div class="iframe-container">
        <iframe
            src="https://roo-bot-fusion-kgfs.vercel.app/embed/d0530217-17ad-4b5b-ad56-45cb8f6ddd9e"
            width="100%"
            height="600px"
            frameborder="0"
            allow="microphone"
            title="BotFusion Chat"
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
            loading="lazy"
            importance="high"
            referrerpolicy="origin"
            fetchpriority="high"
            style="border-radius: 12px; box-shadow: 0 4px 20px rgb(0, 0, 0, 0.15);"
        ></iframe>
    </div>

    <!-- Note about embedding -->
    <div class="important">
        <strong>Note:</strong> For embedding in production sites, you should ensure your server sets appropriate
        headers to allow embedding. CSP meta tags don't work for frame-ancestors directive.
    </div>
</body>
</html>
