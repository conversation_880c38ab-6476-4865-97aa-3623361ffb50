'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { toast } from 'sonner'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  onReset?: () => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * Error boundary component specifically designed to catch and handle
 * speech recognition related errors.
 */
class SpeechRecognitionErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to the console
    console.error('Speech Recognition Error Boundary caught an error:', error, errorInfo)
    
    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo)
    
    // Update state with error info
    this.setState({
      errorInfo
    })
    
    // Show a toast notification
    toast.error('Speech recognition encountered an error. Attempting to recover...')
    
    // Attempt to recover automatically after a short delay
    setTimeout(() => {
      this.handleReset()
    }, 2000)
  }

  handleReset = (): void => {
    // Reset the error boundary state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
    
    // Call the onReset callback if provided
    this.props.onReset?.()
    
    // Show a toast notification
    toast.success('Speech recognition has been reset')
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // If a fallback UI is provided, render it
      if (this.props.fallback) {
        return this.props.fallback
      }
      
      // Otherwise, render a default fallback UI
      return (
        <div className="speech-recognition-error p-4 rounded-md bg-red-50 border border-red-200">
          <h3 className="text-red-800 font-medium mb-2">Speech Recognition Error</h3>
          <p className="text-red-600 mb-4">
            There was an error with the speech recognition functionality.
          </p>
          <button
            onClick={this.handleReset}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
          {this.state.error && (
            <details className="mt-4">
              <summary className="text-sm text-red-600 cursor-pointer">Error Details</summary>
              <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">
                {this.state.error.toString()}
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </pre>
            </details>
          )}
        </div>
      )
    }

    // If there's no error, render the children
    return this.props.children
  }
}

export default SpeechRecognitionErrorBoundary
