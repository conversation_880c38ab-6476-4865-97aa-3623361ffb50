-- Create Test Accounts for Tier System Testing
-- This script creates 3 dummy test accounts (one for each tier) for comprehensive testing
-- Run this script in your Supabase SQL editor or via psql

-- Note: These are dummy accounts for testing purposes only
-- The auth.users table is managed by Supabase Auth, so we'll create user_profiles directly
-- and assume the auth users exist or will be created manually

-- 1. FREE TIER TEST ACCOUNT
-- Email: <EMAIL>
-- Password: TestFree123!
INSERT INTO user_profiles (
  id,
  tier,
  subscription_status,
  subscription_start_date,
  created_at,
  updated_at
) VALUES (
  '********-1111-1111-1111-********1111', -- UUID for free tier test user
  'free',
  'active',
  NOW(),
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  tier = EXCLUDED.tier,
  subscription_status = EXCLUDED.subscription_status,
  updated_at = NOW();

-- 2. STANDARD TIER TEST ACCOUNT  
-- Email: <EMAIL>
-- Password: TestStandard123!
INSERT INTO user_profiles (
  id,
  tier,
  subscription_status,
  subscription_start_date,
  subscription_end_date,
  created_at,
  updated_at
) VALUES (
  '********-2222-2222-2222-********2222', -- UUID for standard tier test user
  'standard',
  'active',
  NOW(),
  NOW() + INTERVAL '1 year', -- Active subscription for 1 year
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  tier = EXCLUDED.tier,
  subscription_status = EXCLUDED.subscription_status,
  subscription_end_date = EXCLUDED.subscription_end_date,
  updated_at = NOW();

-- 3. PRO TIER TEST ACCOUNT
-- Email: <EMAIL>  
-- Password: TestPro123!
INSERT INTO user_profiles (
  id,
  tier,
  subscription_status,
  subscription_start_date,
  subscription_end_date,
  created_at,
  updated_at
) VALUES (
  '********-3333-3333-3333-********3333', -- UUID for pro tier test user
  'pro',
  'active',
  NOW(),
  NOW() + INTERVAL '1 year', -- Active subscription for 1 year
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  tier = EXCLUDED.tier,
  subscription_status = EXCLUDED.subscription_status,
  subscription_end_date = EXCLUDED.subscription_end_date,
  updated_at = NOW();

-- Create sample chat interfaces for testing tier limits

-- Free tier user gets 1 chat interface (at their limit)
INSERT INTO chat_interfaces (
  id,
  user_id,
  name,
  description,
  webhookurl,
  unique_url,
  embed_code,
  primary_color,
  use_gradient_header,
  gradient_start_color,
  gradient_end_color,
  gradient_direction,
  user_bubble_color,
  bot_bubble_color,
  user_text_color,
  bot_text_color,
  dark_mode,
  show_name,
  use_black_outline,
  show_powered_by,
  powered_by_text,
  powered_by_url,
  powered_by_text_color,
  welcome_message,
  enable_natural_speech,
  voice_model,
  natural_speech_model,
  natural_speech_temperature,
  natural_speech_max_tokens,
  createdat
) VALUES (
  'free-test-chat-1',
  '********-1111-1111-1111-********1111',
  'Free Tier Test Chat',
  'Test chat interface for free tier user',
  'https://webhook.site/test-free-webhook',
  'https://botfusion.io/chat/free-test-chat-1',
  '<iframe src="https://botfusion.io/embed/free-test-chat-1" width="100%" height="600px"></iframe>',
  '#3b82f6', -- Basic blue color (no advanced customization)
  false, -- No gradient header (requires Standard+)
  '#3b82f6',
  '#9333ea',
  'to bottom',
  '#ffffff', -- Default colors (no customization)
  '#3b82f6',
  '#000000',
  '#ffffff',
  false,
  true,
  false,
  true, -- Must show powered by (free tier)
  'Powered by BotFusion X', -- Required branding text
  'https://botfusion.io', -- Required branding URL
  '#3b82f6',
  'Welcome! I''m here to help you.',
  false, -- No natural speech (requires Pro)
  'thalia', -- Default voice (but voice features disabled)
  'gpt-4o-mini',
  0.7,
  500,
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  updated_at = NOW();

-- Standard tier user gets 2 chat interfaces (showing they can have unlimited)
INSERT INTO chat_interfaces (
  id,
  user_id,
  name,
  description,
  webhookurl,
  unique_url,
  embed_code,
  primary_color,
  use_gradient_header,
  gradient_start_color,
  gradient_end_color,
  gradient_direction,
  user_bubble_color,
  bot_bubble_color,
  user_text_color,
  bot_text_color,
  dark_mode,
  show_name,
  use_black_outline,
  show_powered_by,
  powered_by_text,
  powered_by_url,
  powered_by_text_color,
  welcome_message,
  enable_natural_speech,
  voice_model,
  natural_speech_model,
  natural_speech_temperature,
  natural_speech_max_tokens,
  createdat
) VALUES 
(
  'standard-test-chat-1',
  '********-2222-2222-2222-********2222',
  'Standard Tier Test Chat 1',
  'First test chat interface for standard tier user with advanced customization',
  'https://webhook.site/test-standard-webhook-1',
  'https://botfusion.io/chat/standard-test-chat-1',
  '<iframe src="https://botfusion.io/embed/standard-test-chat-1" width="100%" height="600px"></iframe>',
  '#10b981', -- Custom color
  true, -- Gradient header enabled (Standard+ feature)
  '#10b981', -- Custom gradient colors
  '#3b82f6',
  'to right',
  '#f0fdf4', -- Custom bubble colors (Standard+ feature)
  '#10b981',
  '#166534', -- Custom text colors (Standard+ feature)
  '#ffffff',
  false,
  true,
  false,
  false, -- Branding removed (Standard+ feature)
  'Powered by BotFusion X', -- Default text (can't customize on Standard)
  'https://botfusion.io', -- Default URL (can't customize on Standard)
  '#10b981',
  'Hello! Welcome to our support chat. How can I assist you today?',
  false, -- No natural speech (requires Pro)
  'asteria', -- Standard tier voice model
  'gpt-4o-mini',
  0.7,
  500,
  NOW()
),
(
  'standard-test-chat-2',
  '********-2222-2222-2222-********2222',
  'Standard Tier Test Chat 2',
  'Second test chat interface for standard tier user',
  'https://webhook.site/test-standard-webhook-2',
  'https://botfusion.io/chat/standard-test-chat-2',
  '<iframe src="https://botfusion.io/embed/standard-test-chat-2" width="100%" height="600px"></iframe>',
  '#8b5cf6', -- Purple theme
  true,
  '#8b5cf6',
  '#ec4899',
  'to bottom',
  '#faf5ff',
  '#8b5cf6',
  '#581c87',
  '#ffffff',
  true, -- Dark mode
  true,
  true, -- Black outline
  true, -- Show branding (optional on Standard)
  'Powered by BotFusion X',
  'https://botfusion.io',
  '#8b5cf6',
  'Hi there! I''m your AI assistant.',
  false,
  'helena', -- Another Standard tier voice
  'gpt-4o-mini',
  0.7,
  500,
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  updated_at = NOW();

-- Pro tier user gets 3 chat interfaces (showing unlimited + all features)
INSERT INTO chat_interfaces (
  id,
  user_id,
  name,
  description,
  webhookurl,
  unique_url,
  embed_code,
  primary_color,
  use_gradient_header,
  gradient_start_color,
  gradient_end_color,
  gradient_direction,
  user_bubble_color,
  bot_bubble_color,
  user_text_color,
  bot_text_color,
  dark_mode,
  show_name,
  use_black_outline,
  show_powered_by,
  powered_by_text,
  powered_by_url,
  powered_by_text_color,
  welcome_message,
  enable_natural_speech,
  voice_model,
  natural_speech_model,
  natural_speech_temperature,
  natural_speech_max_tokens,
  createdat
) VALUES 
(
  'pro-test-chat-1',
  '********-3333-3333-3333-********3333',
  'Pro Tier Premium Chat',
  'Premium chat interface with all Pro features enabled',
  'https://webhook.site/test-pro-webhook-1',
  'https://botfusion.io/chat/pro-test-chat-1',
  '<iframe src="https://botfusion.io/embed/pro-test-chat-1" width="100%" height="600px"></iframe>',
  '#f59e0b',
  true,
  '#f59e0b',
  '#ef4444',
  'to right',
  '#fffbeb',
  '#f59e0b',
  '#92400e',
  '#ffffff',
  false,
  true,
  false,
  true, -- Custom branding enabled (Pro feature)
  'Powered by Acme Corp', -- Custom branding text (Pro feature)
  'https://acme.com', -- Custom branding URL (Pro feature)
  '#f59e0b',
  'Welcome to Acme Support! Our AI assistant is ready to help you with any questions.',
  true, -- Natural speech enabled (Pro feature)
  'apollo', -- Pro tier voice model (male voice)
  'gpt-4o', -- Premium AI model
  0.8,
  750,
  NOW()
),
(
  'pro-test-chat-2',
  '********-3333-3333-3333-********3333',
  'Pro White Label Chat',
  'Fully white-labeled chat interface',
  'https://webhook.site/test-pro-webhook-2',
  'https://botfusion.io/chat/pro-test-chat-2',
  '<iframe src="https://botfusion.io/embed/pro-test-chat-2" width="100%" height="600px"></iframe>',
  '#6366f1',
  true,
  '#6366f1',
  '#8b5cf6',
  'to bottom',
  '#eef2ff',
  '#6366f1',
  '#3730a3',
  '#ffffff',
  false,
  true,
  false,
  false, -- No branding (Pro white-labeling)
  '',
  '',
  '#6366f1',
  'Hello! I''m your personal AI assistant. What can I help you with today?',
  true,
  'zeus', -- Pro tier voice model
  'gpt-4o',
  0.9,
  1000,
  NOW()
),
(
  'pro-test-chat-3',
  '********-3333-3333-3333-********3333',
  'Pro Analytics Demo',
  'Chat interface for testing analytics features',
  'https://webhook.site/test-pro-webhook-3',
  'https://botfusion.io/chat/pro-test-chat-3',
  '<iframe src="https://botfusion.io/embed/pro-test-chat-3" width="100%" height="600px"></iframe>',
  '#059669',
  true,
  '#059669',
  '#0891b2',
  'to right',
  '#ecfdf5',
  '#059669',
  '#064e3b',
  '#ffffff',
  true, -- Dark mode
  true,
  true,
  true,
  'Powered by TechCorp Solutions', -- Custom branding
  'https://techcorp.example.com',
  '#059669',
  'Welcome to TechCorp! Our advanced AI is here to provide you with expert assistance.',
  true,
  'thalia', -- Female voice for variety
  'gpt-4o-mini',
  0.7,
  600,
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  updated_at = NOW();

-- Add some sample chat activity for analytics testing (Pro tier feature)
-- This would normally be done through the chat interfaces, but we'll add some dummy data

-- Note: You may need to create the chat_messages table if it doesn't exist
-- CREATE TABLE IF NOT EXISTS chat_messages (
--   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--   chat_interface_id TEXT REFERENCES chat_interfaces(id),
--   session_id TEXT,
--   message_type TEXT CHECK (message_type IN ('user', 'bot')),
--   content TEXT,
--   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
-- );

-- Sample chat sessions for analytics
-- INSERT INTO chat_messages (chat_interface_id, session_id, message_type, content, created_at) VALUES
-- ('pro-test-chat-3', 'session-1', 'user', 'Hello, I need help with my account', NOW() - INTERVAL '2 hours'),
-- ('pro-test-chat-3', 'session-1', 'bot', 'Hello! I''d be happy to help you with your account. What specific issue are you experiencing?', NOW() - INTERVAL '2 hours'),
-- ('pro-test-chat-3', 'session-2', 'user', 'What are your business hours?', NOW() - INTERVAL '1 hour'),
-- ('pro-test-chat-3', 'session-2', 'bot', 'Our business hours are Monday through Friday, 9 AM to 6 PM EST. Is there anything else I can help you with?', NOW() - INTERVAL '1 hour');

-- Display the created test accounts
SELECT 
  id,
  tier,
  subscription_status,
  subscription_start_date,
  subscription_end_date,
  created_at
FROM user_profiles 
WHERE id IN (
  '********-1111-1111-1111-********1111',
  '********-2222-2222-2222-********2222', 
  '********-3333-3333-3333-********3333'
)
ORDER BY tier;

-- Display the created chat interfaces
SELECT 
  id,
  user_id,
  name,
  tier_info.tier,
  show_powered_by,
  powered_by_text,
  use_gradient_header,
  enable_natural_speech,
  voice_model
FROM chat_interfaces ci
JOIN (
  SELECT id, tier FROM user_profiles 
  WHERE id IN (
    '********-1111-1111-1111-********1111',
    '********-2222-2222-2222-********2222',
    '********-3333-3333-3333-********3333'
  )
) tier_info ON ci.user_id = tier_info.id
ORDER BY tier_info.tier, ci.name;
