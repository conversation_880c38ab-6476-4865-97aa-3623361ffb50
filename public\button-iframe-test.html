<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Iframe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }

        /* Chat widget styles */
        #chat-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        #chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        #chat-iframe {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 600px;
            border: none;
            border-radius: 12px;
            box-shadow: none !important;
            filter: none !important;
            z-index: 9999;
            display: none;
        }

        @media (max-width: 480px) {
            #chat-iframe {
                width: 100%;
                height: 100%;
                bottom: 0;
                right: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <h1>Button Iframe Test</h1>

    <div class="important">
        <strong>Important:</strong> This page demonstrates a direct iframe implementation with a button.
    </div>

    <div class="content">
        <h2>How It Works</h2>
        <p>This page uses a direct iframe approach with a button:</p>
        <pre>&lt;div id="chat-button"&gt;
    &lt;svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
        &lt;path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"&gt;&lt;/path&gt;
    &lt;/svg&gt;
&lt;/div&gt;

&lt;iframe id="chat-iframe" src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" allow="microphone"&gt;&lt;/iframe&gt;

&lt;script&gt;
    // Get the elements
    const button = document.getElementById('chat-button');
    const iframe = document.getElementById('chat-iframe');

    // Add click event to button
    button.onclick = function() {
        iframe.style.display = 'block';
        this.style.display = 'none';
    };

    // Add message listener for close events
    window.addEventListener('message', function(event) {
        if (event.data === 'botfusion-chat-close') {
            iframe.style.display = 'none';
            button.style.display = 'flex';
        }
    });
&lt;/script&gt;</pre>
    </div>

    <!-- Chat Widget -->
    <div id="chat-button">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
    </div>

    <iframe id="chat-iframe" src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" allow="microphone"></iframe>

    <script>
        // Get the elements
        const button = document.getElementById('chat-button');
        const iframe = document.getElementById('chat-iframe');

        // Add click event to button
        button.onclick = function() {
            iframe.style.display = 'block';
            this.style.display = 'none';
        };

        // Add message listener for close events
        window.addEventListener('message', function(event) {
            if (event.data === 'botfusion-chat-close') {
                iframe.style.display = 'none';
                button.style.display = 'flex';
            }
        });
    </script>
</body>
</html>
