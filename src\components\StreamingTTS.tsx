'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface StreamingTTSProps {
  text: string;
  voiceModel: string;
  autoPlay?: boolean;
  onAudioReady?: (messageId: string) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onError?: (error: string) => void;
  messageId: string;
  enableNaturalSpeech?: boolean;
}

interface AudioChunk {
  data: ArrayBuffer;
  timestamp: number;
  chunkNumber: number;
}

export default function StreamingTTS({
  text,
  voiceModel,
  autoPlay = false,
  onAudioReady,
  onStart,
  onEnd,
  onError,
  messageId,
  enableNaturalSpeech = true
}: StreamingTTSProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // PRODUCTION: Monitoring and health tracking
  const [healthStatus, setHealthStatus] = useState<string>('idle');
  const [performanceMetrics, setPerformanceMetrics] = useState({
    firstChunkLatency: 0,
    totalProcessingTime: 0,
    chunksReceived: 0,
    averageChunkSize: 0,
    errorCount: 0,
    completionMethod: 'unknown'
  });

  // Production-optimized refs
  const connectionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioChunk[]>([]);
  const isPlayingRef = useRef(false);
  const audioReadyCalledRef = useRef(false);
  const lastProcessedTextRef = useRef<string>('');
  const currentAudioSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const nextPlayTimeRef = useRef<number>(0);
  const bufferQueueRef = useRef<AudioBuffer[]>([]);
  const isProcessingRef = useRef(false);

  // CHUNK ORDERING: Add refs for sequential chunk processing
  const chunkBufferRef = useRef<Map<number, AudioChunk>>(new Map());
  const nextExpectedChunkRef = useRef<number>(1);
  const totalChunksRef = useRef<number>(0);

  // CRITICAL FIX: Ultra-optimized Web Audio API initialization for minimal latency
  const initializeAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      try {
        // PERFORMANCE: Create context with optimal settings for streaming
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
          sampleRate: 24000, // Match Deepgram output exactly
          latencyHint: 'interactive' // Prioritize low latency over power consumption
        });

        // CRITICAL: Force context to running state immediately
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }

        // PERFORMANCE: Create optimized gain node for volume control
        gainNodeRef.current = audioContextRef.current.createGain();
        gainNodeRef.current.gain.value = 1.0; // Normal volume
        gainNodeRef.current.connect(audioContextRef.current.destination);

        console.log(`StreamingTTS: Web Audio API initialized - State: ${audioContextRef.current.state}, Sample Rate: ${audioContextRef.current.sampleRate}Hz`);
      } catch (error) {
        console.error('StreamingTTS: Failed to initialize Web Audio API:', error);
        onError?.(`Audio initialization error: ${error}`);
      }
    }
  }, [onError]);

  // PRODUCTION: Optimized PCM to AudioBuffer conversion with minimal overhead
  const createAudioBuffer = useCallback(async (pcmData: ArrayBuffer): Promise<AudioBuffer | null> => {
    if (!audioContextRef.current) {
      await initializeAudioContext();
    }

    try {
      const sampleRate = 24000; // Match Deepgram exactly
      const numberOfChannels = 1; // Mono audio
      const bytesPerSample = 2; // 16-bit = 2 bytes
      const numberOfSamples = pcmData.byteLength / bytesPerSample;

      // Validate data size
      if (numberOfSamples === 0) {
        console.warn('StreamingTTS: Empty PCM data received');
        return null;
      }

      // Create AudioBuffer with exact specifications
      const audioBuffer = audioContextRef.current!.createBuffer(
        numberOfChannels,
        numberOfSamples,
        sampleRate
      );

      // ANTI-CLICK FIX: Ultra-optimized PCM conversion with DC offset removal
      const channelData = audioBuffer.getChannelData(0);
      const pcmView = new Int16Array(pcmData);

      // ANTI-CLICK FIX: Calculate and remove DC offset to prevent clicks
      let dcOffset = 0;
      for (let i = 0; i < numberOfSamples; i++) {
        dcOffset += pcmView[i];
      }
      dcOffset = Math.round(dcOffset / numberOfSamples);

      // PERFORMANCE: Unrolled loop with pre-calculated scale and DC removal
      const scale = 1.0 / 32768.0; // Pre-calculate scale factor
      let i = 0;
      const len = numberOfSamples;

      // Process in chunks of 4 for better performance with DC offset removal
      while (i < len - 3) {
        channelData[i] = (pcmView[i] - dcOffset) * scale;
        channelData[i + 1] = (pcmView[i + 1] - dcOffset) * scale;
        channelData[i + 2] = (pcmView[i + 2] - dcOffset) * scale;
        channelData[i + 3] = (pcmView[i + 3] - dcOffset) * scale;
        i += 4;
      }

      // Handle remaining samples with DC offset removal
      while (i < len) {
        channelData[i] = (pcmView[i] - dcOffset) * scale;
        i++;
      }

      return audioBuffer;
    } catch (error) {
      console.error('StreamingTTS: Error creating AudioBuffer:', error);
      return null;
    }
  }, [initializeAudioContext]);

  // PRODUCTION: Seamless audio playback with crossfade to eliminate clicks
  const playAudioBuffer = useCallback(async (audioBuffer: AudioBuffer, isLastChunk: boolean = false): Promise<void> => {
    if (!audioContextRef.current || !gainNodeRef.current) {
      await initializeAudioContext();
    }

    try {
      const source = audioContextRef.current!.createBufferSource();
      source.buffer = audioBuffer;
      source.playbackRate.value = 1.0;

      // ANTI-CLICK FIX: Create a gain node for each source to enable smooth fade-in/out
      const sourceGain = audioContextRef.current!.createGain();
      source.connect(sourceGain);
      sourceGain.connect(gainNodeRef.current!);

      // CRITICAL FIX: Ultra-precise timing for seamless playback
      const currentTime = audioContextRef.current!.currentTime;
      let startTime: number;

      // PRECISION FIX: Sample-accurate scheduling to eliminate clicks
      if (nextPlayTimeRef.current === 0 || nextPlayTimeRef.current <= currentTime + 0.001) {
        // First chunk or recovery - start with minimal but safe delay
        startTime = currentTime + 0.005; // 5ms minimal delay for browser processing
        console.log('StreamingTTS: Starting audio playback with precise timing');
      } else {
        // Sequential continuation - start exactly when previous chunk ends
        startTime = nextPlayTimeRef.current;
        console.log(`StreamingTTS: Sequential playback at ${startTime.toFixed(6)}s`);
      }

      // ANTI-CLICK FIX: Apply very short fade-in to prevent clicks (2ms)
      const fadeTime = 0.002; // 2ms fade
      sourceGain.gain.setValueAtTime(0, startTime);
      sourceGain.gain.linearRampToValueAtTime(1, startTime + fadeTime);

      // ANTI-CLICK FIX: Apply fade-out at the end to prevent clicks
      const endTime = startTime + audioBuffer.duration;
      if (endTime > startTime + fadeTime) { // Only apply fade-out if buffer is long enough
        sourceGain.gain.setValueAtTime(1, endTime - fadeTime);
        sourceGain.gain.linearRampToValueAtTime(0, endTime);
      }

      source.start(startTime);

      // CRITICAL: Update next play time with sample-accurate precision
      nextPlayTimeRef.current = startTime + audioBuffer.duration;

      console.log(`StreamingTTS: Playing audio buffer, duration: ${audioBuffer.duration.toFixed(3)}s starting at ${startTime.toFixed(6)}s, next: ${nextPlayTimeRef.current.toFixed(6)}s`);

      // Store reference for cleanup
      currentAudioSourceRef.current = source;

      // CRITICAL FIX: Don't wait for audio to finish - return immediately for faster processing
      source.onended = () => {
        // Cleanup after playback
        if (currentAudioSourceRef.current === source) {
          currentAudioSourceRef.current = null;
        }
      };
    } catch (error) {
      console.error('StreamingTTS: Error playing audio buffer:', error);
      throw error;
    }
  }, [initializeAudioContext]);

  // FIXED: Audio buffer concatenation for seamless playback
  const concatenateAudioBuffers = useCallback((buffers: AudioBuffer[]): AudioBuffer | null => {
    if (buffers.length === 0) return null;
    if (buffers.length === 1) return buffers[0];

    try {
      const sampleRate = buffers[0].sampleRate;
      const numberOfChannels = buffers[0].numberOfChannels;

      // Calculate total length
      const totalLength = buffers.reduce((sum, buffer) => sum + buffer.length, 0);

      // Create concatenated buffer
      const concatenatedBuffer = audioContextRef.current!.createBuffer(
        numberOfChannels,
        totalLength,
        sampleRate
      );

      // ANTI-CLICK FIX: Copy data from all buffers with crossfade between chunks
      let offset = 0;
      const crossfadeLength = Math.min(24, buffers[0].length / 10); // 1ms crossfade or 10% of chunk

      for (let bufferIndex = 0; bufferIndex < buffers.length; bufferIndex++) {
        const buffer = buffers[bufferIndex];
        const isLastBuffer = bufferIndex === buffers.length - 1;

        for (let channel = 0; channel < numberOfChannels; channel++) {
          const channelData = concatenatedBuffer.getChannelData(channel);
          const sourceData = buffer.getChannelData(channel);

          // Copy main data
          channelData.set(sourceData, offset);

          // ANTI-CLICK FIX: Apply crossfade at buffer boundaries (except first buffer start and last buffer end)
          if (bufferIndex > 0 && crossfadeLength > 0) {
            // Crossfade with previous buffer
            for (let i = 0; i < crossfadeLength && i < buffer.length; i++) {
              const fadeIn = i / crossfadeLength;
              const fadeOut = 1 - fadeIn;
              const currentSample = sourceData[i];
              const prevSample = channelData[offset + i]; // This contains the overlapped previous data
              channelData[offset + i] = prevSample * fadeOut + currentSample * fadeIn;
            }
          }
        }
        offset += buffer.length;
      }

      console.log(`StreamingTTS: Concatenated ${buffers.length} chunks into ${(concatenatedBuffer.duration * 1000).toFixed(0)}ms buffer`);
      return concatenatedBuffer;
    } catch (error) {
      console.error('StreamingTTS: Error concatenating audio buffers:', error);
      return null;
    }
  }, []);

  // FIXED: Batch processing with concatenation for smooth playback
  const processAudioBuffer = useCallback(async () => {
    // CRITICAL FIX: Use isProcessingRef to prevent parallel processing
    if (isProcessingRef.current) {
      console.log('StreamingTTS: Processing already in progress, skipping duplicate call');
      return;
    }

    // CRITICAL FIX: Immediate processing for minimal latency
    const MIN_BUFFER_SIZE = 1; // Process immediately on first chunk
    const BATCH_SIZE = 3; // Smaller batches for faster processing

    // RELIABILITY: Check for final chunk in multiple ways
    const hasLastChunk = audioBufferRef.current.some(chunk =>
      (totalChunksRef.current > 0 && chunk.chunkNumber === totalChunksRef.current) ||
      (chunk.chunkNumber > 0 && totalChunksRef.current === 0) // Handle case where totalChunks not set yet
    );

    // CRITICAL FIX: Process immediately if we have any chunks or final chunk
    if (audioBufferRef.current.length < MIN_BUFFER_SIZE && !hasLastChunk) {
      console.log(`StreamingTTS: Waiting for chunks (have ${audioBufferRef.current.length})`);
      return;
    }

    isProcessingRef.current = true;
    console.log(`StreamingTTS: Starting audio buffer processing (buffer size: ${audioBufferRef.current.length})`);

    try {
      // FIXED: Process chunks in batches sequentially (wait for each batch to complete)
      while (audioBufferRef.current.length > 0) {
        const batchSize = Math.min(BATCH_SIZE, audioBufferRef.current.length);
        const chunksToProcess = audioBufferRef.current.splice(0, batchSize);

        console.log(`StreamingTTS: Processing batch of ${chunksToProcess.length} chunks`);

        // CRITICAL FIX: Process chunks individually for immediate playback
        for (const chunk of chunksToProcess) {
          console.log(`StreamingTTS: Processing chunk #${chunk.chunkNumber}, size: ${chunk.data.byteLength} bytes`);
          const audioBuffer = await createAudioBuffer(chunk.data);

          if (audioBuffer) {
            // Start playback callback on first chunk only
            if (!isPlayingRef.current) {
              isPlayingRef.current = true;
              console.log('StreamingTTS: Starting audio playback');
              onStart?.();
            }

            // Check if this is the last chunk
            const isLastChunk = totalChunksRef.current > 0 && chunk.chunkNumber === totalChunksRef.current;

            // CRITICAL FIX: Play each chunk immediately without waiting
            try {
              // Don't await - start playback immediately and continue processing
              playAudioBuffer(audioBuffer, isLastChunk);
              console.log(`StreamingTTS: Started playback of chunk #${chunk.chunkNumber}${isLastChunk ? ' (FINAL CHUNK)' : ''}`);
            } catch (error) {
              console.error(`StreamingTTS: Playback error for chunk #${chunk.chunkNumber}:`, error);
            }
          }
        }
      }

      console.log('StreamingTTS: All available chunks processed');
    } catch (error) {
      console.error('StreamingTTS: Error processing audio buffer:', error);
      onError?.(`Audio processing error: ${error}`);
    } finally {
      isProcessingRef.current = false;
    }
  }, [createAudioBuffer, onError, onStart, playAudioBuffer]);

  // REMOVED: Direct playback instead of queue system

  // CHUNK ORDERING: Process chunks sequentially to maintain correct audio order
  const addAudioChunk = useCallback((data: ArrayBuffer, chunkNumber: number, totalChunks?: number, isLastChunk?: boolean) => {
    const chunk: AudioChunk = {
      data: data,
      timestamp: Date.now(),
      chunkNumber: chunkNumber
    };

    // Store total chunks if provided
    if (totalChunks) {
      totalChunksRef.current = totalChunks;
    }

    // CRITICAL: If this is marked as the last chunk, ensure we know the total
    if (isLastChunk && !totalChunksRef.current) {
      totalChunksRef.current = chunkNumber;
      console.log(`StreamingTTS: Final chunk #${chunkNumber} received, setting totalChunks to ${chunkNumber}`);
    }

    // Store chunk in ordered buffer
    chunkBufferRef.current.set(chunkNumber, chunk);
    console.log(`StreamingTTS: Buffered chunk #${chunkNumber}${isLastChunk ? ' (FINAL CHUNK)' : ''}, buffer has ${chunkBufferRef.current.size} chunks`);

    // Call onAudioReady when first audio chunk is received
    if (!audioReadyCalledRef.current && onAudioReady) {
      console.log('StreamingTTS: First audio chunk received, calling onAudioReady');
      audioReadyCalledRef.current = true;
      onAudioReady(messageId);
    }

    // Process chunks in order
    processOrderedChunks();
  }, [onAudioReady, messageId]);

  // PRODUCTION: Reliable chunk processing without sequential requirement
  const processOrderedChunks = useCallback(() => {
    let chunksAdded = false;
    const availableChunks: AudioChunk[] = [];

    // RELIABILITY FIX: Process ALL available chunks, not just sequential ones
    // This prevents final chunk from getting stuck due to gaps
    for (const [chunkNumber, chunk] of chunkBufferRef.current.entries()) {
      availableChunks.push(chunk);
      chunkBufferRef.current.delete(chunkNumber);
      chunksAdded = true;
    }

    // Sort chunks by number to maintain order
    availableChunks.sort((a, b) => a.chunkNumber - b.chunkNumber);

    // Add all available chunks to processing buffer
    for (const chunk of availableChunks) {
      audioBufferRef.current.push(chunk);
      console.log(`StreamingTTS: Processing chunk #${chunk.chunkNumber} (gap-tolerant processing)`);
    }

    // RELIABILITY: Check for final chunk in a more robust way
    if (chunksAdded) {
      const hasLastChunk = audioBufferRef.current.some(chunk =>
        (totalChunksRef.current > 0 && chunk.chunkNumber === totalChunksRef.current) ||
        (chunk.chunkNumber > 0 && totalChunksRef.current === 0) // Handle case where totalChunks not set yet
      );

      const hasAnyChunks = audioBufferRef.current.length > 0; // Process any chunks immediately
      const hasLargeBuffer = audioBufferRef.current.length >= 5; // Process if buffer gets large

      // CRITICAL FIX: Process immediately when any chunks are available
      if (hasLastChunk || hasAnyChunks || hasLargeBuffer) {
        console.log(`StreamingTTS: Triggering immediate processing (hasLastChunk: ${hasLastChunk}, hasAny: ${hasAnyChunks}, hasLarge: ${hasLargeBuffer}, bufferSize: ${audioBufferRef.current.length})`);
        processAudioBuffer();
      } else {
        console.log(`StreamingTTS: No chunks to process (buffer size: ${audioBufferRef.current.length})`);
      }
    }

    // CRITICAL FIX: Reduced timeout for faster final processing
    if (availableChunks.length > 0) {
      setTimeout(() => {
        if (audioBufferRef.current.length > 0 && !isProcessingRef.current) {
          console.log('StreamingTTS: Timeout-based final processing triggered');
          processAudioBuffer();
        }
      }, 500); // Reduced to 500ms for faster processing
    }
  }, [processAudioBuffer]);

  // Fallback to optimized streaming endpoint if WebSocket fails
  const startStreamingFallback = useCallback(async () => {
    try {
      console.log('StreamingTTS: Using fallback optimized streaming endpoint...');

      const response = await fetch('/api/optimized-streaming-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          voiceModel: voiceModel,
          format: 'linear16',
          sampleRate: 24000,
          enableNaturalSpeech: enableNaturalSpeech,
          streamResponse: true
        })
      });

      if (!response.ok) {
        throw new Error(`Fallback endpoint failed: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body from fallback');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      connectionRef.current = { reader, response };

      console.log('StreamingTTS: Fallback streaming connection established');
      setIsConnecting(false);
      setIsStreaming(true);
      onStart?.();

      // Process the fallback stream (same logic as main endpoint)
      const processStream = async () => {
        let buffer = '';
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log('StreamingTTS: Fallback stream completed');
              setIsStreaming(false);
              connectionRef.current = null;
              onEnd?.();
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // Process complete lines from buffer (same as main endpoint)
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonData = line.slice(6).trim();
                  if (jsonData) {
                    const data = JSON.parse(jsonData);

                    if (data.type === 'audio' && data.encoding === 'linear16') {
                      const binaryString = atob(data.data);
                      const bytes = new Uint8Array(binaryString.length);
                      for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                      }

                      // PRODUCTION: Update performance metrics
                      setPerformanceMetrics(prev => ({
                        ...prev,
                        chunksReceived: data.chunkNumber,
                        averageChunkSize: data.averageChunkSize || prev.averageChunkSize,
                        firstChunkLatency: data.firstChunkLatency || prev.firstChunkLatency
                      }));

                      addAudioChunk(bytes.buffer, data.chunkNumber, data.totalChunks, data.isLastChunk);
                    } else if (data.type === 'health_status') {
                      // PRODUCTION: Monitor health status from server
                      setHealthStatus(data.status);
                      console.log(`StreamingTTS: Health status: ${data.status}`, data);

                      if (data.status === 'error' || data.status === 'stalled') {
                        setPerformanceMetrics(prev => ({ ...prev, errorCount: prev.errorCount + 1 }));
                      }
                    }
                  }
                } catch (parseError) {
                  console.error('StreamingTTS: Fallback parse error:', parseError);
                }
              }
            }
          }
        } catch (error) {
          console.error('StreamingTTS: Fallback stream error:', error);
          setError(error instanceof Error ? error.message : 'Fallback stream failed');
          setIsStreaming(false);
          connectionRef.current = null;
          onError?.(error instanceof Error ? error.message : 'Fallback stream failed');
        }
      };

      processStream();

    } catch (error) {
      console.error('StreamingTTS: Fallback failed:', error);
      setError(error instanceof Error ? error.message : 'Fallback failed');
      setIsConnecting(false);
      setIsStreaming(false);
      onError?.(error instanceof Error ? error.message : 'Fallback failed');
    }
  }, [text, voiceModel, enableNaturalSpeech, onStart, onEnd, onError, addAudioChunk]);

  // Establish Server-Sent Events connection to proxy
  const connectToDeepgram = useCallback(async () => {
    if (!text || isConnecting || connectionRef.current) {
      return;
    }

    try {
      setIsConnecting(true);
      setError(null);

      console.log('StreamingTTS: Connecting to streaming TTS proxy...');

      // PRODUCTION: Initialize Web Audio API first
      await initializeAudioContext();

      // ULTRA-FAST: Use WebSocket streaming endpoint for immediate audio start
      const endpoint = '/api/websocket-streaming-tts';
      console.log(`StreamingTTS: Making request to ${endpoint} (Natural Speech: ${enableNaturalSpeech ? 'ENABLED (parallel)' : 'DISABLED'})`);
      console.log('StreamingTTS: Request payload:', { text: text.substring(0, 50) + '...', voiceModel, enableNaturalSpeech });

      // OPTIMIZATION: Pre-initialize AudioContext for faster first chunk processing
      await initializeAudioContext();

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          voiceModel: voiceModel,
          format: 'linear16', // CRITICAL: Use LINEAR16 PCM for seamless concatenation
          sampleRate: 24000,  // Optimal for speech
          enableNaturalSpeech: enableNaturalSpeech
        })
      });

      console.log('StreamingTTS: Response received:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('StreamingTTS: WebSocket endpoint error:', errorText);

        // FALLBACK: Try optimized streaming endpoint if WebSocket fails
        console.log('StreamingTTS: Falling back to optimized streaming endpoint...');
        return startStreamingFallback();
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      connectionRef.current = { reader, response };

      console.log('StreamingTTS: Streaming connection established');
      setIsConnecting(false);
      setIsStreaming(true);
      onStart?.();

      // Process streaming data with proper buffering for large JSON payloads
      const processStream = async () => {
        let buffer = ''; // Buffer for incomplete SSE data

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              console.log('StreamingTTS: Stream completed');
              setIsStreaming(false);
              connectionRef.current = null;
              onEnd?.();
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // Process complete lines from buffer
            const lines = buffer.split('\n');
            // Keep the last line in buffer if it doesn't end with \n (incomplete)
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonData = line.slice(6).trim();
                  if (jsonData) {
                    const data = JSON.parse(jsonData);

                    switch (data.type) {
                      case 'status':
                        console.log('StreamingTTS: Status:', data.message);
                        break;

                      case 'audio':
                        console.log('StreamingTTS: Received LINEAR16 PCM chunk', {
                          encoding: data.encoding,
                          chunkNumber: data.chunkNumber,
                          isLastChunk: data.isLastChunk,
                          sampleRate: data.sampleRate,
                          chunkSize: data.chunkSize,
                          totalChunks: data.totalChunks
                        });

                        // CRITICAL FIX: Ultra-fast binary data handling for LINEAR16 PCM
                        if (data.encoding === 'linear16') {
                          try {
                            // PERFORMANCE: Direct base64 to ArrayBuffer conversion
                            const binaryString = atob(data.data);
                            const bytes = new Uint8Array(binaryString.length);

                            // OPTIMIZED: Unrolled loop for faster conversion
                            let i = 0;
                            const len = binaryString.length;
                            while (i < len) {
                              bytes[i] = binaryString.charCodeAt(i);
                              i++;
                            }

                            // CRITICAL: Process chunk immediately
                            addAudioChunk(bytes.buffer, data.chunkNumber, data.totalChunks, data.isLastChunk);
                          } catch (decodeError) {
                            console.error('StreamingTTS: Base64 decode error:', decodeError);
                            onError?.(`Audio decode error: ${decodeError}`);
                          }
                        }
                        break;

                      case 'audio_complete':
                        console.log('StreamingTTS: Audio complete marker received');

                        // ENHANCED: Handle final audio completion marker
                        if (data.totalChunks && !totalChunksRef.current) {
                          totalChunksRef.current = data.totalChunks;
                          console.log(`StreamingTTS: Setting totalChunks from audio_complete: ${data.totalChunks}`);
                        }

                        // Force process any remaining chunks
                        if (audioBufferRef.current.length > 0) {
                          console.log(`StreamingTTS: Processing ${audioBufferRef.current.length} remaining chunks after audio_complete`);
                          processAudioBuffer();
                        }
                        break;

                      case 'close':
                        console.log('StreamingTTS: Stream completed');

                        // CRITICAL FIX: Process any remaining chunks in buffer before closing
                        if (audioBufferRef.current.length > 0) {
                          console.log(`StreamingTTS: Processing ${audioBufferRef.current.length} remaining chunks before closing`);
                          // Force process remaining chunks as final batch
                          processAudioBuffer();
                        }

                        // CRITICAL FIX: Call onAudioReady if stream completes without any chunks
                        // This prevents backup timeout from triggering when no audio is generated
                        if (!audioReadyCalledRef.current && onAudioReady) {
                          console.log('StreamingTTS: Stream completed without audio chunks, calling onAudioReady to prevent backup timeout');
                          audioReadyCalledRef.current = true;
                          onAudioReady(messageId);
                        }

                        // ENHANCED: Also check if there are chunks waiting in the ordered buffer
                        if (chunkBufferRef.current.size > 0) {
                          console.log(`StreamingTTS: Processing ${chunkBufferRef.current.size} remaining ordered chunks before closing`);
                          // Force process any remaining ordered chunks
                          processOrderedChunks();

                          // Process the final buffer if anything was added
                          if (audioBufferRef.current.length > 0) {
                            console.log(`StreamingTTS: Processing final buffer after ordered chunks`);
                            processAudioBuffer();
                          }
                        }

                        setIsStreaming(false);
                        connectionRef.current = null;
                        onEnd?.();
                        return;

                      case 'error':
                        console.error('StreamingTTS: Stream error:', data.message);
                        setError(data.message);
                        setIsConnecting(false);
                        setIsStreaming(false);
                        connectionRef.current = null;

                        // CRITICAL FIX: Call onAudioReady on error to prevent backup timeout
                        if (!audioReadyCalledRef.current && onAudioReady) {
                          console.log('StreamingTTS: Stream error occurred, calling onAudioReady to prevent backup timeout');
                          audioReadyCalledRef.current = true;
                          onAudioReady(messageId);
                        }

                        onError?.(data.message);
                        return;
                    }
                  }
                } catch (parseError) {
                  console.error('StreamingTTS: Error parsing SSE data:', parseError);
                  console.error('StreamingTTS: Problematic line:', line.slice(0, 200) + '...');
                }
              }
            }
          }
        } catch (streamError) {
          console.error('StreamingTTS: Stream processing error:', streamError);
          setError('Stream processing error');
          setIsConnecting(false);
          setIsStreaming(false);
          connectionRef.current = null;

          // CRITICAL FIX: Call onAudioReady on stream error to prevent backup timeout
          if (!audioReadyCalledRef.current && onAudioReady) {
            console.log('StreamingTTS: Stream processing error occurred, calling onAudioReady to prevent backup timeout');
            audioReadyCalledRef.current = true;
            onAudioReady(messageId);
          }

          onError?.('Stream processing error');
        }
      };

      processStream();

    } catch (error: any) {
      console.error('StreamingTTS: Connection error:', error);
      setError(error.message);
      setIsConnecting(false);

      // CRITICAL FIX: Call onAudioReady on connection error to prevent backup timeout
      if (!audioReadyCalledRef.current && onAudioReady) {
        console.log('StreamingTTS: Connection error occurred, calling onAudioReady to prevent backup timeout');
        audioReadyCalledRef.current = true;
        onAudioReady(messageId);
      }

      onError?.(error.message);
    }
  }, [text, voiceModel, messageId, addAudioChunk, onStart, onEnd, onError, initializeAudioContext, startStreamingFallback]);

  // Start streaming when text changes (but only once per unique text) AND autoPlay is enabled
  useEffect(() => {
    // Only process if text is different from last processed text AND autoPlay is enabled
    if (text && text.trim() && text !== lastProcessedTextRef.current && autoPlay) {
      console.log('StreamingTTS: New text received with autoPlay enabled, clearing previous audio buffer');

      // CRITICAL FIX: Clean up any existing connection before starting new one
      if (connectionRef.current) {
        console.log('StreamingTTS: Cleaning up previous connection before starting new one');
        if (connectionRef.current.reader) {
          connectionRef.current.reader.cancel().catch(() => {});
        }
        connectionRef.current = null;
      }

      // Update last processed text
      lastProcessedTextRef.current = text;

      // Clear previous audio buffer when new text comes in
      audioBufferRef.current = [];
      bufferQueueRef.current = []; // Also clear audio buffer queue
      isPlayingRef.current = false;
      audioReadyCalledRef.current = false; // Reset audio ready flag for new text
      nextPlayTimeRef.current = 0; // Reset timing for new text

      // CHUNK ORDERING: Reset chunk ordering state
      chunkBufferRef.current.clear();
      nextExpectedChunkRef.current = 1;
      totalChunksRef.current = 0;

      // CRITICAL FIX: Add small delay to ensure previous connection cleanup is complete
      setTimeout(() => {
        connectToDeepgram();
      }, 100); // 100ms delay to prevent race conditions between consecutive requests
    } else if (text && text.trim() && text !== lastProcessedTextRef.current && !autoPlay) {
      console.log('StreamingTTS: New text received but autoPlay disabled, not starting streaming');
      // Update last processed text but don't start streaming
      lastProcessedTextRef.current = text;

      // Call onAudioReady immediately since we're not processing audio
      if (onAudioReady && !audioReadyCalledRef.current) {
        console.log('StreamingTTS: Calling onAudioReady immediately (autoPlay disabled)');
        audioReadyCalledRef.current = true;
        onAudioReady(messageId);
      }
    }
  }, [text, autoPlay, onAudioReady, messageId]); // Added autoPlay dependency

  // FIXED: Buffer health monitoring without dropping chunks
  useEffect(() => {
    const monitorInterval = setInterval(() => {
      const bufferSize = audioBufferRef.current.length;

      // Monitor buffer health for performance insights only
      if (bufferSize > 10) {
        console.warn(`StreamingTTS: Buffer health info - ${bufferSize} chunks pending (processing may be slower than arrival)`);
      }

      // Log buffer status for debugging (no emergency cleanup that drops audio)
      if (bufferSize > 50) {
        console.info(`StreamingTTS: Large buffer detected - ${bufferSize} chunks. This is normal for long responses.`);
      }
    }, 2000); // Check every 2 seconds for monitoring

    return () => clearInterval(monitorInterval);
  }, []);

  // Cleanup on unmount only
  useEffect(() => {
    return () => {
      // Clean up streaming connection
      if (connectionRef.current) {
        console.log('StreamingTTS: Cleaning up streaming connection on unmount');
        if (connectionRef.current.reader) {
          connectionRef.current.reader.cancel();
        }
        connectionRef.current = null;
      }

      // Clean up any remaining audio buffer
      console.log('StreamingTTS: Clearing audio buffer on unmount');
      audioBufferRef.current = [];
      isPlayingRef.current = false;
      audioReadyCalledRef.current = false;
    };
  }, []);

  // Manual play function for non-autoplay scenarios
  const play = useCallback(() => {
    processAudioBuffer();
  }, [processAudioBuffer]);

  return (
    <div className="streaming-tts-container">
      {isConnecting && (
        <div className="text-sm text-gray-500">
          Connecting to streaming TTS...
        </div>
      )}
      
      {isStreaming && (
        <div className="text-sm text-green-600">
          Streaming audio...
        </div>
      )}
      
      {error && (
        <div className="text-sm text-red-500">
          Streaming error: {error}
        </div>
      )}
      
      {!autoPlay && audioBufferRef.current.length > 0 && (
        <button 
          onClick={play}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Play Streamed Audio
        </button>
      )}
    </div>
  );
}
