import { createBrowserClient } from '@supabase/ssr'

export const createClient = () => {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'x-supabase-api-version': '2024-01-01',
          'Accept-Profile': 'public',
          'Content-Profile': 'public'
        },
      },
    }
  )

  // Add realtime error logging
  supabase.getChannels().forEach(channel => {
    channel.on('system', { event: 'error' }, (payload) => {
      console.error('Realtime error:', payload)
    })
  })

  return supabase
}