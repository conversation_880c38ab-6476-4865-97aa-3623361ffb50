'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { toast } from 'sonner'
import { createClient } from '@/lib/supabase/client'
import { UserTier, getTierDisplayName, getTierPrice, TIER_CONFIGS } from '@/lib/tiers'
import { User } from '@supabase/supabase-js'

interface UserProfile {
  id: string
  tier: UserTier
  subscription_status: string
  subscription_start_date: string
  subscription_end_date?: string
  created_at: string
  updated_at: string
  email?: string
  chat_interface_count?: number
}

export default function TierManagementPage() {
  const [users, setUsers] = useState<UserProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [searchEmail, setSearchEmail] = useState('')
  const [selectedTier, setSelectedTier] = useState<UserTier | 'all'>('all')
  const [currentUser, setCurrentUser] = useState<User | null>(null)

  const supabase = createClient()

  useEffect(() => {
    fetchCurrentUser()
    fetchUsers()
  }, [])

  const fetchCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
  }

  const fetchUsers = async () => {
    try {
      setLoading(true)

      // Fetch user profiles with tier information
      const { data: profiles, error: profilesError } = await supabase
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (profilesError) {
        console.error('Error fetching user profiles:', profilesError)
        toast.error('Failed to fetch user profiles')
        return
      }

      // Fetch user emails from auth.users (requires service role)
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()

      if (authError) {
        console.error('Error fetching auth users:', authError)
        // Continue without email data if auth admin access is not available
      }

      // Fetch chat interface counts for each user
      const userIds = profiles.map(p => p.id)
      const { data: chatCounts, error: chatError } = await supabase
        .from('chat_interfaces')
        .select('user_id')
        .in('user_id', userIds)

      if (chatError) {
        console.error('Error fetching chat interface counts:', chatError)
      }

      // Combine data
      const usersWithData = profiles.map(profile => {
        const authUser = authUsers?.users.find(u => u.id === profile.id)
        const chatCount = chatCounts?.filter(c => c.user_id === profile.id).length || 0

        return {
          ...profile,
          email: authUser?.email || 'Unknown',
          chat_interface_count: chatCount
        }
      })

      setUsers(usersWithData)
    } catch (error) {
      console.error('Error in fetchUsers:', error)
      toast.error('Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  const updateUserTier = async (userId: string, newTier: UserTier) => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ 
          tier: newTier,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) {
        console.error('Error updating user tier:', error)
        toast.error('Failed to update user tier')
        return
      }

      toast.success(`User tier updated to ${getTierDisplayName(newTier)}`)
      fetchUsers() // Refresh the list
    } catch (error) {
      console.error('Error in updateUserTier:', error)
      toast.error('Failed to update user tier')
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesEmail = searchEmail === '' || user.email?.toLowerCase().includes(searchEmail.toLowerCase())
    const matchesTier = selectedTier === 'all' || user.tier === selectedTier
    return matchesEmail && matchesTier
  })

  const getTierBadgeColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-500'
      case 'standard': return 'bg-blue-500'
      case 'pro': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'cancelled': return 'bg-yellow-500'
      case 'expired': return 'bg-red-500'
      case 'trial': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
            <p className="mt-4">Loading tier management dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🎯 Tier Management Dashboard
          </h1>
          <p className="text-xl text-gray-300">
            Manage user subscription tiers and monitor usage
          </p>
        </div>

        {/* Tier Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {(['free', 'standard', 'pro'] as UserTier[]).map(tier => {
            const tierUsers = users.filter(u => u.tier === tier)
            const tierConfig = TIER_CONFIGS[tier]
            const pricing = getTierPrice(tier)

            return (
              <Card key={tier} className="bg-white/10 border-gray-600 p-6">
                <div className="text-center">
                  <Badge className={`${getTierBadgeColor(tier)} text-white mb-4`}>
                    {getTierDisplayName(tier)}
                  </Badge>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {tierUsers.length} Users
                  </h3>
                  <p className="text-gray-300 mb-4">
                    ${pricing.monthly}/month
                  </p>
                  <div className="text-sm text-gray-400 space-y-1">
                    <p>Max Interfaces: {tierConfig.maxChatInterfaces === -1 ? '∞' : tierConfig.maxChatInterfaces}</p>
                    <p>Voice: {tierConfig.voiceFeaturesEnabled ? '✅' : '❌'}</p>
                    <p>Branding: {tierConfig.brandingRemoval ? '✅' : '❌'}</p>
                  </div>
                </div>
              </Card>
            )
          })}
        </div>

        {/* Filters */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label className="text-white mb-2 block">Search by Email</Label>
              <Input
                placeholder="Enter email address..."
                value={searchEmail}
                onChange={(e) => setSearchEmail(e.target.value)}
                className="bg-white/20 border-gray-600 text-white"
              />
            </div>
            <div>
              <Label className="text-white mb-2 block">Filter by Tier</Label>
              <Select value={selectedTier} onValueChange={(value) => setSelectedTier(value as UserTier | 'all')}>
                <SelectTrigger className="bg-white/20 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="pro">Pro</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchUsers} className="w-full">
                🔄 Refresh
              </Button>
            </div>
          </div>
        </Card>

        {/* Feature Comparison Matrix */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-2xl font-bold text-white mb-4">
            📊 Feature Comparison Matrix
          </h2>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-600">
                  <TableHead className="text-gray-300">Feature</TableHead>
                  <TableHead className="text-center text-gray-300">Free</TableHead>
                  <TableHead className="text-center text-gray-300">Standard</TableHead>
                  <TableHead className="text-center text-gray-300">Pro</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Chat Interfaces</TableCell>
                  <TableCell className="text-center text-gray-300">1</TableCell>
                  <TableCell className="text-center text-green-400">Unlimited</TableCell>
                  <TableCell className="text-center text-green-400">Unlimited</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Voice Features</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-yellow-400">Basic (3 voices)</TableCell>
                  <TableCell className="text-center text-green-400">Premium (6 voices)</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Natural Speech</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Branding Removal</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-yellow-400">Optional</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">White Labeling</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Custom Branding</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Advanced Customization</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Analytics Dashboard</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
                <TableRow className="border-gray-600">
                  <TableCell className="text-white font-medium">Priority Support</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-red-400">❌</TableCell>
                  <TableCell className="text-center text-green-400">✅</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </Card>

        {/* Users Table */}
        <Card className="bg-white/10 border-gray-600">
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">
              User Management ({filteredUsers.length} users)
            </h2>
            
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-600">
                    <TableHead className="text-gray-300">Email</TableHead>
                    <TableHead className="text-gray-300">Current Tier</TableHead>
                    <TableHead className="text-gray-300">Status</TableHead>
                    <TableHead className="text-gray-300">Chat Interfaces</TableHead>
                    <TableHead className="text-gray-300">Member Since</TableHead>
                    <TableHead className="text-gray-300">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map(user => (
                    <TableRow key={user.id} className="border-gray-600">
                      <TableCell className="text-white">
                        {user.email}
                        {user.id === currentUser?.id && (
                          <Badge className="ml-2 bg-yellow-500 text-black">You</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getTierBadgeColor(user.tier)} text-white`}>
                          {getTierDisplayName(user.tier)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusBadgeColor(user.subscription_status)} text-white`}>
                          {user.subscription_status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-white">
                        {user.chat_interface_count}
                      </TableCell>
                      <TableCell className="text-gray-300">
                        {new Date(user.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Select
                          value={user.tier}
                          onValueChange={(newTier) => updateUserTier(user.id, newTier as UserTier)}
                          disabled={user.id === currentUser?.id}
                        >
                          <SelectTrigger className="w-32 bg-white/20 border-gray-600 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="free">Free</SelectItem>
                            <SelectItem value="standard">Standard</SelectItem>
                            <SelectItem value="pro">Pro</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-400">No users found matching the current filters.</p>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  )
}
