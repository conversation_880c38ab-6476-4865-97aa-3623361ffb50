<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Latest Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Latest Embed Test</h1>

    <div class="note">
        <strong>Important:</strong> This page tests the standard script embed code.
        Please open your browser's developer console (F12 or right-click > Inspect > Console)
        to check for any errors during the script loading process.
    </div>

    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget and check for any errors in the console.</p>

        <!-- BotFusion Chat Widget -->
        <script id="botfusion-chat-widget"
            data-chat-id="1"
            data-primary-color="#3b82f6"
            data-origin="https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app"
            src="https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app/api/embed-script">
        </script>
    </div>

    <!-- Error Tracking -->
    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });

        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all scripts and resources');

            // Log all iframes on the page
            const iframes = document.querySelectorAll('iframe');
            console.log(`Found ${iframes.length} iframes on the page:`);
            iframes.forEach((iframe, index) => {
                console.log(`Iframe #${index + 1}:`, {
                    id: iframe.id,
                    src: iframe.src,
                    width: iframe.style.width,
                    height: iframe.style.height
                });
            });
        });
    </script>
</body>
</html>
