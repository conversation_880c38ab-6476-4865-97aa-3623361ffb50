-- BotFusion X Database Setup
-- Run this SQL in your Supabase SQL Editor to set up the tier system

-- 1. Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  tier TEXT NOT NULL DEFAULT 'free' CHECK (tier IN ('free', 'standard', 'pro')),
  subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial')),
  subscription_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Enable RLS for user_profiles
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 3. Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.user_profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON public.user_profiles;

-- 4. Create RLS policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable insert for authenticated users" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow service role to manage all profiles (for admin operations)
CREATE POLICY "Service role can manage all profiles" ON public.user_profiles
  FOR ALL USING (auth.role() = 'service_role');

-- 5. Create function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, tier, subscription_status)
  VALUES (NEW.id, 'free', 'active')
  ON CONFLICT (id) DO NOTHING;
  
  RETURN NEW;
END;
$$;

-- 6. Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7. Create profiles for existing users (you may need to adjust the email)
-- Replace '<EMAIL>' with your actual admin email
DO $$
DECLARE
  user_record RECORD;
  admin_email TEXT := '<EMAIL>';
BEGIN
  FOR user_record IN 
    SELECT id, email FROM auth.users
  LOOP
    INSERT INTO public.user_profiles (
      id, 
      tier, 
      subscription_status,
      subscription_start_date,
      subscription_end_date,
      created_at,
      updated_at
    ) VALUES (
      user_record.id,
      CASE 
        WHEN user_record.email = admin_email THEN 'pro'
        ELSE 'free'
      END,
      'active',
      NOW(),
      CASE 
        WHEN user_record.email = admin_email THEN NOW() + INTERVAL '1 year'
        ELSE NULL
      END,
      NOW(),
      NOW()
    ) ON CONFLICT (id) DO UPDATE SET
      tier = EXCLUDED.tier,
      subscription_status = EXCLUDED.subscription_status,
      subscription_start_date = EXCLUDED.subscription_start_date,
      subscription_end_date = EXCLUDED.subscription_end_date,
      updated_at = NOW();
  END LOOP;
END $$;

-- 8. Verify the setup
SELECT 
  'Setup verification' as status,
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN tier = 'free' THEN 1 END) as free_users,
  COUNT(CASE WHEN tier = 'standard' THEN 1 END) as standard_users,
  COUNT(CASE WHEN tier = 'pro' THEN 1 END) as pro_users
FROM public.user_profiles;

-- 9. Show admin user status
SELECT 
  'Admin user status' as info,
  email,
  tier,
  subscription_status
FROM auth.users u
JOIN public.user_profiles p ON u.id = p.id
WHERE u.email = '<EMAIL>';
