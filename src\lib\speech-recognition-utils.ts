/**
 * Utility functions for speech recognition error handling and recovery
 */

// Define error types for speech recognition
export enum SpeechRecognitionErrorType {
  // Standard Web Speech API error types
  ABORTED = 'aborted',
  AUDIO_CAPTURE = 'audio-capture',
  BAD_GRAMMAR = 'bad-grammar',
  LANGUAGE_NOT_SUPPORTED = 'language-not-supported',
  NETWORK = 'network',
  NO_SPEECH = 'no-speech',
  NOT_ALLOWED = 'not-allowed',
  SERVICE_NOT_ALLOWED = 'service-not-allowed',
  
  // Custom error types
  INITIALIZATION_FAILED = 'initialization-failed',
  BROWSER_NOT_SUPPORTED = 'browser-not-supported',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

// Define error categories for grouping similar errors
export enum SpeechRecognitionErrorCategory {
  PERMISSION = 'permission',
  HARDWARE = 'hardware',
  NETWORK = 'network',
  USER = 'user',
  BROWSER = 'browser',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

// Interface for speech recognition errors
export interface SpeechRecognitionErrorInfo {
  type: SpeechRecognitionErrorType;
  category: SpeechRecognitionErrorCategory;
  message: string;
  recoverable: boolean;
  recoveryStrategy: SpeechRecognitionRecoveryStrategy;
}

// Define recovery strategies
export enum SpeechRecognitionRecoveryStrategy {
  RETRY = 'retry',
  RESTART = 'restart',
  REQUEST_PERMISSION = 'request-permission',
  SWITCH_BROWSER = 'switch-browser',
  MANUAL_INTERVENTION = 'manual-intervention',
  NONE = 'none'
}

// Map error types to error info
export const speechRecognitionErrorMap: Record<SpeechRecognitionErrorType, SpeechRecognitionErrorInfo> = {
  [SpeechRecognitionErrorType.ABORTED]: {
    type: SpeechRecognitionErrorType.ABORTED,
    category: SpeechRecognitionErrorCategory.USER,
    message: 'Speech recognition was aborted.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RETRY
  },
  [SpeechRecognitionErrorType.AUDIO_CAPTURE]: {
    type: SpeechRecognitionErrorType.AUDIO_CAPTURE,
    category: SpeechRecognitionErrorCategory.HARDWARE,
    message: 'No microphone was found or the microphone is not accessible.',
    recoverable: false,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.MANUAL_INTERVENTION
  },
  [SpeechRecognitionErrorType.BAD_GRAMMAR]: {
    type: SpeechRecognitionErrorType.BAD_GRAMMAR,
    category: SpeechRecognitionErrorCategory.SYSTEM,
    message: 'There was an error in the speech grammar.',
    recoverable: false,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.NONE
  },
  [SpeechRecognitionErrorType.LANGUAGE_NOT_SUPPORTED]: {
    type: SpeechRecognitionErrorType.LANGUAGE_NOT_SUPPORTED,
    category: SpeechRecognitionErrorCategory.SYSTEM,
    message: 'The language specified is not supported.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RESTART
  },
  [SpeechRecognitionErrorType.NETWORK]: {
    type: SpeechRecognitionErrorType.NETWORK,
    category: SpeechRecognitionErrorCategory.NETWORK,
    message: 'A network error occurred during speech recognition.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RETRY
  },
  [SpeechRecognitionErrorType.NO_SPEECH]: {
    type: SpeechRecognitionErrorType.NO_SPEECH,
    category: SpeechRecognitionErrorCategory.USER,
    message: 'No speech was detected.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RETRY
  },
  [SpeechRecognitionErrorType.NOT_ALLOWED]: {
    type: SpeechRecognitionErrorType.NOT_ALLOWED,
    category: SpeechRecognitionErrorCategory.PERMISSION,
    message: 'The user has denied permission to use the microphone.',
    recoverable: false,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.REQUEST_PERMISSION
  },
  [SpeechRecognitionErrorType.SERVICE_NOT_ALLOWED]: {
    type: SpeechRecognitionErrorType.SERVICE_NOT_ALLOWED,
    category: SpeechRecognitionErrorCategory.PERMISSION,
    message: 'The speech recognition service is not allowed in this context.',
    recoverable: false,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.SWITCH_BROWSER
  },
  [SpeechRecognitionErrorType.INITIALIZATION_FAILED]: {
    type: SpeechRecognitionErrorType.INITIALIZATION_FAILED,
    category: SpeechRecognitionErrorCategory.SYSTEM,
    message: 'Failed to initialize speech recognition.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RESTART
  },
  [SpeechRecognitionErrorType.BROWSER_NOT_SUPPORTED]: {
    type: SpeechRecognitionErrorType.BROWSER_NOT_SUPPORTED,
    category: SpeechRecognitionErrorCategory.BROWSER,
    message: 'Speech recognition is not supported in this browser.',
    recoverable: false,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.SWITCH_BROWSER
  },
  [SpeechRecognitionErrorType.TIMEOUT]: {
    type: SpeechRecognitionErrorType.TIMEOUT,
    category: SpeechRecognitionErrorCategory.SYSTEM,
    message: 'Speech recognition timed out.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RETRY
  },
  [SpeechRecognitionErrorType.UNKNOWN]: {
    type: SpeechRecognitionErrorType.UNKNOWN,
    category: SpeechRecognitionErrorCategory.UNKNOWN,
    message: 'An unknown error occurred during speech recognition.',
    recoverable: true,
    recoveryStrategy: SpeechRecognitionRecoveryStrategy.RESTART
  }
};

/**
 * Get error info for a given error type
 */
export function getSpeechRecognitionErrorInfo(errorType: string): SpeechRecognitionErrorInfo {
  const type = errorType as SpeechRecognitionErrorType;
  return speechRecognitionErrorMap[type] || speechRecognitionErrorMap[SpeechRecognitionErrorType.UNKNOWN];
}

/**
 * Check if speech recognition is supported in the current browser
 */
export function isSpeechRecognitionSupported(): boolean {
  if (typeof window === 'undefined') return false;
  
  return !!(
    (window as any).SpeechRecognition || 
    (window as any).webkitSpeechRecognition || 
    (window as any).mozSpeechRecognition || 
    (window as any).msSpeechRecognition
  );
}

/**
 * Get the appropriate SpeechRecognition constructor for the current browser
 */
export function getSpeechRecognitionConstructor(): any {
  if (typeof window === 'undefined') return null;
  
  return (
    (window as any).SpeechRecognition || 
    (window as any).webkitSpeechRecognition || 
    (window as any).mozSpeechRecognition || 
    (window as any).msSpeechRecognition
  );
}
