# Database Updates for Voice Selection Feature

## Overview
This document outlines the database schema updates made to support the voice selection and natural speech processing features in BotFusion X.

## Updated Tables

### chat_interfaces Table
The `chat_interfaces` table has been updated with 5 new columns to support voice selection and natural speech processing.

#### New Columns Added

| Column Name | Data Type | Default Value | Description |
|-------------|-----------|---------------|-------------|
| `voice_model` | TEXT | 'thalia' | Voice selection for TTS (thalia, asteria, helena, arcas, apollo, zeus) |
| `enable_natural_speech` | BOOLEAN | true | Toggle for natural speech processing via GPT models |
| `natural_speech_model` | TEXT | 'gpt-4o-mini' | AI model for natural speech processing |
| `natural_speech_temperature` | DECIMAL(3,2) | 0.7 | Creativity level for natural speech (0.0-1.0) |
| `natural_speech_max_tokens` | INTEGER | 500 | Maximum tokens for natural speech response (100-1000) |

#### Voice Model Options
The `voice_model` column supports 6 Deepgram Aura voices:

**Female Voices:**
- `thalia` - Warm and friendly (default)
- `asteria` - Professional and clear
- `helena` - Sophisticated and elegant

**Male Voices:**
- `arcas` - Confident and authoritative
- `apollo` - Smooth and engaging
- `zeus` - Deep and commanding

#### Natural Speech Models
The `natural_speech_model` column supports:
- `gpt-4o-mini` - Recommended for best balance of quality and speed
- `gpt-4o` - Higher quality processing
- `gpt-3.5-turbo` - Faster processing

## Database Indexes
Created performance indexes for the new columns:

```sql
-- Index for voice model queries
CREATE INDEX idx_chat_interfaces_voice_model ON chat_interfaces(voice_model);

-- Index for natural speech analytics
CREATE INDEX idx_chat_interfaces_natural_speech ON chat_interfaces(enable_natural_speech, natural_speech_model);
```

## Migration Applied
The migration was successfully applied to the production database (sashaiqgcmqysumbawag) on 2025-01-15.

### Migration SQL
```sql
-- Add voice selection column
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS voice_model TEXT DEFAULT 'thalia';

-- Add natural speech processing columns
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS enable_natural_speech BOOLEAN DEFAULT true;
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_model TEXT DEFAULT 'gpt-4o-mini';
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_temperature DECIMAL(3,2) DEFAULT 0.7;
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS natural_speech_max_tokens INTEGER DEFAULT 500;
```

## Verification
- ✅ All columns added successfully
- ✅ Default values applied to existing records
- ✅ Indexes created for performance
- ✅ Column comments added for documentation
- ✅ API compatibility verified

## Impact on Existing Data
- All existing chat interfaces automatically received the default values
- No data loss or corruption occurred
- Backward compatibility maintained

## API Integration
The updated schema is fully integrated with:
- Chat interface creation/update APIs
- Frontend voice selection dropdown
- TTS components across all platforms (chat, embed, widget)
- Natural speech processing endpoints

## Next Steps
The database is now ready to support:
1. Voice selection per chat interface
2. Natural speech processing configuration
3. Voice persistence across sessions
4. Analytics on voice usage patterns

## Files Updated
- `migrations/add_voice_and_natural_speech_columns.sql` - Migration script
- `src/app/api/chat-interfaces/route.ts` - API handles new columns
- `src/components/ChatInterfaceForm.tsx` - Frontend form integration
- `src/lib/voice-config.ts` - Voice configuration system
