import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { checkChatInterfaceLimit, checkTierPermission } from '@/lib/middleware/tierMiddleware'
import { TIER_CONFIGS } from '@/lib/tiers'

export async function GET() {
  const supabase = createClient()

  // Check if chat_stats table exists by trying to query it directly
  let hasStatsTable = false
  try {
    const { data: statsData, error: statsError } = await supabase
      .from('chat_stats')
      .select('count(*)')
      .limit(1)

    hasStatsTable = !statsError
  } catch (err) {
    console.log('Error checking chat_stats table:', err)
    // Table doesn't exist, continue without it
  }

  const query = supabase
    .from('chat_interfaces')
    .select('*')
    .order('createdat', { ascending: false })

  if (hasStatsTable) {
    query.select(`
      *,
      chat_stats (
        message_count,
        last_message_at
      )
    `)
  }

  const { data, error } = await query
  if (error) {
    console.error('Error fetching chat interfaces:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  // Return empty array if no data
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return NextResponse.json([])
  }

  // Ensure all chat interfaces have the use_black_outline property
  // This is important for the frontend to maintain the correct state
  if (Array.isArray(data)) {
    const enhancedData = data.map(chat => {
      if (chat.use_black_outline === undefined) {
        return {
          ...chat,
          use_black_outline: false // Default to false if not present
        };
      }
      return chat;
    });
    return NextResponse.json(enhancedData)
  }

  return NextResponse.json(data)
}

export async function POST(request: Request) {
  const supabase = createClient()

  let body;
  try {
    body = await request.json()
  } catch (error) {
    console.error('Error parsing JSON body:', error)
    return Response.json({ error: 'Invalid JSON body' }, { status: 400 })
  }
  const {
    name,
    description,
    webhookUrl,
    primaryColor = '#3b82f6',
    useGradientHeader = false,
    gradientStartColor = '#3b82f6',
    gradientEndColor = '#9333ea',
    gradientDirection = 'to bottom',
    userBubbleColor = '#ffffff',
    botBubbleColor = '#3b82f6',
    userTextColor = '#000000',
    botTextColor = '#ffffff',
    logoUrl = '',
    darkMode = false,
    showName = true,
    useBlackOutline = false,
    showPoweredBy = true,
    poweredByText = 'Powered by BotFusion',
    poweredByUrl = 'https://botfusion.io',
    poweredByTextColor,
    welcomeMessage = '',
    enableNaturalSpeech = false, // OPTIMIZATION: Disabled by default for faster responses
    naturalSpeechModel = 'gpt-4o-mini', // Default model - supports o3 models too
    naturalSpeechTemperature = 0.3, // OPTIMIZATION: Reduced for faster processing
    naturalSpeechMaxTokens = 200, // OPTIMIZATION: Reduced for faster processing
    voiceModel = 'thalia' // Default voice model for TTS
  } = body

  console.log('Received chat interface data:', {
    name,
    description,
    webhookUrl,
    primaryColor,
    useGradientHeader,
    gradientStartColor,
    gradientEndColor,
    gradientDirection,
    userBubbleColor,
    botBubbleColor,
    userTextColor,
    botTextColor,
    logoUrl,
    darkMode,
    showName,
    useBlackOutline,
    showPoweredBy,
    poweredByText,
    poweredByUrl,
    poweredByTextColor,
    welcomeMessage
  });

  if (!name || !webhookUrl) {
    return NextResponse.json({ error: 'Name and webhookUrl are required' }, { status: 400 })
  }

  // Check user authentication and get user ID
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError || !user) {
    return NextResponse.json({
      error: 'Authentication required',
      details: 'You must be logged in to create chat interfaces'
    }, { status: 401 })
  }

  // Check tier limits for chat interface creation
  const tierCheck = await checkChatInterfaceLimit(user.id)

  if (!tierCheck.allowed) {
    return NextResponse.json({
      error: 'Chat interface limit reached',
      reason: tierCheck.reason,
      userTier: tierCheck.userTier,
      upgradeRequired: tierCheck.upgradeRequired,
      details: `Your ${tierCheck.userTier} tier allows a limited number of chat interfaces. Please upgrade to create more.`
    }, { status: 403 })
  }

  // Get user tier for branding validation
  const userTier = tierCheck.userTier
  const tierConfig = TIER_CONFIGS[userTier]

  // Enforce tier-based branding restrictions
  let finalShowPoweredBy = showPoweredBy
  let finalPoweredByText = poweredByText
  let finalPoweredByUrl = poweredByUrl

  // Free tier must show branding
  if (!tierConfig.brandingRemoval) {
    finalShowPoweredBy = true
  }

  // Only Pro tier can customize branding text/URL
  if (!tierConfig.customBrandingText) {
    finalPoweredByText = 'Powered by BotFusion X'
    finalPoweredByUrl = 'https://botfusion.io'
  }

  try {
    // Check if a webhook with the same name already exists
    // This check is incorrect - we should be checking if a webhook URL exists, not if the name matches
    // Commenting out this check for now
    /*
    const { data: existingWebhook } = await supabase
      .from('webhooks')
      .select('id')
      .eq('name', name)
      .single();

    if (existingWebhook) {
      return NextResponse.json({
        error: 'A webhook with this name already exists. Please choose a different name for your chat interface.'
      }, { status: 400 });
    }
    */

    // Check if another chat interface with the same name exists
    const { data: existingChatInterface } = await supabase
      .from('chat_interfaces')
      .select('id')
      .eq('name', name)
      .single();

    if (existingChatInterface) {
      return NextResponse.json({
        error: 'Another chat interface with this name already exists. Please choose a different name.'
      }, { status: 400 });
    }
    // Generate a unique ID for the chat interface
    const uniqueId = crypto.randomUUID()

    // Get the base URL for the application
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'

    // Generate the unique URL and embed code
    const uniqueUrl = `${baseUrl}/chat/${uniqueId}`

    // Generate the embed code
    const embedCode = `<iframe
  src="${baseUrl}/embed/${uniqueId}"
  width="100%"
  height="600px"
  frameborder="0"
  allow="microphone"
  title="${name} Chat"
></iframe>`

    // Create the base insert data without the powered by fields
    const baseInsertData = {
      id: uniqueId, // Use the generated UUID
      name,
      description,
      webhookurl: webhookUrl, // Note: column name is lowercase
      unique_url: uniqueUrl,
      embed_code: embedCode,
      user_id: user.id, // Add user_id for ownership and tier enforcement
      primary_color: primaryColor,
      use_gradient_header: useGradientHeader,
      gradient_start_color: gradientStartColor,
      gradient_end_color: gradientEndColor,
      gradient_direction: gradientDirection,
      user_bubble_color: userBubbleColor,
      bot_bubble_color: botBubbleColor,
      user_text_color: userTextColor,
      bot_text_color: botTextColor,
      logo_url: logoUrl,
      dark_mode: darkMode,
      show_name: showName,
      use_black_outline: useBlackOutline,
      show_powered_by: finalShowPoweredBy, // Use tier-enforced value
      powered_by_text: finalPoweredByText, // Use tier-enforced value
      powered_by_url: finalPoweredByUrl, // Use tier-enforced value
      powered_by_text_color: poweredByTextColor,
      welcome_message: welcomeMessage,
      enable_natural_speech: enableNaturalSpeech,
      voice_model: voiceModel,
      natural_speech_model: naturalSpeechModel,
      natural_speech_temperature: naturalSpeechTemperature,
      natural_speech_max_tokens: naturalSpeechMaxTokens,
      createdat: new Date().toISOString()
    };

    // Handle missing columns gracefully by removing them from insert data if they don't exist
    const handleMissingColumns = (insertData: any) => {
      // List of columns that might not exist yet
      const optionalColumns = [
        'use_black_outline',
        'enable_natural_speech',
        'natural_speech_model',
        'natural_speech_temperature',
        'natural_speech_max_tokens'
      ];

      // Create a copy without optional columns for fallback
      const fallbackData = { ...insertData };
      optionalColumns.forEach(col => delete fallbackData[col]);

      return { insertData, fallbackData };
    };

    // Try to insert with all fields including powered by and natural speech
    try {
      const fullInsertData = {
        ...baseInsertData,
        show_powered_by: showPoweredBy,
        powered_by_text: poweredByText,
        powered_by_url: poweredByUrl,
        powered_by_text_color: poweredByTextColor
      };

      const { insertData, fallbackData } = handleMissingColumns(fullInsertData);

      // Try with all columns first
      const { data, error } = await supabase
        .from('chat_interfaces')
        .insert([insertData])
        .select();

      if (!error) {
        return NextResponse.json(data ? (Array.isArray(data) ? data[0] : data) : insertData, { status: 200 });
      }

      // If there's an error related to missing columns, try with fallback data
      if (error.message && (
        error.message.includes("powered_by_text") ||
        error.message.includes("powered_by_url") ||
        error.message.includes("show_powered_by") ||
        error.message.includes("use_black_outline") ||
        error.message.includes("enable_natural_speech") ||
        error.message.includes("natural_speech_") ||
        error.message.includes("voice_model")
      )) {
        console.log('Falling back to insert without missing fields');

        // Try with fallback data (without optional columns)
        const { data: fallbackResult, error: fallbackError } = await supabase
          .from('chat_interfaces')
          .insert([fallbackData])
          .select();

        if (!fallbackError) {
          // Include the missing fields in the response for UI consistency
          const responseData = {
            ...(Array.isArray(fallbackResult) ? fallbackResult[0] : fallbackResult),
            show_powered_by: showPoweredBy,
            powered_by_text: poweredByText,
            powered_by_url: poweredByUrl,
            powered_by_text_color: poweredByTextColor,
            enable_natural_speech: enableNaturalSpeech,
            natural_speech_model: naturalSpeechModel,
            natural_speech_temperature: naturalSpeechTemperature,
            natural_speech_max_tokens: naturalSpeechMaxTokens
          };
          return NextResponse.json(responseData, { status: 200 });
        }
      } else {
        throw error;
      }
    } catch (insertError) {
      console.error('Error with full insert:', insertError);
    }

    // Final fallback: Insert with minimal data
    const { fallbackData } = handleMissingColumns(baseInsertData);
    const { data, error } = await supabase
      .from('chat_interfaces')
      .insert([fallbackData])
      .select()

    if (error) {
      console.error('Supabase insert error:', error)
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 })
    }

    // Handle successful insert
    const responseData = data
      ? (Array.isArray(data) ? data[0] : data)
      : {
          ...fallbackData,
          // Include all fields in the response even if they weren't saved to the database
          // This allows the UI to work correctly even if the columns don't exist yet
          show_powered_by: showPoweredBy,
          powered_by_text: poweredByText,
          powered_by_url: poweredByUrl,
          powered_by_text_color: poweredByTextColor,
          enable_natural_speech: enableNaturalSpeech,
          natural_speech_model: naturalSpeechModel,
          natural_speech_temperature: naturalSpeechTemperature,
          natural_speech_max_tokens: naturalSpeechMaxTokens,
          voice_model: voiceModel
        }

    return NextResponse.json(responseData, { status: 200 })
  } catch (error) {
    console.error('Error creating chat interface:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  const supabase = createClient()
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  let body;
  try {
    body = await request.json()
  } catch (error) {
    console.error('Error parsing JSON body:', error)
    return Response.json({ error: 'Invalid JSON body' }, { status: 400 })
  }
  const {
    name,
    description,
    webhookUrl,
    primaryColor,
    useGradientHeader,
    gradientStartColor,
    gradientEndColor,
    gradientDirection,
    userBubbleColor,
    botBubbleColor,
    userTextColor,
    botTextColor,
    logoUrl,
    darkMode,
    showName,
    useBlackOutline,
    showPoweredBy,
    poweredByText,
    poweredByUrl,
    poweredByTextColor,
    welcomeMessage,
    enableNaturalSpeech,
    naturalSpeechModel,
    naturalSpeechTemperature,
    naturalSpeechMaxTokens,
    voiceModel
  } = body

  if (!id) {
    return NextResponse.json({ error: 'ID is required' }, { status: 400 })
  }

  if (!name || !webhookUrl) {
    return NextResponse.json({ error: 'Name and webhookUrl are required' }, { status: 400 })
  }

  // Check user authentication and tier permissions for updates
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError || !user) {
    return NextResponse.json({
      error: 'Authentication required',
      details: 'You must be logged in to update chat interfaces'
    }, { status: 401 })
  }

  // Get user tier for branding validation
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('tier')
    .eq('id', user.id)
    .single()

  const userTier = profile?.tier || 'free'
  const tierConfig = TIER_CONFIGS[userTier]

  // Enforce tier-based branding restrictions for updates
  let finalShowPoweredBy = showPoweredBy
  let finalPoweredByText = poweredByText
  let finalPoweredByUrl = poweredByUrl

  // Free tier must show branding
  if (!tierConfig.brandingRemoval) {
    finalShowPoweredBy = true
  }

  // Only Pro tier can customize branding text/URL
  if (!tierConfig.customBrandingText) {
    finalPoweredByText = 'Powered by BotFusion X'
    finalPoweredByUrl = 'https://botfusion.io'
  }

  // Check if a webhook with the same name already exists
  // This check is incorrect - we should be checking if a webhook URL exists, not if the name matches
  // Commenting out this check for now
  /*
  const { data: existingWebhook } = await supabase
    .from('webhooks')
    .select('id')
    .eq('name', name)
    .single();

  if (existingWebhook) {
    return NextResponse.json({
      error: 'A webhook with this name already exists. Please choose a different name for your chat interface.'
    }, { status: 400 });
  }
  */

  // Check if another chat interface with the same name exists (excluding the current one)
  const { data: existingChatInterface } = await supabase
    .from('chat_interfaces')
    .select('id')
    .eq('name', name)
    .neq('id', id)
    .single();

  if (existingChatInterface) {
    return NextResponse.json({
      error: 'Another chat interface with this name already exists. Please choose a different name.'
    }, { status: 400 });
  }

  // Get the base URL for the application
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'

  // Get the bypass secret for iframe embedding
  const bypassSecret = process.env.VERCEL_AUTOMATION_BYPASS_SECRET || 'Q3W0KiXNSiZt6HCFH0e1zYfT2MwpCR2F';

  // Generate the unique URL and embed code
  const uniqueUrl = `${baseUrl}/chat/${id}`

  // Generate the embed code with bypass secret
  const embedCode = `<iframe
  src="${baseUrl}/embed/${id}?x-vercel-protection-bypass=${bypassSecret}"
  width="100%"
  height="600px"
  frameborder="0"
  allow="microphone; autoplay"
  title="${name} Chat"
></iframe>`

  const updateData: any = {
    name,
    description,
    webhookurl: webhookUrl,
    unique_url: uniqueUrl,
    embed_code: embedCode
  };

  // Only add customization fields if they are provided
  if (primaryColor !== undefined) updateData.primary_color = primaryColor;
  if (useGradientHeader !== undefined) updateData.use_gradient_header = useGradientHeader;
  if (gradientStartColor !== undefined) updateData.gradient_start_color = gradientStartColor;
  if (gradientEndColor !== undefined) updateData.gradient_end_color = gradientEndColor;
  if (gradientDirection !== undefined) updateData.gradient_direction = gradientDirection;
  if (userBubbleColor !== undefined) updateData.user_bubble_color = userBubbleColor;
  if (botBubbleColor !== undefined) updateData.bot_bubble_color = botBubbleColor;
  if (userTextColor !== undefined) updateData.user_text_color = userTextColor;
  if (botTextColor !== undefined) updateData.bot_text_color = botTextColor;
  if (logoUrl !== undefined) updateData.logo_url = logoUrl;
  if (darkMode !== undefined) updateData.dark_mode = darkMode;
  if (showName !== undefined) updateData.show_name = showName;
  if (useBlackOutline !== undefined) updateData.use_black_outline = useBlackOutline;
  // Use tier-enforced branding values for updates
  if (finalShowPoweredBy !== undefined) updateData.show_powered_by = finalShowPoweredBy;
  if (finalPoweredByText !== undefined) updateData.powered_by_text = finalPoweredByText;
  if (finalPoweredByUrl !== undefined) updateData.powered_by_url = finalPoweredByUrl;
  if (poweredByTextColor !== undefined) updateData.powered_by_text_color = poweredByTextColor;
  if (welcomeMessage !== undefined) updateData.welcome_message = welcomeMessage;
  if (enableNaturalSpeech !== undefined) updateData.enable_natural_speech = enableNaturalSpeech;
  if (naturalSpeechModel !== undefined) updateData.natural_speech_model = naturalSpeechModel;
  if (naturalSpeechTemperature !== undefined) updateData.natural_speech_temperature = naturalSpeechTemperature;
  if (naturalSpeechMaxTokens !== undefined) updateData.natural_speech_max_tokens = naturalSpeechMaxTokens;
  if (voiceModel !== undefined) updateData.voice_model = voiceModel;
  // First try without the powered by fields
  const { data: initialData, error: initialError } = await supabase
    .from('chat_interfaces')
    .update(updateData)
    .eq('id', id)
    .select()

  if (initialError) {
    console.error('Supabase update error:', initialError)

    // If the error is related to missing columns, try again without those fields
    if (initialError.message && (
      initialError.message.includes("use_black_outline") ||
      initialError.message.includes("enable_natural_speech") ||
      initialError.message.includes("natural_speech_") ||
      initialError.message.includes("voice_model") ||
      initialError.message.includes("show_powered_by") ||
      initialError.message.includes("powered_by_")
    )) {
      console.log('Removing problematic fields and retrying update');

      // FIXED: Remove all potentially problematic fields
      const problematicFields = [
        'use_black_outline',
        'enable_natural_speech',
        'natural_speech_model',
        'natural_speech_temperature',
        'natural_speech_max_tokens',
        'voice_model',
        'show_powered_by',
        'powered_by_text',
        'powered_by_url',
        'powered_by_text_color'
      ];

      problematicFields.forEach(field => {
        if (updateData[field] !== undefined) {
          console.log(`Removing ${field} from update data`);
          delete updateData[field];
        }
      });

      // Try the update again
      const { data: retryData, error: retryError } = await supabase
        .from('chat_interfaces')
        .update(updateData)
        .eq('id', id)
        .select()

      if (retryError) {
        console.error('Supabase retry update error:', retryError)
        return NextResponse.json({ error: retryError.message }, { status: 500 })
      }

      if (retryData) {
        const result = Array.isArray(retryData) ? retryData[0] : retryData
        if (!result.id) {
          console.error('Updated data missing ID:', result)
          return NextResponse.json({ error: 'Updated data missing ID' }, { status: 500 })
        }

        // Add back the properties that couldn't be saved to the database
        // This ensures the frontend gets the correct values even if the columns don't exist
        const enhancedResult = {
          ...result,
          ...(useBlackOutline !== undefined && { use_black_outline: useBlackOutline }),
          ...(enableNaturalSpeech !== undefined && { enable_natural_speech: enableNaturalSpeech }),
          ...(naturalSpeechModel !== undefined && { natural_speech_model: naturalSpeechModel }),
          ...(naturalSpeechTemperature !== undefined && { natural_speech_temperature: naturalSpeechTemperature }),
          ...(naturalSpeechMaxTokens !== undefined && { natural_speech_max_tokens: naturalSpeechMaxTokens }),
          ...(voiceModel !== undefined && { voice_model: voiceModel }),
          ...(showPoweredBy !== undefined && { show_powered_by: showPoweredBy }),
          ...(poweredByText !== undefined && { powered_by_text: poweredByText }),
          ...(poweredByUrl !== undefined && { powered_by_url: poweredByUrl }),
          ...(poweredByTextColor !== undefined && { powered_by_text_color: poweredByTextColor })
        };

        return NextResponse.json(enhancedResult)
      }
    }

    return NextResponse.json({ error: initialError.message }, { status: 500 })
  }

  // If successful, return the result
  if (initialData) {
    const result = Array.isArray(initialData) ? initialData[0] : initialData
    if (!result.id) {
      console.error('Updated data missing ID:', result)
      return NextResponse.json({ error: 'Updated data missing ID' }, { status: 500 })
    }

    // Ensure the use_black_outline property is included in the response
    // This is important for the frontend to maintain the correct state
    if (result.use_black_outline === undefined && useBlackOutline !== undefined) {
      const enhancedResult = {
        ...result,
        use_black_outline: useBlackOutline
      };
      return NextResponse.json(enhancedResult)
    }

    return NextResponse.json(result)
  }

  // If we get here, something unexpected happened
  return NextResponse.json({ error: 'Failed to update chat interface' }, { status: 500 })
}

export async function DELETE(request: Request) {
  const supabase = createClient()
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  if (!id) {
    return NextResponse.json({ error: 'ID is required' }, { status: 400 })
  }

  const { error } = await supabase
    .from('chat_interfaces')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Supabase delete error:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ success: true })
}