'use client'

import { useState, useEffect, Suspense } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { signInWithEmail, signUpWithEmail, signInWithOAuth } from '@/lib/supabase/auth'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'

// Client component that uses useSearchParams
function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLogin, setIsLogin] = useState(true)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  // Check if the signup parameter is present in the URL
  useEffect(() => {
    const signup = searchParams.get('signup')
    if (signup === 'true') {
      setIsLogin(false)
    }
  }, [searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      if (isLogin) {
        await signInWithEmail(email, password)
      } else {
        const response = await signUpWithEmail(email, password)
        if (response.error) throw response.error
        if (response.data?.user?.identities?.length === 0) {
          throw new Error('User already registered')
        }
        setError('Check your email for verification link')
        return
      }
      router.push('/dashboard')
    } catch (err) {
      setError(err instanceof Error ?
        err.message.includes('Email not confirmed') ? 'Please verify your email first' :
        err.message.includes('Invalid login credentials') ? 'Invalid email or password' :
        err.message
        : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleOAuthSignIn = async (provider: 'google' | 'github') => {
    setLoading(true)
    setError('')
    try {
      await signInWithOAuth(provider)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign in')
      setLoading(false)
    }
  }

  return (
    <main className="min-h-screen relative overflow-hidden font-sans tech-grid-bg text-glow-blue">
      {/* Glowing background elements */}
      <div className="absolute top-[-150px] left-[-150px] w-[500px] h-[500px] bg-gradient-to-tr from-blue-900 to-indigo-800 rounded-full filter blur-3xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute top-[80px] right-[-120px] w-[400px] h-[400px] bg-gradient-to-tr from-indigo-800 to-blue-900 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-4000"></div>

      <div className="flex min-h-screen flex-col items-center justify-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md space-y-6 glass-card p-8 neon-border"
        >
          <h1 className="text-center text-2xl font-bold text-neon-blue">
            {isLogin ? 'Welcome Back' : 'Create Account'}
          </h1>

          {error && (
            <p className="text-center text-sm text-red-500">
              {error}
            </p>
          )}

          <div className="flex gap-2">
            <Button
              type="button"
              onClick={() => handleOAuthSignIn('google')}
              className="flex-1 glass-card hover:shadow-glow-blue"
              disabled={loading}
            >
              Google
            </Button>
            <Button
              type="button"
              onClick={() => handleOAuthSignIn('github')}
              className="flex-1 glass-card hover:shadow-glow-blue"
              disabled={loading}
            >
              GitHub
            </Button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-neon-blue/20"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-glass px-2 text-neon-blue/70">
                Or continue with
              </span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4" noValidate>
            <Input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="glass-input"
              required
              disabled={loading}
            />
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="glass-input"
              required
              minLength={6}
              disabled={loading}
            />
            <Button
              type="submit"
              className="w-full glass-card hover:shadow-glow-blue"
              disabled={loading}
            >
              {loading ? 'Processing...' : isLogin ? 'Sign In' : 'Sign Up'}
            </Button>
          </form>

          <p className="text-center text-sm text-neon-blue/70">
            {isLogin ? "Don't have an account? " : "Already have an account? "}
            <button
              onClick={() => setIsLogin(!isLogin)}
              className="text-neon-blue hover:underline"
              type="button"
              disabled={loading}
            >
              {isLogin ? 'Sign up' : 'Sign in'}
            </button>
          </p>
        </motion.div>
      </div>
    </main>
  )
}

// Loading fallback component
function LoginLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center tech-grid-bg">
      <div className="glass-card p-8 rounded-lg">
        <p className="text-neon-blue">Loading...</p>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function LoginPage() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginForm />
    </Suspense>
  );
}