<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodePen Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CodePen Embed Test</h1>

    <div class="note">
        <strong>Instructions:</strong> Copy the HTML below and paste it into a new CodePen to test embedding on a different domain.
    </div>

    <h2>HTML for CodePen:</h2>
    <pre>&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Chat Widget Embed Test&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Chat Widget Embed Test on CodePen&lt;/h1&gt;

    &lt;iframe
        src="https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/embed/1"
        width="400"
        height="600"
        frameborder="0"
        style="border: 1px solid #ccc;"
    &gt;&lt;/iframe&gt;

    &lt;script&gt;
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });

        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all iframes and resources');
        });
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</pre>

    <div class="note">
        <strong>Steps:</strong>
        <ol>
            <li>Go to <a href="https://codepen.io/pen/" target="_blank">CodePen</a></li>
            <li>Paste the HTML above into the HTML panel</li>
            <li>Open the browser console (F12 or right-click > Inspect > Console)</li>
            <li>Check for any errors related to X-Frame-Options, CSP, or 401 Unauthorized</li>
        </ol>
    </div>

    <h2>Direct CodePen Link:</h2>
    <p>For convenience, you can also use this pre-created CodePen:</p>
    <a href="https://codepen.io/pen?template=abXYZqr" target="_blank">Open Test in CodePen</a>
</body>
</html>
