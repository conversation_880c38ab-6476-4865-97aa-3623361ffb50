'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import SimpleAudioPlayer from '@/components/SimpleAudioPlayer';

export default function TTSDebugPage() {
  const [text, setText] = useState('This is a test of the text-to-speech functionality. If you can hear this message, the TTS is working correctly.');
  const [voice, setVoice] = useState('nova');
  const [logs, setLogs] = useState<string[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const logRef = useRef<HTMLDivElement>(null);

  // Add a log message
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ${message}`;
    setLogs(prevLogs => [...prevLogs, logMessage]);
  }, []);

  // Scroll to the bottom of the logs when they update
  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);

  // Handle TTS errors
  const handleTtsError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    addLog(`TTS Error: ${errorMessage}`);
    setIsPlaying(false);
  }, [addLog]);

  // Handle audio ready event
  const handleAudioReady = useCallback(() => {
    addLog('Audio is ready for playback');
    setIsPlaying(false);
  }, []);

  // Clear logs
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-4">TTS Debug Page</h1>

      <div className="mb-4">
        <label className="block mb-2">Test Text:</label>
        <textarea
          className="w-full p-2 border rounded"
          rows={4}
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
      </div>

      <div className="mb-4">
        <label className="block mb-2">Voice:</label>
        <select
          className="p-2 border rounded"
          value={voice}
          onChange={(e) => setVoice(e.target.value)}
        >
          <option value="nova">Nova</option>
          <option value="alloy">Alloy</option>
          <option value="echo">Echo</option>
          <option value="fable">Fable</option>
          <option value="onyx">Onyx</option>
          <option value="shimmer">Shimmer</option>
        </select>
      </div>

      <div className="mb-4 flex space-x-4">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => {
            addLog(`Testing TTS with voice=${voice} and text="${text.substring(0, 30)}..."`);
            setIsPlaying(true);
            setError(null);
          }}
        >
          Test TTS
        </button>

        <button
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          onClick={clearLogs}
        >
          Clear Logs
        </button>
      </div>

      {error && (
        <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">SimpleAudioPlayer Component</h2>
        <div className="mb-2">
          <button
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 mr-2"
            onClick={() => {
              // Create a direct URL to the TTS API for manual testing
              const ttsUrl = new URL('/api/deepgram-tts', window.location.origin);
              ttsUrl.searchParams.append('text', text);
              ttsUrl.searchParams.append('voice', voice);
              ttsUrl.searchParams.append('t', Date.now().toString());

              // Open in a new tab for direct testing
              window.open(ttsUrl.toString(), '_blank');
              addLog(`Opening direct TTS URL in new tab: ${ttsUrl.toString()}`);
            }}
          >
            Test Direct TTS URL
          </button>
        </div>
        <SimpleAudioPlayer
          text={isPlaying ? text : ''}
          voice={voice}
          autoPlay={true}
          onError={handleTtsError}
          onAudioReady={handleAudioReady}
        />
      </div>

      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">Logs</h2>
        <div
          ref={logRef}
          className="h-64 overflow-y-auto p-2 bg-gray-100 font-mono text-sm"
        >
          {logs.map((log, index) => (
            <div key={index} className="mb-1">{log}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
