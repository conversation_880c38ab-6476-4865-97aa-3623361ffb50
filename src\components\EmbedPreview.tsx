'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'

// Default origin for server-side rendering
const DEFAULT_ORIGIN = process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-bnul21hvo-tellivisions-projects.vercel.app'

export function EmbedPreview({ chatId }: { chatId: string }) {
  const [previewType, setPreviewType] = useState<'standard' | 'html-only' | 'pure-html'>('standard')
  const [origin, setOrigin] = useState<string>(DEFAULT_ORIGIN)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Set the origin in client-side code and mark as client
    setOrigin(window.location.origin)
    setIsClient(true)
  }, [])

  // Only render the full component on the client side
  if (!isClient) {
    return (
      <div className="mt-6 border border-gray-700 rounded-md p-4 bg-gray-800">
        <h3 className="text-lg font-medium text-neon-blue mb-4">Live Preview</h3>
        <p className="text-gray-400 text-center py-4">Loading preview options...</p>
      </div>
    )
  }

  return (
    <div className="mt-6 border border-gray-700 rounded-md p-4 bg-gray-800">
      <h3 className="text-lg font-medium text-neon-blue mb-4">Live Preview</h3>

      <Tabs defaultValue="standard" onValueChange={(value) => setPreviewType(value as any)} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4 bg-gray-800 text-gray-400">
          <TabsTrigger value="standard" className="data-[state=active]:bg-gray-700 data-[state=active]:text-neon-blue">
            Standard
          </TabsTrigger>
          <TabsTrigger value="html-only" className="data-[state=active]:bg-gray-700 data-[state=active]:text-neon-blue">
            HTML-Only
          </TabsTrigger>
          <TabsTrigger value="pure-html" className="data-[state=active]:bg-gray-700 data-[state=active]:text-neon-blue">
            Pure HTML
          </TabsTrigger>
        </TabsList>

        <TabsContent value="standard">
          <div className="relative border border-gray-700 rounded-md p-4 h-[300px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-gray-400 mb-4">Standard embed preview:</p>
              <Button
                onClick={() => {
                  const previewWindow = window.open('', '_blank', 'width=800,height=600')
                  if (previewWindow) {
                    previewWindow.document.write(`
                      <!DOCTYPE html>
                      <html lang="en">
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Standard Embed Preview</title>
                        <style>
                          body {
                            font-family: Arial, sans-serif;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 20px;
                          }
                          h1 {
                            color: #333;
                          }
                          .content {
                            min-height: 1000px;
                            padding: 20px;
                            background-color: #f9f9f9;
                            border-radius: 8px;
                            margin-top: 20px;
                          }
                        </style>
                      </head>
                      <body>
                        <h1>Standard Embed Preview</h1>
                        <div class="content">
                          <h2>Sample Content</h2>
                          <p>This is a preview of the standard embed code. The chat button should appear in the bottom-right corner.</p>

                          <!-- BotFusion Chat Widget -->
                          <script type="text/javascript">
                          (function(w, d, s, o, f, js, fjs) {
                              w['BotFusion-Widget'] = o;
                              w[o] = w[o] || function() {
                                  (w[o].q = w[o].q || []).push(arguments);
                              };
                              js = d.createElement(s);
                              fjs = d.getElementsByTagName(s)[0];
                              js.id = o;
                              js.src = f;
                              js.async = 1;
                              fjs.parentNode.insertBefore(js, fjs);
                          }(window, document, 'script', 'bf', '${origin}/api/embed-script'));

                          bf('init', {
                              chatId: "${chatId}",
                              position: 'right',
                              primaryColor: "#3b82f6",
                              userBubbleColor: "#ffffff",
                              botBubbleColor: "#3b82f6",
                              userTextColor: "#000000",
                              botTextColor: "#ffffff",
                              logoUrl: "",
                              darkMode: false,
                              greeting: "Hello! How can I help you today?"
                          });
                          </script>
                        </div>
                      </body>
                      </html>
                    `)
                    previewWindow.document.close()
                  }
                }}
                className="glass-card hover:shadow-glow-blue"
              >
                Open Preview in New Tab
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="html-only">
          <div className="relative border border-gray-700 rounded-md p-4 h-[300px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-gray-400 mb-4">HTML-Only embed preview:</p>
              <Button
                onClick={() => {
                  const previewWindow = window.open('', '_blank', 'width=800,height=600')
                  if (previewWindow) {
                    previewWindow.document.write(`
                      <!DOCTYPE html>
                      <html lang="en">
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>HTML-Only Embed Preview</title>
                        <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'none'; style-src 'self' 'unsafe-inline'; frame-src ${origin}">
                        <style>
                          body {
                            font-family: Arial, sans-serif;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 20px;
                          }
                          h1 {
                            color: #333;
                          }
                          .content {
                            min-height: 1000px;
                            padding: 20px;
                            background-color: #f9f9f9;
                            border-radius: 8px;
                            margin-top: 20px;
                          }
                        </style>
                      </head>
                      <body>
                        <h1>HTML-Only Embed Preview</h1>
                        <p>This preview has a strict CSP that blocks all scripts: <code>script-src 'none'</code></p>
                        <div class="content">
                          <h2>Sample Content</h2>
                          <p>This is a preview of the HTML-Only embed code. The chat button should appear in the bottom-right corner.</p>

                          <!-- HTML-Only Embed -->
                          <iframe
                            src="${origin}/api/html-embed?chatId=${chatId}"
                            style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
                            frameborder="0"
                            title="Chat Widget"
                            loading="lazy"
                            allow="microphone"
                          ></iframe>
                        </div>
                      </body>
                      </html>
                    `)
                    previewWindow.document.close()
                  }
                }}
                className="glass-card hover:shadow-glow-blue"
              >
                Open Preview in New Tab
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="pure-html">
          <div className="relative border border-gray-700 rounded-md p-4 h-[300px] flex items-center justify-center">
            <div className="text-center">
              <p className="text-gray-400 mb-4">Pure HTML embed preview:</p>
              <Button
                onClick={() => {
                  const previewWindow = window.open('', '_blank', 'width=800,height=600')
                  if (previewWindow) {
                    previewWindow.document.write(`
                      <!DOCTYPE html>
                      <html lang="en">
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Pure HTML Embed Preview</title>
                        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; frame-src ${origin}">
                      </head>
                      <body style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
                        <h1 style="color: #333;">Pure HTML Embed Preview</h1>
                        <p>This preview has the strictest possible CSP: <code style="background-color: #f0f0f0; padding: 2px 4px; border-radius: 3px;">default-src 'none'</code></p>
                        <div style="min-height: 1000px; padding: 20px; background-color: #f9f9f9; border-radius: 8px; margin-top: 20px;">
                          <h2 style="color: #333;">Sample Content</h2>
                          <p>This is a preview of the Pure HTML embed code. The chat button should appear in the bottom-right corner.</p>

                          <!-- Pure HTML Embed -->
                          <iframe
                            src="${origin}/api/pure-html-embed?chatId=${chatId}"
                            style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
                            frameborder="0"
                            title="Chat Widget"
                            loading="lazy"
                          ></iframe>
                        </div>
                      </body>
                      </html>
                    `)
                    previewWindow.document.close()
                  }
                }}
                className="glass-card hover:shadow-glow-blue"
              >
                Open Preview in New Tab
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
