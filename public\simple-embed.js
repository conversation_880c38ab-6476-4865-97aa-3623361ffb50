/**
 * BotFusion Simple Embed Script
 * This script is designed to be as simple as possible for maximum compatibility
 * Version: 1.0.0
 */

(function() {
  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.width = '60px';
  button.style.height = '60px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#3b82f6';
  button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  button.style.cursor = 'pointer';
  button.style.zIndex = '9999';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

  // Create the SVG icon
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Add hover effect
  button.onmouseover = function() {
    this.style.transform = 'scale(1.05)';
    this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  };

  button.onmouseout = function() {
    this.style.transform = 'scale(1)';
    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  };

  // Create the iframe element (hidden initially)
  var iframe = document.createElement('iframe');
  iframe.id = 'botfusion-chat-iframe';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px'; // Position at bottom-right corner like professional chat widgets
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '12px';
  iframe.style.boxShadow = 'none !important';
  iframe.style.backgroundColor = 'transparent';
  iframe.style.zIndex = '9999';
  iframe.style.display = 'none';
  iframe.allow = 'microphone';

  // Add null check and toggle functionality
  if (button && iframe) {
    var isOpen = false;

    button.onclick = function() {
      if (isOpen) {
        // Close the chat
        iframe.style.opacity = '0';
        iframe.style.transform = 'translateY(20px)';
        setTimeout(function() {
          iframe.style.display = 'none';
        }, 300);
        isOpen = false;
      } else {
        // Open the chat
        iframe.style.display = 'block';
        iframe.style.opacity = '0';
        iframe.style.transform = 'translateY(20px)';

        // Only set the src when the iframe is first opened
        if (!iframe.src) {
          iframe.src = 'ORIGIN_PLACEHOLDER/embed/CHAT_ID_PLACEHOLDER';
        }

        setTimeout(function() {
          iframe.style.opacity = '1';
          iframe.style.transform = 'translateY(0)';
        }, 10);
        isOpen = true;
      }
    };

    // Add message listener for close events
    window.addEventListener('message', function(event) {
      if (event.data === 'botfusion-chat-close') {
        iframe.style.opacity = '0';
        iframe.style.transform = 'translateY(20px)';
        setTimeout(function() {
          iframe.style.display = 'none';
        }, 300);
        isOpen = false;
      }
    });
  }

  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(iframe);

  // Add responsive styles for mobile
  var style = document.createElement('style');
  style.textContent = '@media (max-width: 480px) { ' +
    '#botfusion-chat-iframe { width: 100%; height: 100%; bottom: 0; right: 0; border-radius: 0; }' +
  '}';
  document.head.appendChild(style);
})();
