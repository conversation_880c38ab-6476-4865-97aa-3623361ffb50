import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId')
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
    }

    // Get the origin for the API
    const origin = process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-mmc59fcqj-tellivisions-projects.vercel.app'

    // Get other parameters from the query string
    const primaryColor = request.nextUrl.searchParams.get('primaryColor') || '#3b82f6';
    const userBubbleColor = request.nextUrl.searchParams.get('userBubbleColor') || '#ffffff';
    const botBubbleColor = request.nextUrl.searchParams.get('botBubbleColor') || '#3b82f6';
    const userTextColor = request.nextUrl.searchParams.get('userTextColor') || '#000000';
    const botTextColor = request.nextUrl.searchParams.get('botTextColor') || '#ffffff';
    const logoUrl = request.nextUrl.searchParams.get('logoUrl') || '';
    const darkMode = request.nextUrl.searchParams.get('darkMode') === 'true';
    const useBlackOutline = request.nextUrl.searchParams.get('useBlackOutline') === 'true';
    const useGradientHeader = request.nextUrl.searchParams.get('useGradientHeader') === 'true';
    const gradientStartColor = request.nextUrl.searchParams.get('gradientStartColor') || '#3b82f6';
    const gradientEndColor = request.nextUrl.searchParams.get('gradientEndColor') || '#9333ea';
    const gradientDirection = request.nextUrl.searchParams.get('gradientDirection') || 'to bottom';
    const greeting = request.nextUrl.searchParams.get('greeting') || 'Hello! How can I help you today?';

    // Generate the HTML for the iframe embed
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BotFusion Chat Widget</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      overflow: hidden;
    }

    /* Button styles */
    .chat-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: ${primaryColor};
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .chat-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    /* Chat window styles */
    .chat-window {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 400px;
      height: 600px;
      border: none;
      border-radius: 12px;
      box-shadow: none !important;
      z-index: 9999;
      display: none;
      transition: transform 0.3s ease, opacity 0.3s ease;
      opacity: 0;
      transform: translateY(20px);
      background-color: transparent;
    }

    .chat-window.visible {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }

    /* SVG icon */
    .chat-icon {
      width: 24px;
      height: 24px;
      display: block;
    }
  </style>
</head>
<body>
  <!-- Chat Button -->
  <button id="chat-button" class="chat-button" aria-label="Open chat">
    <svg class="chat-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
    </svg>
  </button>

  <!-- Chat Window (iframe) -->
  <iframe id="chat-window" class="chat-window" src="${origin}/embed/${chatId}?primaryColor=${encodeURIComponent(primaryColor)}&userBubbleColor=${encodeURIComponent(userBubbleColor)}&botBubbleColor=${encodeURIComponent(botBubbleColor)}&userTextColor=${encodeURIComponent(userTextColor)}&botTextColor=${encodeURIComponent(botTextColor)}&logoUrl=${encodeURIComponent(logoUrl)}&darkMode=${darkMode ? 'true' : 'false'}&useBlackOutline=${useBlackOutline ? 'true' : 'false'}&useGradientHeader=${useGradientHeader ? 'true' : 'false'}&gradientStartColor=${encodeURIComponent(gradientStartColor)}&gradientEndColor=${encodeURIComponent(gradientEndColor)}&gradientDirection=${encodeURIComponent(gradientDirection)}&t=${new Date().getTime()}" title="Chat Window" allow="microphone"></iframe>

  <script>
    // Toggle chat window when button is clicked
    document.getElementById('chat-button').addEventListener('click', function() {
      const chatWindow = document.getElementById('chat-window');
      chatWindow.classList.add('visible');
      this.style.display = 'none';
    });

    // Listen for messages from the chat iframe
    window.addEventListener('message', function(event) {
      if (event.data === 'botfusion-chat-close') {
        const chatButton = document.getElementById('chat-button');
        const chatWindow = document.getElementById('chat-window');

        // Add closing animation
        chatWindow.style.opacity = '0';
        chatWindow.style.transform = 'translateY(20px)';

        setTimeout(function() {
          chatWindow.classList.remove('visible');
          chatButton.style.display = 'flex';
        }, 300);
      }
    });
  </script>
</body>
</html>`;

    // Return the HTML with proper headers
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      }
    })
  } catch (error) {
    console.error('Error serving iframe embed:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
    },
  });
}
