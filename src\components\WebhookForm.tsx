'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { toast } from 'sonner'

export default function WebhookForm({
  onSave,
  initialData = null
}: {
  onSave: (data: {
    name: string
    url: string
    description?: string
  }) => Promise<void>
  initialData?: {
    name: string
    url: string
    description?: string
  } | null
}) {
  const [name, setName] = useState(initialData?.name || '')
  const [nameError, setNameError] = useState<string | null>(null)
  const [url, setUrl] = useState(initialData?.url || '')
  const [description, setDescription] = useState(initialData?.description || '')
  const [isTesting, setIsTesting] = useState(false)

  const handleTest = async () => {
    try {
      // Validate URL first
      if (!url) {
        toast.error('Please enter a webhook URL')
        return
      }

      try {
        new URL(url) // Validate URL format
      } catch {
        toast.error('Please enter a valid URL (include http:// or https://)')
        return
      }

      setIsTesting(true)
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: "Test message from BotFusion webhook test",
          chatId: "webhook-test",
          sessionId: `test-${Date.now()}`
        })
      })

      if (response.ok) {
        toast.success('Webhook test successful')
      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `Webhook test failed with status ${response.status}: ${errorData.message || 'No error details'}`
        )
      }
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : 'Webhook test failed'
      )
    } finally {
      setIsTesting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('WebhookForm: Form submitted with data:', { name, url, description })

    if (!name || !url) {
      console.error('WebhookForm: Missing required fields:', { name: !!name, url: !!url })
      toast.error('Please fill in all required fields')
      return
    }

    try {
      console.log('WebhookForm: Calling onSave with data:', { name, url, description })
      await onSave({ name, url, description })
      console.log('WebhookForm: onSave completed successfully')
    } catch (error) {
      console.error('WebhookForm: Failed to save webhook:', error)

      // Check if it's a name conflict error
      if (error instanceof Error &&
          (error.message.includes('chat interface with this name already exists') ||
           error.message.includes('webhook with this name already exists'))) {
        setNameError(error.message)
      } else {
        toast.error(error instanceof Error ? error.message : 'Failed to save webhook')
      }
    }
  }

  return (
    <Card className="p-4 space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-neon-blue">
            Webhook Name
          </label>
          <Input
            value={name}
            onChange={(e) => {
              setName(e.target.value);
              setNameError(null); // Clear error when user types
            }}
            placeholder="My Webhook"
            required
            className={nameError ? "border-red-500 focus:ring-red-500" : ""}
          />
          {nameError && (
            <div className="text-red-500 text-sm mt-1 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              {nameError}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-neon-blue">
            Webhook URL
          </label>
          <Input
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://your-webhook-endpoint.com"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-neon-blue">
            Description (Optional)
          </label>
          <Input
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe this webhook..."
          />
        </div>

        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleTest}
            disabled={isTesting || !url}
          >
            {isTesting ? 'Testing...' : 'Test Webhook'}
          </Button>
          <Button type="submit" className="flex-1">
            {initialData ? 'Update' : 'Create'} Webhook
          </Button>
        </div>
      </form>
    </Card>
  )
}