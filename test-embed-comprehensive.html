<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .instructions {
            flex: 1;
        }
        .test-area {
            flex: 1;
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 8px;
        }
        h1 {
            color: #333;
        }
        h2 {
            color: #555;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        iframe {
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Comprehensive Iframe Embedding Test</h1>

    <div class="note">
        <strong>Important:</strong> Please open your browser's developer console (F12 or right-click > Inspect > Console)
        to check for any errors during the iframe loading process.
    </div>

    <div class="container">
        <div class="instructions">
            <h2>What to Check For:</h2>
            <ol>
                <li><strong>X-Frame-Options errors</strong> - Look for messages like "Refused to display in a frame because it set 'X-Frame-Options' to 'deny'"</li>
                <li><strong>Content Security Policy (CSP) errors</strong> - Look for messages about CSP blocking scripts or resources</li>
                <li><strong>401 Unauthorized errors</strong> - Check if any API requests are returning 401 status codes</li>
                <li><strong>CORS errors</strong> - Look for messages about cross-origin requests being blocked</li>
                <li><strong>Network requests</strong> - In the Network tab, check the headers of the iframe request and any API requests it makes</li>
            </ol>

            <h2>Expected Behavior:</h2>
            <ul>
                <li>The iframe should load without any security errors</li>
                <li>The chat interface should be visible and functional</li>
                <li>You should be able to send messages and receive responses</li>
            </ul>

            <h2>Iframe URL:</h2>
            <pre>https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/embed/1</pre>
        </div>

        <div class="test-area">
            <h2>Test Iframe:</h2>
            <iframe
                src="https://roo-bot-fusion-kgfs-no8wto7uc-tellivisions-projects.vercel.app/embed/1"
                width="100%"
                height="600"
                frameborder="0"
            ></iframe>
        </div>
    </div>

    <script>
        // Listen for any errors that might occur
        window.addEventListener('error', function(event) {
            console.error('Caught global error:', event.message);
        });

        // Log when the page is fully loaded
        window.addEventListener('load', function() {
            console.log('Page fully loaded, including all iframes and resources');
        });
    </script>
</body>
</html>
