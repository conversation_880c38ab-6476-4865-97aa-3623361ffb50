import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@deepgram/sdk';

const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;

export async function POST(request: NextRequest) {
  console.log('🧪 Test Deepgram TTS endpoint called');

  if (!DEEPGRAM_API_KEY) {
    console.error('❌ DEEPGRAM_API_KEY not configured');
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  try {
    const { text = 'Hello test' } = await request.json();

    console.log('🎵 Testing Deepgram TTS with text:', text);

    // Create Deepgram client
    const deepgram = createClient(DEEPGRAM_API_KEY);

    console.log('🔗 Deepgram client created, making TTS request...');

    // Test the actual Deepgram SDK TTS call
    const response = await deepgram.speak.request(
      { text },
      {
        model: 'aura-2-thalia-en',
        encoding: 'linear16',
        container: 'none',
        sample_rate: 24000,
        smart_format: true
      }
    );

    console.log('📦 Deepgram TTS response received:', {
      responseType: typeof response,
      responseKeys: response ? Object.keys(response) : 'null',
      hasResult: response && 'result' in response,
      hasError: response && 'error' in response
    });

    // Check if response follows { result, error } pattern
    if (response && typeof response === 'object') {
      const { result, error } = response as any;

      console.log('🔍 Response analysis:', {
        resultType: typeof result,
        resultKeys: result ? Object.keys(result) : 'null',
        errorType: typeof error,
        errorValue: error
      });

      if (error) {
        return NextResponse.json({
          success: false,
          error: `Deepgram TTS error: ${error}`,
          responseStructure: { responseType: typeof response, hasResult: !!result, hasError: !!error }
        });
      }

      if (result) {
        // Try to determine the structure of result
        let audioDataInfo = 'No audio data found';

        if (result instanceof ArrayBuffer) {
          audioDataInfo = `ArrayBuffer with ${result.byteLength} bytes`;
        } else if (result instanceof Uint8Array) {
          audioDataInfo = `Uint8Array with ${result.length} bytes`;
        } else if (typeof result === 'object') {
          const props = Object.keys(result);
          audioDataInfo = `Object with properties: ${props.join(', ')}`;

          // Check common audio properties
          for (const prop of ['buffer', 'audio', 'data', 'audioData', 'content']) {
            if (prop in result) {
              const propValue = result[prop];
              audioDataInfo += ` | ${prop}: ${typeof propValue} (${propValue instanceof ArrayBuffer ? propValue.byteLength + ' bytes' : propValue instanceof Uint8Array ? propValue.length + ' bytes' : 'unknown'})`;
            }
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Deepgram TTS request successful',
          responseStructure: {
            responseType: typeof response,
            resultType: typeof result,
            audioDataInfo: audioDataInfo
          }
        });
      }
    }

    // If response doesn't follow expected pattern
    return NextResponse.json({
      success: false,
      error: 'Unexpected response structure',
      responseStructure: {
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : 'null'
      }
    });

  } catch (error: any) {
    console.error('❌ Test Deepgram TTS error:', error);
    return NextResponse.json({
      success: false,
      error: error.message || String(error),
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ message: 'Test Deepgram TTS endpoint - use POST with {"text": "your text"}' });
}
