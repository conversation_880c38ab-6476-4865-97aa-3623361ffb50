<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Embed Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0070f3;
            margin-bottom: 1rem;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        .log {
            background: #f6f8fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            font-size: 0.9rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .error { color: #d32f2f; }
        .warn { color: #f57c00; }
        .info { color: #0288d1; }
    </style>
</head>
<body>
    <h1>BotFusion Embed Test</h1>
    
    <div class="container">
        <div>
            <h2>Console Output</h2>
            <div id="console-output" class="log"></div>
        </div>
    </div>

    <!-- Script to test embedding -->
    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        
        // Store original console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods
        console.log = function() {
            // Call original method
            originalConsole.log.apply(console, arguments);
            
            // Add to our display
            const logElement = document.createElement('div');
            logElement.className = 'log-entry';
            logElement.textContent = Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.error = function() {
            originalConsole.error.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry error';
            logElement.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.warn = function() {
            originalConsole.warn.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry warn';
            logElement.textContent = 'WARNING: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.info = function() {
            originalConsole.info.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log-entry info';
            logElement.textContent = 'INFO: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        // Function to load the script
        function loadScript() {
            console.info('Attempting to load BotFusion script...');
            
            const script = document.createElement('script');
            script.src = 'https://roo-bot-fusion-kgfs.vercel.app/api/embed-script';
            script.dataset.chatId = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';
            script.dataset.baseUrl = 'https://roo-bot-fusion-kgfs.vercel.app';
            script.async = true;
            script.defer = true;
            
            script.onload = function() {
                console.info('Script loaded successfully!');
            };
            
            script.onerror = function(error) {
                console.error('Error loading script:', error);
            };
            
            document.body.appendChild(script);
            console.info('Script element added to the document.');
        }

        // Load the script after a short delay
        setTimeout(loadScript, 1000);

        // Check if script loaded
        setTimeout(function() {
            if (window.BotFusionChat) {
                console.info('BotFusion script initialized successfully!');
            } else {
                console.error('BotFusion script not found after 5 seconds');
            }
        }, 5000);
    </script>
</body>
</html>
