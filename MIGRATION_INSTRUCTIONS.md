# How to Run the Migration

To add the "Powered By" columns to the `chat_interfaces` table, follow these steps:

## Option 1: Using the Supabase Dashboard SQL Editor

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the following SQL:

```sql
-- Add powered_by fields to chat_interfaces table
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS show_powered_by BOOLEAN DEFAULT true;
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS powered_by_text TEXT DEFAULT 'Powered by Bot<PERSON><PERSON>';
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS powered_by_url TEXT DEFAULT 'https://botfusion.io';
```

5. Run the query
6. Verify the columns were added by running:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'chat_interfaces' 
AND column_name IN ('show_powered_by', 'powered_by_text', 'powered_by_url');
```

## Option 2: Using the Migration Script

If you have the Supabase service role key, you can run the migration script:

1. Make sure your `.env.local` file contains:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`

2. Run the migration script:

```bash
node scripts/run_powered_by_migration.js
```

## Verifying the Migration

After running the migration, restart your application. The "Powered By" section should now appear in your chat interfaces and be configurable in the settings.
