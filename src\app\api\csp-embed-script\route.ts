import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId')
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
    }

    // Get the origin for the API
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'

    // Read the CSP embed script template
    const filePath = path.join(process.cwd(), 'public', 'new-csp-embed.js')
    let scriptContent = fs.readFileSync(filePath, 'utf8')

    // Get other parameters from the query string
    const primaryColor = request.nextUrl.searchParams.get('primaryColor') || '#3b82f6';
    const userBubbleColor = request.nextUrl.searchParams.get('userBubbleColor') || '#ffffff';
    const botBubbleColor = request.nextUrl.searchParams.get('botBubbleColor') || '#3b82f6';
    const userTextColor = request.nextUrl.searchParams.get('userTextColor') || '#000000';
    const botTextColor = request.nextUrl.searchParams.get('botTextColor') || '#ffffff';
    const logoUrl = request.nextUrl.searchParams.get('logoUrl') || '';
    const darkMode = request.nextUrl.searchParams.get('darkMode') === 'true';
    const greeting = request.nextUrl.searchParams.get('greeting') || 'Hello! How can I help you today?';

    // Replace placeholders with actual values
    scriptContent = scriptContent.replace('CHAT_ID_PLACEHOLDER', chatId)
    scriptContent = scriptContent.replace('ORIGIN_PLACEHOLDER', origin)

    // Replace other config values if provided
    if (primaryColor) {
      scriptContent = scriptContent.replace('"primaryColor": "#3b82f6"', `"primaryColor": "${primaryColor}"`)
    }
    if (userBubbleColor) {
      scriptContent = scriptContent.replace('"userBubbleColor": "#ffffff"', `"userBubbleColor": "${userBubbleColor}"`)
    }
    if (botBubbleColor) {
      scriptContent = scriptContent.replace('"botBubbleColor": "#3b82f6"', `"botBubbleColor": "${botBubbleColor}"`)
    }
    if (userTextColor) {
      scriptContent = scriptContent.replace('"userTextColor": "#000000"', `"userTextColor": "${userTextColor}"`)
    }
    if (botTextColor) {
      scriptContent = scriptContent.replace('"botTextColor": "#ffffff"', `"botTextColor": "${botTextColor}"`)
    }
    if (logoUrl) {
      scriptContent = scriptContent.replace('"logoUrl": ""', `"logoUrl": "${logoUrl}"`)
    }
    if (darkMode) {
      scriptContent = scriptContent.replace('"darkMode": false', `"darkMode": true`)
    }
    if (greeting) {
      scriptContent = scriptContent.replace('"greeting": "Hello! How can I help you today?"', `"greeting": "${greeting}"`)
    }

    // Return the script with proper headers
    return new NextResponse(scriptContent, {
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin'
      }
    })
  } catch (error) {
    console.error('Error serving CSP embed script:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
    },
  });
}
