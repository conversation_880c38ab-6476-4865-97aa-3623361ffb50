<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Deployed Embed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
        .debug-info pre {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Test Deployed Embed</h1>

    <div class="important">
        <strong>Important:</strong> This page is for testing the deployed embed code. Please open the browser console (F12) to see any errors.
    </div>

    <div class="content">
        <h2>Direct DOM Embed</h2>
        <p>This test uses the direct DOM embed approach with the new deployment:</p>
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Direct DOM Embed -->
    <script>
(function() {
  // Create the CSS styles
  var styles = document.createElement('style');
  styles.textContent = `
    #botfusion-chat-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    #botfusion-chat-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    #botfusion-chat-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 400px;
      height: 600px;
      border: none;
      border-radius: 12px;
      /* box-shadow removed */
      z-index: 9999;
      display: none;
      background-color: white;
      overflow: hidden;
    }

    #botfusion-chat-header {
      background-color: #3b82f6;
      color: white;
      padding: 10px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    #botfusion-chat-title {
      font-weight: bold;
      font-size: 16px;
    }

    #botfusion-chat-close {
      cursor: pointer;
      font-size: 20px;
    }

    #botfusion-chat-iframe {
      width: 100%;
      height: calc(100% - 40px);
      border: none;
    }

    @media (max-width: 480px) {
      #botfusion-chat-container {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
      }
    }
  `;
  document.head.appendChild(styles);

  // Create the button element
  var button = document.createElement('div');
  button.id = 'botfusion-chat-button';
  button.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>';

  // Create the chat container
  var container = document.createElement('div');
  container.id = 'botfusion-chat-container';
  container.innerHTML = `
    <div id="botfusion-chat-header">
      <div id="botfusion-chat-title">BotFusion Chat</div>
      <div id="botfusion-chat-close">×</div>
    </div>
    <iframe id="botfusion-chat-iframe" src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" allow="microphone" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox" loading="lazy" importance="high" referrerpolicy="origin" fetchpriority="high" style="border-radius: 12px;"></iframe>
  `;

  // Add elements to the document
  document.body.appendChild(button);
  document.body.appendChild(container);

  // Get the elements
  var closeButton = document.getElementById('botfusion-chat-close');

  // Add click event to button
  button.onclick = function() {
    container.style.display = 'block';
    this.style.display = 'none';
  };

  // Add click event to close button
  closeButton.onclick = function() {
    container.style.display = 'none';
    button.style.display = 'flex';
  };

  // Add message listener for close events
  window.addEventListener('message', function(event) {
    if (event.data === 'botfusion-chat-close') {
      container.style.display = 'none';
      button.style.display = 'flex';
    }
  });
})();
    </script>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        (function() {
            var oldConsoleLog = console.log;
            var oldConsoleError = console.error;
            var oldConsoleWarn = console.warn;
            var consoleOutput = document.getElementById('console-output');

            function appendToOutput(type, args) {
                var message = Array.from(args).map(arg => {
                    if (typeof arg === 'object') {
                        try {
                            return JSON.stringify(arg);
                        } catch (e) {
                            return String(arg);
                        }
                    }
                    return String(arg);
                }).join(' ');

                var pre = document.createElement('pre');
                pre.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
                pre.textContent = type.toUpperCase() + ': ' + message;
                consoleOutput.appendChild(pre);
            }

            console.log = function() {
                oldConsoleLog.apply(console, arguments);
                appendToOutput('log', arguments);
            };

            console.error = function() {
                oldConsoleError.apply(console, arguments);
                appendToOutput('error', arguments);
            };

            console.warn = function() {
                oldConsoleWarn.apply(console, arguments);
                appendToOutput('warn', arguments);
            };

            // Log any errors
            window.addEventListener('error', function(event) {
                appendToOutput('error', [event.message + ' at ' + event.filename + ':' + event.lineno]);
            });
        })();
    </script>
</body>
</html>
