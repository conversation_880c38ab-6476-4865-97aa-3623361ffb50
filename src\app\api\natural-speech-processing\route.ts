import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuration for natural speech processing
const NATURAL_SPEECH_CONFIG = {
  model: 'gpt-4o-mini', // Default model - can be overridden per request
  maxTokens: 500,
  temperature: 0.7,
  systemPrompt: `You are a natural speech optimizer. Your job is to take text responses and make them sound more natural when spoken aloud by a text-to-speech system.

RULES:
1. Keep the core meaning and information intact
2. Make the text flow naturally when spoken
3. Add appropriate pauses with commas and periods
4. Break up long sentences into shorter, more digestible chunks
5. Use conversational language and contractions where appropriate
6. Add natural speech patterns like "Well," "You know," "Actually," when it makes sense
7. Remove awkward phrasing that sounds robotic
8. Ensure smooth transitions between ideas
9. Keep the response length similar to the original
10. Make it sound like a human is speaking, not reading from a script

Transform the following text to sound more natural when spoken:`,
};

export async function POST(request: NextRequest) {
  try {
    const { text, enableProcessing = true, model } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    // If processing is disabled, return original text
    if (!enableProcessing) {
      return NextResponse.json({ 
        originalText: text,
        processedText: text,
        processed: false 
      });
    }

    if (!process.env.OPENAI_API_KEY) {
      console.warn('OpenAI API key not configured, returning original text');
      return NextResponse.json({ 
        originalText: text,
        processedText: text,
        processed: false,
        warning: 'OpenAI API key not configured'
      });
    }

    const selectedModel = model || NATURAL_SPEECH_CONFIG.model;

    console.log(`🧠 Processing text for natural speech with ${selectedModel}...`);
    console.log('📝 Original text length:', text.length);

    try {
      // Call OpenAI API to process the text for natural speech
      const completion = await openai.chat.completions.create({
        model: selectedModel,
        messages: [
          {
            role: 'system',
            content: NATURAL_SPEECH_CONFIG.systemPrompt
          },
          {
            role: 'user',
            content: text
          }
        ],
        max_tokens: NATURAL_SPEECH_CONFIG.maxTokens,
        temperature: NATURAL_SPEECH_CONFIG.temperature,
      });

      const processedText = completion.choices[0]?.message?.content?.trim();

      if (!processedText) {
        console.warn(`No processed text received from ${selectedModel}, using original`);
        return NextResponse.json({
          originalText: text,
          processedText: text,
          processed: false,
          warning: `No response from ${selectedModel}`
        });
      }

      console.log('✅ Text processed successfully');
      console.log('📝 Processed text length:', processedText.length);
      console.log('🔄 Original vs Processed:');
      console.log('Original:', text.substring(0, 100) + '...');
      console.log('Processed:', processedText.substring(0, 100) + '...');

      return NextResponse.json({
        originalText: text,
        processedText: processedText,
        processed: true,
        tokensUsed: completion.usage?.total_tokens || 0
      });

    } catch (openaiError) {
      console.error('OpenAI API error:', openaiError);
      
      // Return original text if OpenAI fails
      return NextResponse.json({ 
        originalText: text,
        processedText: text,
        processed: false,
        error: 'OpenAI processing failed, using original text'
      });
    }

  } catch (error) {
    console.error('Natural speech processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET(request: NextRequest) {
  const testText = request.nextUrl.searchParams.get('text') || 
    'Hello, this is a test of the natural speech processing system. It should make text sound more natural when spoken by a text-to-speech engine.';

  try {
    // Test the processing with sample text
    const response = await fetch(request.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: testText, enableProcessing: true }),
    });

    const result = await response.json();
    
    return NextResponse.json({
      status: 'Natural Speech Processing API Test',
      testText,
      result,
      apiKeyConfigured: !!process.env.OPENAI_API_KEY
    });

  } catch (error) {
    return NextResponse.json({
      status: 'Test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      apiKeyConfigured: !!process.env.OPENAI_API_KEY
    });
  }
}
