<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Piper TTS SSLIP.IO Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: vertical;
            margin-bottom: 15px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-time {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        .log-info {
            color: #2980b9;
        }
        .log-success {
            color: #27ae60;
        }
        .log-error {
            color: #c0392b;
        }
        audio {
            width: 100%;
            margin-top: 15px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Piper TTS SSLIP.IO Test</h1>
    
    <div class="card">
        <h2>Service Information</h2>
        <p><strong>URL:</strong> <code id="service-url">http://igk40kgg4kwocogkkcss8kkw.************.sslip.io:10200</code></p>
        <button id="check-status">Check Service Status</button>
        <div id="status-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="card">
        <h2>Test Text-to-Speech</h2>
        <textarea id="text-input">This is a test of the Piper TTS service using SSLIP.IO domain.</textarea>
        <button id="convert-btn">Convert to Speech</button>
        
        <div id="result" style="display: none;" class="result">
            <p id="result-message"></p>
            <audio id="audio-player" controls></audio>
            <div style="margin-top: 10px;">
                <button id="download-btn" disabled>Download Audio</button>
                <button id="clear-btn">Clear</button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h2>Log</h2>
        <div id="log" class="log"></div>
        <button id="clear-log" style="margin-top: 10px;">Clear Log</button>
    </div>
    
    <script>
        // Configuration
        const SERVICE_URL = 'http://igk40kgg4kwocogkkcss8kkw.************.sslip.io:10200';
        
        // DOM Elements
        const serviceUrlEl = document.getElementById('service-url');
        const checkStatusBtn = document.getElementById('check-status');
        const statusResultEl = document.getElementById('status-result');
        const textInput = document.getElementById('text-input');
        const convertBtn = document.getElementById('convert-btn');
        const resultDiv = document.getElementById('result');
        const resultMessage = document.getElementById('result-message');
        const audioPlayer = document.getElementById('audio-player');
        const downloadBtn = document.getElementById('download-btn');
        const clearBtn = document.getElementById('clear-btn');
        const logDiv = document.getElementById('log');
        const clearLogBtn = document.getElementById('clear-log');
        
        // Log function
        function log(message, type = 'info') {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="log-time">[${timeString}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Check service status
        async function checkServiceStatus() {
            log('Checking service status...');
            checkStatusBtn.disabled = true;
            checkStatusBtn.innerHTML = '<span class="loading"></span> Checking...';
            statusResultEl.style.display = 'none';
            
            try {
                log(`Testing connection to ${SERVICE_URL}...`);
                const startTime = performance.now();
                
                const response = await fetch(SERVICE_URL, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache',
                    headers: {
                        'Accept': 'text/html'
                    },
                    timeout: 10000
                });
                
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                if (response.ok) {
                    statusResultEl.className = 'result success';
                    statusResultEl.innerHTML = `
                        <p>✅ Service is available!</p>
                        <p>Response time: ${responseTime}ms</p>
                        <p>Status: ${response.status} ${response.statusText}</p>
                    `;
                    log(`Connection successful. Response time: ${responseTime}ms`, 'success');
                } else {
                    statusResultEl.className = 'result error';
                    statusResultEl.innerHTML = `
                        <p>❌ Service returned an error.</p>
                        <p>Status: ${response.status} ${response.statusText}</p>
                    `;
                    log(`Connection failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                statusResultEl.className = 'result error';
                statusResultEl.innerHTML = `
                    <p>❌ Could not connect to service.</p>
                    <p>Error: ${error.message}</p>
                    <p>This could be due to:</p>
                    <ul>
                        <li>Service is not running</li>
                        <li>CORS policy is blocking the request</li>
                        <li>Network connectivity issues</li>
                    </ul>
                `;
                log(`Connection error: ${error.message}`, 'error');
            } finally {
                statusResultEl.style.display = 'block';
                checkStatusBtn.disabled = false;
                checkStatusBtn.textContent = 'Check Service Status';
            }
        }
        
        // Convert text to speech
        async function convertTextToSpeech() {
            const text = textInput.value.trim();
            if (!text) {
                alert('Please enter some text to convert');
                return;
            }
            
            convertBtn.disabled = true;
            convertBtn.innerHTML = '<span class="loading"></span> Converting...';
            resultDiv.style.display = 'none';
            
            try {
                log(`Converting text to speech...`);
                log(`Text: "${text.length > 50 ? text.substring(0, 50) + '...' : text}"`);
                
                const startTime = performance.now();
                
                const response = await fetch(SERVICE_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'text/plain'
                    },
                    body: text
                });
                
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const audioUrl = URL.createObjectURL(blob);
                    
                    audioPlayer.src = audioUrl;
                    resultDiv.className = 'result success';
                    resultMessage.innerHTML = `
                        <p>✅ Conversion successful!</p>
                        <p>Response time: ${responseTime}ms</p>
                        <p>Audio size: ${(blob.size / 1024).toFixed(2)} KB</p>
                    `;
                    
                    // Enable download button
                    downloadBtn.disabled = false;
                    downloadBtn.onclick = () => {
                        const a = document.createElement('a');
                        a.href = audioUrl;
                        a.download = 'piper-tts-output.wav';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    };
                    
                    log(`Conversion successful. Audio size: ${(blob.size / 1024).toFixed(2)} KB, Response time: ${responseTime}ms`, 'success');
                } else {
                    resultDiv.className = 'result error';
                    resultMessage.innerHTML = `
                        <p>❌ Conversion failed.</p>
                        <p>Status: ${response.status} ${response.statusText}</p>
                    `;
                    log(`Conversion failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultMessage.innerHTML = `
                    <p>❌ Conversion error.</p>
                    <p>Error: ${error.message}</p>
                `;
                log(`Conversion error: ${error.message}`, 'error');
            } finally {
                resultDiv.style.display = 'block';
                convertBtn.disabled = false;
                convertBtn.textContent = 'Convert to Speech';
            }
        }
        
        // Event listeners
        checkStatusBtn.addEventListener('click', checkServiceStatus);
        convertBtn.addEventListener('click', convertTextToSpeech);
        clearBtn.addEventListener('click', () => {
            resultDiv.style.display = 'none';
            audioPlayer.src = '';
            downloadBtn.disabled = true;
        });
        clearLogBtn.addEventListener('click', () => {
            logDiv.innerHTML = '';
            log('Log cleared', 'info');
        });
        
        // Initialize
        log('Page loaded. Ready to test Piper TTS service.', 'info');
        log(`Service URL: ${SERVICE_URL}`, 'info');
        
        // Add fetch timeout polyfill
        (function(self) {
            if (self.fetch) {
                const originalFetch = self.fetch;
                self.fetch = function(resource, options) {
                    if (options && options.timeout) {
                        const controller = new AbortController();
                        const signal = controller.signal;
                        
                        const timeout = setTimeout(() => {
                            controller.abort();
                        }, options.timeout);
                        
                        options.signal = signal;
                        
                        return originalFetch(resource, options)
                            .then(response => {
                                clearTimeout(timeout);
                                return response;
                            })
                            .catch(error => {
                                clearTimeout(timeout);
                                throw error;
                            });
                    } else {
                        return originalFetch(resource, options);
                    }
                };
            }
        })(window);
    </script>
</body>
</html>