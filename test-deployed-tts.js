// Test script for deployed TTS API
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Deployed URL
const DEPLOYED_URL = 'https://roo-bot-fusion-kgfs-jcz7kcx7c-tellivisions-projects.vercel.app';

// Hardcoded token for testing - this is the same as in .env.local
const API_ACCESS_TOKEN = 'tts-auth-token-12345';

async function testDeployedTTS() {
  try {
    console.log('Testing deployed TTS API...');

    // Skip token fetch and use hardcoded token
    console.log('Using hardcoded API_ACCESS_TOKEN for testing');
    const token = API_ACCESS_TOKEN;

    console.log('Authentication token set successfully');

    // Step 2: Call the TTS API
    console.log('Step 2: Calling TTS API...');

    // Test text
    const text = 'This is a test of the deployed OpenAI Text-to-Speech API. If you can hear this message, the API is working correctly.';

    // Create a timestamp to prevent caching
    const timestamp = Date.now();

    // Create the URL with query parameters - use fallback-tts endpoint
    const ttsUrl = new URL('/api/fallback-tts', DEPLOYED_URL);
    ttsUrl.searchParams.append('text', text);
    ttsUrl.searchParams.append('voice', 'nova');
    ttsUrl.searchParams.append('t', timestamp.toString());

    console.log(`Calling TTS API with URL: ${ttsUrl.toString()}`);

    // Make the request - direct-tts doesn't require authentication
    const ttsResponse = await fetch(ttsUrl.toString(), {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });

    // Clone the response for multiple reads
    const ttsResponseClone = ttsResponse.clone();

    if (!ttsResponse.ok) {
      let errorMessage = `API request failed: ${ttsResponse.status} ${ttsResponse.statusText}`;

      try {
        const errorText = await ttsResponseClone.text();
        console.error('TTS Error response:', errorText);

        if (errorText) {
          // Check if it's HTML
          if (errorText.includes('<!doctype') || errorText.includes('<html')) {
            errorMessage = `API request failed: Received HTML response instead of JSON. This might indicate a server-side error or redirection.`;
            console.error('HTML Response received:');
            console.error(errorText.substring(0, 500) + '...');
          } else {
            try {
              const errorData = JSON.parse(errorText);
              errorMessage = errorData.message || errorData.error || errorMessage;
            } catch (jsonError) {
              errorMessage = errorText;
            }
          }
        }
      } catch (e) {
        console.error('Error reading TTS response body:', e);
      }

      throw new Error(errorMessage);
    }

    // Get audio data as buffer
    const audioBuffer = await ttsResponse.arrayBuffer();

    if (audioBuffer.byteLength === 0) {
      throw new Error('Received empty audio data from the server');
    }

    console.log(`Received audio data: ${audioBuffer.byteLength} bytes`);

    // Save the audio file
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const outputPath = path.join(__dirname, 'test-deployed-tts-output.mp3');
    fs.writeFileSync(outputPath, Buffer.from(audioBuffer));

    console.log(`Audio file saved to: ${outputPath}`);
    console.log('Test completed successfully!');

  } catch (error) {
    console.error('Error testing deployed TTS API:');
    console.error(error);
  }
}

// Run the test
testDeployedTTS();
