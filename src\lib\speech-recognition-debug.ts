/**
 * Utility functions for debugging speech recognition issues
 */

// Define the debug level
export enum DebugLevel {
  NONE = 0,
  ERROR = 1,
  WARN = 2,
  INFO = 3,
  DEBUG = 4,
  VERBOSE = 5
}

// Global debug level setting
let globalDebugLevel = DebugLevel.INFO;

// Set the global debug level
export function setDebugLevel(level: DebugLevel) {
  globalDebugLevel = level;
}

// Get the current debug level
export function getDebugLevel(): DebugLevel {
  return globalDebugLevel;
}

// Debug logger function
export function debugLog(level: DebugLevel, message: string, ...args: any[]) {
  if (level <= globalDebugLevel) {
    const timestamp = new Date().toISOString();
    const prefix = getLogPrefix(level);
    
    if (args.length > 0) {
      console.log(`[${timestamp}] ${prefix} ${message}`, ...args);
    } else {
      console.log(`[${timestamp}] ${prefix} ${message}`);
    }
  }
}

// Get the log prefix based on the debug level
function getLogPrefix(level: DebugLevel): string {
  switch (level) {
    case DebugLevel.ERROR:
      return '[ERROR]';
    case DebugLevel.WARN:
      return '[WARN]';
    case DebugLevel.INFO:
      return '[INFO]';
    case DebugLevel.DEBUG:
      return '[DEBUG]';
    case DebugLevel.VERBOSE:
      return '[VERBOSE]';
    default:
      return '';
  }
}

// Convenience functions for different log levels
export const logError = (message: string, ...args: any[]) => 
  debugLog(DebugLevel.ERROR, message, ...args);

export const logWarn = (message: string, ...args: any[]) => 
  debugLog(DebugLevel.WARN, message, ...args);

export const logInfo = (message: string, ...args: any[]) => 
  debugLog(DebugLevel.INFO, message, ...args);

export const logDebug = (message: string, ...args: any[]) => 
  debugLog(DebugLevel.DEBUG, message, ...args);

export const logVerbose = (message: string, ...args: any[]) => 
  debugLog(DebugLevel.VERBOSE, message, ...args);

// Function to check browser capabilities
export function checkBrowserCapabilities() {
  const capabilities = {
    speechRecognition: !!(
      (window as any).SpeechRecognition || 
      (window as any).webkitSpeechRecognition || 
      (window as any).mozSpeechRecognition || 
      (window as any).msSpeechRecognition
    ),
    mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    audioContext: !!(window as any).AudioContext || !!(window as any).webkitAudioContext,
    permissions: !!(navigator.permissions),
    secureContext: window.isSecureContext,
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    isIframe: window.self !== window.top,
    isHttps: window.location.protocol === 'https:',
    referrer: document.referrer
  };
  
  logInfo('Browser capabilities:', capabilities);
  return capabilities;
}

// Function to check microphone permissions
export async function checkMicrophonePermissions() {
  try {
    // Check if the Permissions API is available
    if (navigator.permissions && navigator.permissions.query) {
      try {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        logInfo('Microphone permission status:', permissionStatus.state);
        return permissionStatus.state;
      } catch (error) {
        logWarn('Error querying microphone permission:', error);
      }
    }
    
    // Fallback to getUserMedia
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // Stop all tracks
      stream.getTracks().forEach(track => track.stop());
      logInfo('Microphone permission granted via getUserMedia');
      return 'granted';
    } catch (error) {
      logError('Microphone permission denied via getUserMedia:', error);
      return 'denied';
    }
  } catch (error) {
    logError('Error checking microphone permissions:', error);
    return 'error';
  }
}

// Function to monitor speech recognition events
export function monitorSpeechRecognition(recognition: any) {
  if (!recognition) {
    logError('Cannot monitor speech recognition: recognition object is null');
    return;
  }
  
  // Store original event handlers
  const originalHandlers: Record<string, any> = {};
  
  // List of events to monitor
  const events = [
    'audiostart',
    'audioend',
    'end',
    'error',
    'nomatch',
    'result',
    'soundstart',
    'soundend',
    'speechstart',
    'speechend',
    'start'
  ];
  
  // Wrap each event handler
  events.forEach(event => {
    originalHandlers[event] = recognition[`on${event}`];
    
    recognition[`on${event}`] = function(e: any) {
      logDebug(`SpeechRecognition event: ${event}`, e);
      
      // Call the original handler if it exists
      if (typeof originalHandlers[event] === 'function') {
        originalHandlers[event].call(this, e);
      }
    };
  });
  
  // Special handling for error events
  const originalErrorHandler = recognition.onerror;
  recognition.onerror = function(e: any) {
    logError(`SpeechRecognition error: ${e.error}`, e);
    
    // Call the original error handler if it exists
    if (typeof originalErrorHandler === 'function') {
      originalErrorHandler.call(this, e);
    }
  };
  
  logInfo('Speech recognition monitoring enabled');
  
  // Return a function to restore original handlers
  return function cleanup() {
    events.forEach(event => {
      recognition[`on${event}`] = originalHandlers[event];
    });
    logInfo('Speech recognition monitoring disabled');
  };
}

// Function to test audio output
export async function testAudioOutput() {
  try {
    // Create an audio context
    const AudioContext = (window as any).AudioContext || (window as any).webkitAudioContext;
    if (!AudioContext) {
      logError('AudioContext not supported');
      return false;
    }
    
    const audioContext = new AudioContext();
    
    // Create an oscillator
    const oscillator = audioContext.createOscillator();
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
    
    // Create a gain node to control volume
    const gainNode = audioContext.createGain();
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime); // Low volume
    
    // Connect the oscillator to the gain node and the gain node to the destination
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Start the oscillator
    oscillator.start();
    
    // Stop the oscillator after 500ms
    setTimeout(() => {
      oscillator.stop();
      audioContext.close();
      logInfo('Audio output test completed');
    }, 500);
    
    logInfo('Audio output test started');
    return true;
  } catch (error) {
    logError('Error testing audio output:', error);
    return false;
  }
}

// Export a debug object with all functions
export const SpeechRecognitionDebug = {
  setDebugLevel,
  getDebugLevel,
  debugLog,
  logError,
  logWarn,
  logInfo,
  logDebug,
  logVerbose,
  checkBrowserCapabilities,
  checkMicrophonePermissions,
  monitorSpeechRecognition,
  testAudioOutput,
  DebugLevel
};
