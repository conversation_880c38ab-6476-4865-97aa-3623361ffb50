'use client';

import { useState, useRef } from 'react';
import StreamingTTS from '@/components/StreamingTTS';

export default function ComprehensiveTTSTest() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');
  const streamingTTSRef = useRef<any>(null);

  const testCases = [
    {
      name: 'Short Text Test',
      text: 'Hello, this is a short test.',
      voiceModel: 'aura-asteria-en',
      expectedChunks: 1
    },
    {
      name: 'Medium Text Test',
      text: 'This is a medium length text that should be split into multiple chunks. It contains several sentences to test the chunking logic properly.',
      voiceModel: 'aura-asteria-en',
      expectedChunks: 2
    },
    {
      name: 'Long Text Test',
      text: 'This is a much longer text that will definitely be split into multiple chunks. It contains many sentences and should test the streaming functionality thoroughly. The system should process each chunk sequentially and deliver audio as it becomes available. This provides a better user experience compared to waiting for the entire text to be processed.',
      voiceModel: 'aura-asteria-en',
      expectedChunks: 3
    },
    {
      name: 'Different Voice Model Test',
      text: 'Testing with a different voice model to ensure compatibility.',
      voiceModel: 'aura-2-thalia-en',
      expectedChunks: 1
    },
    {
      name: 'Special Characters Test',
      text: 'Testing with special characters: Hello! How are you? I\'m fine, thanks. What about you?',
      voiceModel: 'aura-asteria-en',
      expectedChunks: 2
    }
  ];

  const runTest = async (testCase: any) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let audioChunksReceived = 0;
      let errors: string[] = [];
      let completed = false;

      const testResult = {
        name: testCase.name,
        text: testCase.text,
        voiceModel: testCase.voiceModel,
        expectedChunks: testCase.expectedChunks,
        actualChunks: 0,
        audioChunksReceived: 0,
        duration: 0,
        success: false,
        errors: [],
        startTime: new Date().toISOString()
      };

      // Mock the streaming TTS component behavior
      const mockHandleAudio = (audioData: ArrayBuffer, metadata?: any) => {
        audioChunksReceived++;
        testResult.audioChunksReceived = audioChunksReceived;
        if (metadata?.chunk) {
          testResult.actualChunks = Math.max(testResult.actualChunks, metadata.chunk);
        }
        console.log(`Test ${testCase.name}: Received audio chunk ${audioChunksReceived}`);
      };

      const mockHandleError = (error: string) => {
        errors.push(error);
        testResult.errors = [...errors];
        console.error(`Test ${testCase.name}: Error - ${error}`);
      };

      const mockHandleComplete = () => {
        if (!completed) {
          completed = true;
          testResult.duration = Date.now() - startTime;
          testResult.success = errors.length === 0 && audioChunksReceived > 0;
          console.log(`Test ${testCase.name}: Completed in ${testResult.duration}ms`);
          resolve(testResult);
        }
      };

      // Start the test by making a direct API call
      fetch('/api/deepgram-tts-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testCase.text,
          voiceModel: testCase.voiceModel
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('No response body reader available');
        }

        const decoder = new TextDecoder();
        let buffer = ''; // Buffer for incomplete SSE data

        const readStream = () => {
          reader.read().then(({ done, value }) => {
            if (done) {
              if (!completed) {
                mockHandleComplete();
              }
              return;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // Process complete lines from buffer
            const lines = buffer.split('\n');
            // Keep the last line in buffer if it doesn't end with \n (incomplete)
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonData = line.slice(6).trim();
                  if (jsonData) {
                    const data = JSON.parse(jsonData);

                    if (data.type === 'audio') {
                      const audioBuffer = Uint8Array.from(atob(data.data), c => c.charCodeAt(0)).buffer;
                      mockHandleAudio(audioBuffer, data);
                    } else if (data.type === 'error') {
                      mockHandleError(data.message);
                    } else if (data.type === 'close') {
                      mockHandleComplete();
                      return;
                    }
                  }
                } catch (parseError) {
                  console.error('Error parsing SSE data:', parseError);
                  console.error('Problematic line:', line.slice(0, 200) + '...');
                }
              }
            }

            readStream();
          }).catch(error => {
            mockHandleError(`Stream reading error: ${error.message}`);
            mockHandleComplete();
          });
        };

        readStream();
      })
      .catch(error => {
        mockHandleError(`Fetch error: ${error.message}`);
        mockHandleComplete();
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!completed) {
          mockHandleError('Test timeout after 30 seconds');
          mockHandleComplete();
        }
      }, 30000);
    });
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    for (const testCase of testCases) {
      setCurrentTest(testCase.name);
      console.log(`Starting test: ${testCase.name}`);
      
      const result = await runTest(testCase);
      setTestResults(prev => [...prev, result]);
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    setCurrentTest('');
    setIsRunning(false);
  };

  const getTestStatusIcon = (result: any) => {
    if (result.success) return '✅';
    if (result.errors.length > 0) return '❌';
    return '⏳';
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          Comprehensive Streaming TTS Test Suite
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Test Controls</h2>
            <button
              onClick={runAllTests}
              disabled={isRunning}
              className={`px-6 py-2 rounded-lg font-medium ${
                isRunning 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>
          
          {currentTest && (
            <div className="bg-blue-50 border border-blue-200 rounded p-3">
              <p className="text-blue-800">Currently running: <strong>{currentTest}</strong></p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          
          {testResults.length === 0 && !isRunning && (
            <p className="text-gray-500">No tests run yet. Click "Run All Tests" to start.</p>
          )}
          
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium flex items-center gap-2">
                    {getTestStatusIcon(result)}
                    {result.name}
                  </h3>
                  <span className="text-sm text-gray-500">
                    {result.duration}ms
                  </span>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Voice Model:</span>
                    <p className="text-gray-600">{result.voiceModel}</p>
                  </div>
                  <div>
                    <span className="font-medium">Expected Chunks:</span>
                    <p className="text-gray-600">{result.expectedChunks}</p>
                  </div>
                  <div>
                    <span className="font-medium">Actual Chunks:</span>
                    <p className="text-gray-600">{result.actualChunks}</p>
                  </div>
                  <div>
                    <span className="font-medium">Audio Received:</span>
                    <p className="text-gray-600">{result.audioChunksReceived}</p>
                  </div>
                </div>
                
                <div className="mt-2">
                  <span className="font-medium">Test Text:</span>
                  <p className="text-gray-600 text-sm">{result.text}</p>
                </div>
                
                {result.errors.length > 0 && (
                  <div className="mt-2">
                    <span className="font-medium text-red-600">Errors:</span>
                    <ul className="text-red-600 text-sm list-disc list-inside">
                      {result.errors.map((error: string, i: number) => (
                        <li key={i}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
