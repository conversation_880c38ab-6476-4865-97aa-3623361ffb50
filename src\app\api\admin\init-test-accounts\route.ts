import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { UserTier } from '@/lib/tiers'

interface TestAccount {
  email: string
  password: string
  tier: UserTier
  description: string
}

const TEST_ACCOUNTS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'testpass123',
    tier: 'free',
    description: 'Free tier test account - 1 chat interface limit'
  },
  {
    email: '<EMAIL>',
    password: 'testpass123',
    tier: 'standard',
    description: 'Standard tier test account - unlimited interfaces, 3 voice models'
  },
  {
    email: '<EMAIL>',
    password: 'testpass123',
    tier: 'pro',
    description: 'Pro tier test account - all features, 6 voice models'
  }
]

export async function POST(request: NextRequest) {
  try {
    console.log('Initializing test accounts...')

    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    const results = []

    // Create test accounts
    for (const account of TEST_ACCOUNTS) {
      try {
        console.log(`Creating account: ${account.email}`)

        // Check if user already exists
        const { data: existingUsers } = await supabase.auth.admin.listUsers()
        const existingUser = existingUsers.users.find(u => u.email === account.email)

        let userId: string

        if (existingUser) {
          console.log(`User ${account.email} already exists`)
          userId = existingUser.id
        } else {
          // Create new user
          const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
            email: account.email,
            password: account.password,
            email_confirm: true
          })

          if (createError) {
            console.error(`Error creating user ${account.email}:`, createError)
            results.push({
              email: account.email,
              success: false,
              error: createError.message
            })
            continue
          }

          userId = newUser.user.id
          console.log(`Created user ${account.email} with ID: ${userId}`)
        }

        // Create or update user profile
        const now = new Date().toISOString()
        const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()

        const { error: profileError } = await supabase
          .from('user_profiles')
          .upsert({
            id: userId,
            tier: account.tier,
            subscription_status: 'active',
            subscription_start_date: now,
            subscription_end_date: account.tier === 'free' ? null : oneYearFromNow,
            created_at: now,
            updated_at: now
          })

        if (profileError) {
          console.error(`Error creating profile for ${account.email}:`, profileError)
          results.push({
            email: account.email,
            success: false,
            error: profileError.message
          })
        } else {
          console.log(`Created profile for ${account.email} with ${account.tier} tier`)
          results.push({
            email: account.email,
            tier: account.tier,
            description: account.description,
            success: true,
            userId
          })
        }
      } catch (error) {
        console.error(`Error processing ${account.email}:`, error)
        results.push({
          email: account.email,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Also ensure admin account (<EMAIL>) is set to Pro
    try {
      console.log('Setting admin account to Pro tier...')
      
      const { data: adminUsers } = await supabase.auth.admin.listUsers()
      const adminUser = adminUsers.users.find(u => u.email === '<EMAIL>')

      if (adminUser) {
        const now = new Date().toISOString()
        const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()

        const { error: adminProfileError } = await supabase
          .from('user_profiles')
          .upsert({
            id: adminUser.id,
            tier: 'pro',
            subscription_status: 'active',
            subscription_start_date: now,
            subscription_end_date: oneYearFromNow,
            created_at: now,
            updated_at: now
          })

        if (adminProfileError) {
          console.error('Error setting admin tier:', adminProfileError)
        } else {
          console.log('Admin account set to Pro tier')
          results.push({
            email: '<EMAIL>',
            tier: 'pro',
            description: 'Admin account - always Pro tier',
            success: true,
            userId: adminUser.id
          })
        }
      } else {
        console.log('Admin user not found')
        results.push({
          email: '<EMAIL>',
          success: false,
          error: 'Admin user not found in auth.users'
        })
      }
    } catch (error) {
      console.error('Error setting admin tier:', error)
      results.push({
        email: '<EMAIL>',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    return NextResponse.json({
      message: 'Test account initialization completed',
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    })
  } catch (error) {
    console.error('Error in init-test-accounts:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check test accounts status
export async function GET() {
  try {
    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    const accounts = []

    for (const email of testEmails) {
      const { data: users } = await supabase.auth.admin.listUsers()
      const user = users.users.find(u => u.email === email)

      if (user) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        accounts.push({
          email,
          userId: user.id,
          tier: profile?.tier || 'unknown',
          subscriptionStatus: profile?.subscription_status || 'unknown',
          hasProfile: !!profile,
          createdAt: user.created_at
        })
      } else {
        accounts.push({
          email,
          exists: false
        })
      }
    }

    return NextResponse.json({
      message: 'Test accounts status',
      accounts
    })
  } catch (error) {
    console.error('Error checking test accounts:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
