import { getCurrentUser } from '@/lib/supabase/auth'
import Sidebar from '@/app/dashboard/Sidebar'
import UserProfile from '@/components/UserProfile'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser()

  return (
    <div className="flex min-h-screen tech-grid-bg text-glow-blue">
      {/* Sidebar with user profile */}
      <Sidebar user={user} />

      {/* Main content */}
      <div className="flex-1">
        {/* Page content */}
        <main className="p-4">{children}</main>
      </div>
    </div>
  )
}