-- Add user_id column to chat_interfaces table
ALTER TABLE chat_interfaces
ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Update RLS policies to use user_id
DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON chat_interfaces;
DROP POLICY IF EXISTS "Enable update access for authenticated users" ON chat_interfaces;

CREATE POLICY "Enable insert access for authenticated users" ON chat_interfaces
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update access for authenticated users" ON chat_interfaces
  FOR UPDATE USING (auth.uid() = user_id);