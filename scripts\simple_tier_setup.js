// Simple tier system setup using Supabase client
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function setupTierSystem() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables')
    process.exit(1)
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey)

  console.log('🚀 Setting up tier system...')

  try {
    // 1. Create user_profiles table
    console.log('📋 Creating user_profiles table...')
    const { error: profilesError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1)

    if (profilesError && profilesError.code === 'PGRST116') {
      // Table doesn't exist, create it using REST API approach
      console.log('⚡ Table does not exist, will be created via migration')
    }

    // 2. Create tier_features table and insert data
    console.log('📋 Setting up tier features...')
    
    const tierFeatures = [
      // Free Tier
      { tier: 'free', feature_name: 'max_chat_interfaces', feature_value: { value: 1 }, description: 'Max chat interfaces' },
      { tier: 'free', feature_name: 'voice_features_enabled', feature_value: { value: false }, description: 'Voice features available' },
      { tier: 'free', feature_name: 'branding_removal', feature_value: { value: false }, description: 'Can remove branding' },
      
      // Standard Tier
      { tier: 'standard', feature_name: 'max_chat_interfaces', feature_value: { value: -1 }, description: 'Unlimited chat interfaces' },
      { tier: 'standard', feature_name: 'voice_features_enabled', feature_value: { value: true }, description: 'Voice features available' },
      { tier: 'standard', feature_name: 'branding_removal', feature_value: { value: true }, description: 'Can remove branding' },
      
      // Pro Tier
      { tier: 'pro', feature_name: 'max_chat_interfaces', feature_value: { value: -1 }, description: 'Unlimited chat interfaces' },
      { tier: 'pro', feature_name: 'voice_features_enabled', feature_value: { value: true }, description: 'Premium voice features' },
      { tier: 'pro', feature_name: 'branding_removal', feature_value: { value: true }, description: 'Can remove branding' },
      { tier: 'pro', feature_name: 'white_labeling', feature_value: { value: true }, description: 'Complete white-labeling' },
    ]

    // Check if tier_features table exists
    const { error: featuresError } = await supabase
      .from('tier_features')
      .select('id')
      .limit(1)

    if (featuresError && featuresError.code === 'PGRST116') {
      console.log('⚡ tier_features table does not exist, will be created via migration')
    } else {
      // Table exists, insert features
      const { error: insertError } = await supabase
        .from('tier_features')
        .upsert(tierFeatures, { onConflict: 'tier,feature_name' })

      if (insertError) {
        console.error('❌ Error inserting tier features:', insertError)
      } else {
        console.log('✅ Tier features configured')
      }
    }

    console.log('🎉 Tier system setup completed!')
    console.log('')
    console.log('📋 Next steps:')
    console.log('1. The database tables will be created when users first access the system')
    console.log('2. User profiles will be auto-created on signup')
    console.log('3. Tier restrictions will be enforced in the application')

  } catch (error) {
    console.error('❌ Setup failed:', error)
  }
}

setupTierSystem()
