'use client'

import { useState, useEffect, useRef } from 'react'
import { useParams } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import UnifiedSpeechToText from '../../../components/UnifiedSpeechToText'
import SpeechToTextProvider from '../../../contexts/SpeechToTextContext'
import TextToSpeech from '../../../components/TextToSpeech'
import { STTProvider } from '../../../types/deepgram-stt'

interface ChatInterface {
  id: string
  name: string
  description: string
  webhookurl: string
  primary_color?: string
  use_gradient_header?: boolean
  gradient_start_color?: string
  gradient_end_color?: string
  gradient_direction?: string
  user_bubble_color?: string
  bot_bubble_color?: string
  user_text_color?: string
  bot_text_color?: string
  logo_url?: string
  dark_mode?: boolean
  show_name?: boolean
  use_black_outline?: boolean
  show_powered_by?: boolean
  powered_by_text?: string
  powered_by_url?: string
  powered_by_text_color?: string
  welcome_message?: string
  enable_natural_speech?: boolean
  natural_speech_model?: string
  natural_speech_temperature?: number
  natural_speech_max_tokens?: number
  voice_model?: string
}

interface Message {
  id: string
  content: string
  sender: 'user' | 'bot'
  timestamp: string
}

export default function WidgetPage() {
  const { id } = useParams<{ id: string }>()
  const [chatInterface, setChatInterface] = useState<ChatInterface | null>(null)
  const [loading, setLoading] = useState(true)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false)
  const [messageWasVoiceInput, setMessageWasVoiceInput] = useState(false) // Track if current message was created via voice
  const [lastBotMessage, setLastBotMessage] = useState('')

  useEffect(() => {
    // Generate a unique session ID for this chat session if one doesn't exist
    if (typeof window !== 'undefined' && !localStorage.getItem('chatSessionId')) {
      localStorage.setItem('chatSessionId', Date.now().toString())
    }
  }, [])

  useEffect(() => {
    const fetchChatInterface = async () => {
      try {
        console.log('Fetching chat interface data for ID:', id);

        // Add a cache-busting parameter and specify no-cors mode to avoid CORS issues
        const response = await fetch(`/api/chat/${id}?t=${new Date().getTime()}&embed=true`, {
          method: 'GET',
          credentials: 'omit', // Don't send credentials
          cache: 'no-store', // Don't use cache
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Embed-Request': 'true', // Add this header to identify embed requests
            'Authorization': 'Bearer embed' // Add a special authorization header for embed requests
          }
        })

        if (!response.ok) {
          console.error('Failed to load chat interface:', response.status, response.statusText);
          throw new Error(`Failed to load chat interface: ${response.status} ${response.statusText}`)
        }

        console.log('Chat interface API response status:', response.status);
        const data = await response.json()
        console.log('Chat interface data received:', data);

        // Check for URL parameters that might override the chat interface settings
        if (typeof window !== 'undefined') {
          const urlParams = new URLSearchParams(window.location.search);

          // Apply URL parameters if they exist
          if (urlParams.has('primaryColor')) {
            data.primary_color = decodeURIComponent(urlParams.get('primaryColor') || '');
          }

          if (urlParams.has('userBubbleColor')) {
            data.user_bubble_color = decodeURIComponent(urlParams.get('userBubbleColor') || '');
          }

          if (urlParams.has('botBubbleColor')) {
            data.bot_bubble_color = decodeURIComponent(urlParams.get('botBubbleColor') || '');
          }

          if (urlParams.has('userTextColor')) {
            data.user_text_color = decodeURIComponent(urlParams.get('userTextColor') || '');
          }

          if (urlParams.has('botTextColor')) {
            data.bot_text_color = decodeURIComponent(urlParams.get('botTextColor') || '');
          }

          if (urlParams.has('logoUrl')) {
            data.logo_url = decodeURIComponent(urlParams.get('logoUrl') || '');
          }

          if (urlParams.has('darkMode')) {
            data.dark_mode = urlParams.get('darkMode') === 'true';
          }

          if (urlParams.has('showName')) {
            data.show_name = urlParams.get('showName') === 'true';
          }

          if (urlParams.has('showPoweredBy')) {
            data.show_powered_by = urlParams.get('showPoweredBy') === 'true';
          }

          if (urlParams.has('poweredByText')) {
            data.powered_by_text = decodeURIComponent(urlParams.get('poweredByText') || '');
          }

          if (urlParams.has('poweredByUrl')) {
            data.powered_by_url = decodeURIComponent(urlParams.get('poweredByUrl') || '');
          }

          if (urlParams.has('poweredByTextColor')) {
            data.powered_by_text_color = decodeURIComponent(urlParams.get('poweredByTextColor') || '');
          }

          // Add gradient header parameters
          if (urlParams.has('useGradientHeader')) {
            data.use_gradient_header = urlParams.get('useGradientHeader') === 'true';
          }

          if (urlParams.has('gradientStartColor')) {
            data.gradient_start_color = decodeURIComponent(urlParams.get('gradientStartColor') || '');
          }

          if (urlParams.has('gradientEndColor')) {
            data.gradient_end_color = decodeURIComponent(urlParams.get('gradientEndColor') || '');
          }

          if (urlParams.has('gradientDirection')) {
            data.gradient_direction = decodeURIComponent(urlParams.get('gradientDirection') || '');
          }
        }

        setChatInterface(data)

        // If there's a welcome message, add it to the messages
        if (data.welcome_message) {
          setMessages([{
            id: 'welcome-message',
            content: data.welcome_message,
            sender: 'bot',
            timestamp: new Date().toISOString()
          }])
        }
      } catch (error) {
        console.error('Error fetching chat interface:', error)
        setError('Failed to load chat interface. Please try again later.')
        toast.error('Failed to load chat interface')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      console.log('Starting to fetch chat interface with ID:', id)
      fetchChatInterface()
    } else {
      console.error('No chat ID provided')
      setLoading(false)
      setError('No chat ID provided')
    }
  }, [id])

  // Track the last message sent to prevent duplicates
  const lastSentMessageRef = useRef<string>('');
  const lastSentTimeRef = useRef<number>(0);

  // Ref for auto-scrolling to bottom
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const sendMessage = async (voiceTranscript?: string) => {
    const messageToSend = voiceTranscript || inputValue;
    console.log('[DEBUG] Widget sendMessage called, inputValue state:', `"${inputValue}"`, 'voiceTranscript:', `"${voiceTranscript || 'none'}"`, 'using:', `"${messageToSend}"`);
    if (!messageToSend.trim() || !chatInterface?.webhookurl) {
      console.log('[DEBUG] Widget sendMessage early return - message empty or no chatInterface');
      return;
    }

    // Check for duplicate messages sent within a short time window (1.5 seconds)
    const currentTime = Date.now();
    const timeSinceLastMessage = currentTime - lastSentTimeRef.current;

    if (messageToSend.trim() === lastSentMessageRef.current && timeSinceLastMessage < 1500) {
      console.log('Preventing duplicate message send:', messageToSend.trim());
      console.log(`Time since last message: ${timeSinceLastMessage}ms`);
      return;
    }

    // Update last sent message tracking
    lastSentMessageRef.current = messageToSend.trim();
    lastSentTimeRef.current = currentTime;

    const userMessage = {
      id: currentTime.toString(),
      content: messageToSend.trim(),
      sender: 'user' as const,
      timestamp: new Date().toISOString()
    }

    // Add user message to the chat
    setMessages(prev => [...prev, userMessage])
    // Only clear input state if this wasn't a voice transcript
    if (!voiceTranscript) {
      setInputValue('')
    }
    setSending(true)

    try {
      console.log('Sending message to webhook via proxy:', chatInterface.webhookurl);

      // Prepare the request body for our proxy
      const requestBody = {
        webhookUrl: chatInterface.webhookurl,
        message: userMessage.content,
        chatId: id,
        sessionId: localStorage.getItem('chatSessionId') || Date.now().toString()
      };

      console.log('Request body:', requestBody);

      // Send message to our proxy API instead of directly to the webhook
      const response = await fetch('/api/webhook-proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Response from webhook proxy:', data);

      // Add bot response to the chat
      if (data.response) {
        const botMessage = {
          id: `bot-${Date.now()}`,
          content: data.response,
          sender: 'bot' as const,
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, botMessage]);
        setLastBotMessage(data.response);
      } else {
        console.warn('No response content received from webhook');
        // Add a fallback message
        const fallbackContent = "I'm sorry, I couldn't process your request at this time.";
        const fallbackMessage = {
          id: `bot-${Date.now()}`,
          content: fallbackContent,
          sender: 'bot' as const,
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, fallbackMessage]);
        setLastBotMessage(fallbackContent);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
      toast.error('Failed to send message');

      // Add an error message from the bot
      const errorContent = "I'm sorry, there was an error processing your message. Please try again later.";
      const errorMessage = {
        id: `error-${Date.now()}`,
        content: errorContent,
        sender: 'bot' as const,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      setLastBotMessage(errorContent);
    } finally {
      setSending(false);
    }
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // If user presses Enter (typing), this is NOT a voice input
      // But don't reset if this was set by voice input
      if (!isMicrophoneActive && !messageWasVoiceInput) {
        setMessageWasVoiceInput(false);
        console.log('Enter key pressed in widget (typing), TTS disabled for bot response');
      } else if (messageWasVoiceInput) {
        console.log('Enter key pressed in widget after voice input, TTS enabled for bot response');
      }
      sendMessage();
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-600">Loading chat interface...</p>
        <p className="text-xs text-gray-500 mt-2">Chat ID: {id}</p>
      </div>
    );
  }

  if (!chatInterface) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold text-red-500 mb-4">Chat Not Found</h1>
        <p className="text-gray-600 mb-2">The chat interface you're looking for doesn't exist or has been removed.</p>
        <p className="text-sm text-gray-500 mb-6">Chat ID: {id}</p>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-md">
            <p className="text-red-600">{error}</p>
          </div>
        )}
      </div>
    );
  }

  // Determine colors based on chat interface settings or defaults
  const primaryColor = chatInterface.primary_color || '#3b82f6';
  const useGradientHeader = chatInterface.use_gradient_header || false;
  const gradientStartColor = chatInterface.gradient_start_color || '#3b82f6';
  const gradientEndColor = chatInterface.gradient_end_color || '#9333ea';
  const gradientDirection = chatInterface.gradient_direction || 'to bottom';
  const userBubbleColor = chatInterface.user_bubble_color || '#ffffff';
  const botBubbleColor = chatInterface.bot_bubble_color || primaryColor;
  const userTextColor = chatInterface.user_text_color || '#000000';
  const botTextColor = chatInterface.bot_text_color || '#ffffff';
  const isDarkMode = chatInterface.dark_mode || false;
  const showName = chatInterface.show_name !== undefined ? chatInterface.show_name : true;
  const showPoweredBy = chatInterface.show_powered_by !== undefined ? chatInterface.show_powered_by : true;
  const poweredByText = chatInterface.powered_by_text || 'Powered by BotFusion';
  const poweredByUrl = chatInterface.powered_by_url || 'https://botfusion.ai';
  const poweredByTextColor = chatInterface.powered_by_text_color || '#9ca3af';

  // Check if black outline is enabled
  const useBlackOutline = chatInterface.use_black_outline !== undefined ? chatInterface.use_black_outline : false;

  return (
    <SpeechToTextProvider provider={STTProvider.AUTO}>
      <div
        className={`chat-interface flex flex-col h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'} ${useBlackOutline ? 'border-2 border-black' : `border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}`}
        style={{
          borderWidth: useBlackOutline ? '2px' : '1px',
          borderColor: useBlackOutline ? 'black' : (isDarkMode ? '#374151' : '#e5e7eb'),
          borderStyle: 'solid'
        }}
      >
      {/* Chat Header */}
      <div
        className="flex items-center justify-between p-4"
        style={useGradientHeader
          ? { background: `linear-gradient(${gradientDirection}, ${gradientStartColor}, ${gradientEndColor})` }
          : { backgroundColor: primaryColor }
        }
      >
        <div className="flex items-center">
          {chatInterface.logo_url && (
            <img
              src={chatInterface.logo_url}
              alt={`${chatInterface.name} logo`}
              className="h-8 w-8 mr-2 rounded-full object-contain"
            />
          )}
          {showName && (
            <h1 className="font-medium text-white">{chatInterface.name}</h1>
          )}
        </div>
      </div>

      {/* Chat Messages */}
      <div className={`flex-1 overflow-y-auto p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className={`text-center ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Send a message to start the conversation
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg px-4 py-2 ${
                    message.sender === 'user'
                      ? 'rounded-tr-none'
                      : 'rounded-tl-none'
                  }`}
                  style={{
                    backgroundColor: message.sender === 'user' ? userBubbleColor : botBubbleColor,
                    color: message.sender === 'user' ? userTextColor : botTextColor,
                  }}
                >
                  <p className="whitespace-pre-wrap text-base leading-relaxed">{message.content}</p>
                </div>
              </div>
            ))}
          </div>
        )}
        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />
      </div>

      {/* Powered By */}
      {showPoweredBy && (
        <div className="text-center py-1 text-xs" style={{ color: poweredByTextColor }}>
          <a
            href={poweredByUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: poweredByTextColor }}
          >
            {poweredByText}
          </a>
        </div>
      )}

      {/* Text-to-Speech component */}
      <TextToSpeech
        text={lastBotMessage}
        autoPlay={messageWasVoiceInput}
        voice={(chatInterface?.voice_model || 'thalia') as any} // Use interface-specific voice
        microphoneActive={isMicrophoneActive}
        enableNaturalSpeech={chatInterface?.enable_natural_speech !== false} // Default to true if not specified
        onStart={() => {
          console.log('TTS audio started playing in widget, resetting voice input flag');
          // CRITICAL FIX: Delay resetting voice input flag to prevent autoPlay from changing during audio processing
          setTimeout(() => {
            setMessageWasVoiceInput(false);
          }, 1000); // Wait 1 second to ensure audio processing is stable
        }}
      />

      {/* Input Area */}
      <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700 bg-gray-900' : 'border-gray-200 bg-white'}`}>
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className={`w-full ${isDarkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-300'}`}
              disabled={sending}
            />
          </div>
          <UnifiedSpeechToText
            onTranscript={(text: string) => {
              // Only update text input if auto-send is disabled
              // For auto-send, transcript goes directly to chat without showing in input
              console.log('[DEBUG] Voice transcript received in widget:', text, 'Auto-send enabled, skipping text input update');
            }}
            disabled={sending}
            color={primaryColor}
            onMicrophoneActiveChange={(active) => {
              setIsMicrophoneActive(active);
              if (active) {
                setMessageWasVoiceInput(true);
                console.log('Voice input detected in widget, will enable TTS for next bot response');
              }
            }}
            autoSend={true}
            onSend={sendMessage}
          />
          <Button
            onClick={() => {
              // If user clicks send button (typing), this is NOT a voice input
              if (!isMicrophoneActive) {
                setMessageWasVoiceInput(false);
                console.log('Send button clicked in widget (typing), TTS disabled for bot response');
              }
              sendMessage();
            }}
            disabled={!inputValue.trim() || sending}
            className=""
            style={{ backgroundColor: primaryColor }}
          >
            {sending ? (
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"></div>
            ) : (
              'Send'
            )}
          </Button>
        </div>
        {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
      </div>
    </div>
    </SpeechToTextProvider>
  )
}
