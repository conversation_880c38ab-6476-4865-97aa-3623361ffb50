-- Add voice selection column to chat_interfaces table
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS voice_model TEXT DEFAULT 'thalia';

-- Add comment for documentation
COMMENT ON COLUMN chat_interfaces.voice_model IS 'Deepgram Aura voice model for TTS (thalia, arcas, apollo, helena, zeus, asteria)';

-- Add constraint to ensure only valid voice models are used
ALTER TABLE chat_interfaces ADD CONSTRAINT IF NOT EXISTS check_voice_model 
CHECK (voice_model IN ('thalia', 'arcas', 'apollo', 'helena', 'zeus', 'asteria'));
