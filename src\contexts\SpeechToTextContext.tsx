import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { STTProvider, SpeechToTextContextValue } from '@/types/deepgram-stt';

const SpeechToTextContext = createContext<SpeechToTextContextValue | undefined>(undefined);

interface SpeechToTextProviderProps {
  children: React.ReactNode;
  provider?: STTProvider;
}

export const SpeechToTextProvider: React.FC<SpeechToTextProviderProps> = ({ 
  children, 
  provider = STTProvider.AUTO 
}) => {
  const [currentProvider, setCurrentProvider] = useState<STTProvider>(provider);
  const [isDeepgramAvailable, setIsDeepgramAvailable] = useState(false);
  const [isWebSpeechAvailable, setIsWebSpeechAvailable] = useState(false);
  const [fallbackReason, setFallbackReason] = useState<string | undefined>();

  // Check if Web Speech API is available
  const checkWebSpeechAvailability = useCallback(() => {
    if (typeof window === 'undefined') return false;
    
    const hasWebSpeech = !!(
      (window as any).SpeechRecognition || 
      (window as any).webkitSpeechRecognition
    );
    
    console.log('[STTProvider] Web Speech API available:', hasWebSpeech);
    return hasWebSpeech;
  }, []);

  // Check if Deepgram STT is available
  const checkDeepgramAvailability = useCallback(async () => {
    try {
      const response = await fetch('/api/deepgram-stt');
      const data = await response.json();
      
      if (response.ok && data.enabled) {
        console.log('[STTProvider] Deepgram STT available');
        return true;
      } else {
        console.log('[STTProvider] Deepgram STT not available:', data.error || 'Unknown error');
        return false;
      }
    } catch (error) {
      console.error('[STTProvider] Deepgram availability check failed:', error);
      return false;
    }
  }, []);

  // Determine the best available provider
  const determineBestProvider = useCallback((
    requestedProvider: STTProvider,
    deepgramAvailable: boolean,
    webSpeechAvailable: boolean
  ): { provider: STTProvider; reason?: string } => {
    
    // If a specific provider is requested and available, use it
    if (requestedProvider === STTProvider.DEEPGRAM && deepgramAvailable) {
      return { provider: STTProvider.DEEPGRAM };
    }
    
    if (requestedProvider === STTProvider.WEB_SPEECH && webSpeechAvailable) {
      return { provider: STTProvider.WEB_SPEECH };
    }
    
    if (requestedProvider === STTProvider.DISABLED) {
      return { provider: STTProvider.DISABLED };
    }

    // Auto selection logic
    if (requestedProvider === STTProvider.AUTO) {
      // Prefer Deepgram if available
      if (deepgramAvailable) {
        return { provider: STTProvider.DEEPGRAM };
      }
      
      // Fallback to Web Speech API
      if (webSpeechAvailable) {
        return { 
          provider: STTProvider.WEB_SPEECH, 
          reason: 'Deepgram not available, using Web Speech API' 
        };
      }
      
      // No STT available
      return { 
        provider: STTProvider.DISABLED, 
        reason: 'No speech-to-text providers available' 
      };
    }

    // Requested provider not available, try fallbacks
    let reason = '';
    
    if (requestedProvider === STTProvider.DEEPGRAM) {
      reason = 'Deepgram not available';
    } else if (requestedProvider === STTProvider.WEB_SPEECH) {
      reason = 'Web Speech API not available';
    }

    // Try Deepgram first
    if (deepgramAvailable) {
      return { 
        provider: STTProvider.DEEPGRAM, 
        reason: `${reason}, using Deepgram` 
      };
    }
    
    // Fallback to Web Speech API
    if (webSpeechAvailable) {
      return { 
        provider: STTProvider.WEB_SPEECH, 
        reason: `${reason}, using Web Speech API` 
      };
    }
    
    // No providers available
    return { 
      provider: STTProvider.DISABLED, 
      reason: `${reason}, no fallback available` 
    };
  }, []);

  // Initialize provider availability and selection
  useEffect(() => {
    const initializeProviders = async () => {
      console.log('[STTProvider] Initializing providers...');
      
      // Check availability
      const webSpeechAvailable = checkWebSpeechAvailability();
      const deepgramAvailable = await checkDeepgramAvailability();
      
      setIsWebSpeechAvailable(webSpeechAvailable);
      setIsDeepgramAvailable(deepgramAvailable);
      
      // Determine best provider
      const { provider: bestProvider, reason } = determineBestProvider(
        provider,
        deepgramAvailable,
        webSpeechAvailable
      );
      
      console.log('[STTProvider] Selected provider:', bestProvider, reason ? `(${reason})` : '');
      
      setCurrentProvider(bestProvider);
      setFallbackReason(reason);
    };

    initializeProviders();
  }, [provider, checkWebSpeechAvailability, checkDeepgramAvailability, determineBestProvider]);

  // Handle provider switching with fallback
  const setProvider = useCallback(async (newProvider: STTProvider) => {
    console.log('[STTProvider] Switching to provider:', newProvider);
    
    const { provider: selectedProvider, reason } = determineBestProvider(
      newProvider,
      isDeepgramAvailable,
      isWebSpeechAvailable
    );
    
    console.log('[STTProvider] Actually using provider:', selectedProvider, reason ? `(${reason})` : '');
    
    setCurrentProvider(selectedProvider);
    setFallbackReason(reason);
  }, [isDeepgramAvailable, isWebSpeechAvailable, determineBestProvider]);

  // Handle Deepgram errors by falling back to Web Speech API
  const handleDeepgramError = useCallback(() => {
    console.warn('[STTProvider] Deepgram error, falling back to Web Speech API');
    
    if (isWebSpeechAvailable) {
      setCurrentProvider(STTProvider.WEB_SPEECH);
      setFallbackReason('Deepgram error, using Web Speech API');
    } else {
      setCurrentProvider(STTProvider.DISABLED);
      setFallbackReason('Deepgram error, no fallback available');
    }
  }, [isWebSpeechAvailable]);

  // Handle Web Speech API errors
  const handleWebSpeechError = useCallback(() => {
    console.warn('[STTProvider] Web Speech API error');
    
    if (isDeepgramAvailable && currentProvider !== STTProvider.DEEPGRAM) {
      setCurrentProvider(STTProvider.DEEPGRAM);
      setFallbackReason('Web Speech API error, using Deepgram');
    } else {
      setCurrentProvider(STTProvider.DISABLED);
      setFallbackReason('Web Speech API error, no fallback available');
    }
  }, [isDeepgramAvailable, currentProvider]);

  const contextValue: SpeechToTextContextValue = {
    provider,
    setProvider,
    isDeepgramAvailable,
    isWebSpeechAvailable,
    currentProvider,
    fallbackReason
  };

  // Add error handlers to context for components to use
  (contextValue as any).handleDeepgramError = handleDeepgramError;
  (contextValue as any).handleWebSpeechError = handleWebSpeechError;

  return (
    <SpeechToTextContext.Provider value={contextValue}>
      {children}
    </SpeechToTextContext.Provider>
  );
};

// Hook to use the SpeechToText context
export const useSpeechToText = (): SpeechToTextContextValue => {
  const context = useContext(SpeechToTextContext);
  if (context === undefined) {
    throw new Error('useSpeechToText must be used within a SpeechToTextProvider');
  }
  return context;
};

// Hook to get error handlers
export const useSpeechToTextErrorHandlers = () => {
  const context = useContext(SpeechToTextContext);
  if (context === undefined) {
    throw new Error('useSpeechToTextErrorHandlers must be used within a SpeechToTextProvider');
  }
  
  return {
    handleDeepgramError: (context as any).handleDeepgramError,
    handleWebSpeechError: (context as any).handleWebSpeechError
  };
};

export default SpeechToTextProvider;
