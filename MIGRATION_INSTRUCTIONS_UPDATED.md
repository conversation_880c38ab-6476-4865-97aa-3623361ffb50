# How to Run the Migration for Powered By Text Color

To add the "Powered By Text Color" column to the `chat_interfaces` table, follow these steps:

## Using the Supabase Dashboard SQL Editor

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the following SQL:

```sql
-- Add powered_by_text_color field to chat_interfaces table
ALTER TABLE chat_interfaces ADD COLUMN IF NOT EXISTS powered_by_text_color TEXT;
```

5. Run the query
6. Verify the column was added by running:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'chat_interfaces' 
AND column_name = 'powered_by_text_color';
```

## After Running the Migration

After adding the column to the database, restart your application. The "Powered By" text color customization will now work correctly, and you'll be able to change the color in the chat interface settings.

## Using the Feature

1. Go to the dashboard and edit a chat interface
2. Navigate to the "Appearance" tab and then to the "General" section
3. Under the "Powered By Section" settings, you'll find a color picker for the "Text Color"
4. Choose any color you want for the "Powered By" text
5. Save your changes
6. The "Powered By" text will now appear in the color you selected
