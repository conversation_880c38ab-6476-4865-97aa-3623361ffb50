import { NextRequest, NextResponse } from 'next/server';
import { buildDeepgramTTSUrl, DEEPGRAM_HEADERS, STANDARD_AUDIO_CONFIG } from '@/lib/tts-config-standard';

const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;

export async function GET(request: NextRequest) {
  if (!DEEPGRAM_API_KEY) {
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  // Test texts with various number formats that should be pronounced naturally
  const testCases = [
    {
      name: "Basic Numbers",
      text: "The number 14 should be pronounced as fourteen, not one four."
    },
    {
      name: "Large Numbers", 
      text: "The year 2024 and the number 1,500 should sound natural."
    },
    {
      name: "Decimals",
      text: "The price is $19.99 and the temperature is 98.6 degrees."
    },
    {
      name: "Percentages",
      text: "The success rate is 95% and the completion is 100%."
    },
    {
      name: "Phone Numbers",
      text: "Call me at ************ for more information."
    },
    {
      name: "Dates",
      text: "The meeting is on 12/25/2024 at 3:30 PM."
    },
    {
      name: "Mixed Content",
      text: "Order #12345 for $299.99 will arrive on 1/15/2025 at 2:00 PM."
    }
  ];

  const voiceModel = 'aura-2-thalia-en'; // Use default voice for testing
  const results: any[] = [];

  console.log(`🔢 Testing smart format number pronunciation with ${testCases.length} test cases...`);

  for (const testCase of testCases) {
    const startTime = Date.now();
    
    try {
      console.log(`Testing: ${testCase.name}`);
      
      // Test with smart_format=true (standardized configuration)
      const response = await fetch(buildDeepgramTTSUrl(voiceModel, 'linear16'), {
        method: 'POST',
        headers: {
          ...DEEPGRAM_HEADERS,
          'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        },
        body: JSON.stringify({ text: testCase.text })
      });

      const duration = Date.now() - startTime;

      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();
        const audioSize = audioBuffer.byteLength;
        
        results.push({
          testCase: testCase.name,
          text: testCase.text,
          status: 'success',
          audioSize,
          duration,
          smartFormatEnabled: STANDARD_AUDIO_CONFIG.smartFormat,
          sampleRate: STANDARD_AUDIO_CONFIG.sampleRate,
          encoding: STANDARD_AUDIO_CONFIG.encoding
        });
        
        console.log(`✅ ${testCase.name}: ${audioSize} bytes in ${duration}ms`);
      } else {
        const errorText = await response.text();
        results.push({
          testCase: testCase.name,
          text: testCase.text,
          status: 'error',
          error: `HTTP ${response.status}: ${errorText}`,
          duration
        });
        
        console.log(`❌ ${testCase.name}: HTTP ${response.status}`);
      }
    } catch (error: any) {
      const duration = Date.now() - startTime;
      results.push({
        testCase: testCase.name,
        text: testCase.text,
        status: 'error',
        error: error.message,
        duration
      });
      
      console.log(`❌ ${testCase.name}: ${error.message}`);
    }

    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  const successCount = results.filter(r => r.status === 'success').length;
  const failureCount = results.filter(r => r.status === 'error').length;

  const summary = {
    totalTests: testCases.length,
    successCount,
    failureCount,
    successRate: `${((successCount / testCases.length) * 100).toFixed(1)}%`,
    voiceModel,
    smartFormatEnabled: STANDARD_AUDIO_CONFIG.smartFormat,
    standardConfiguration: STANDARD_AUDIO_CONFIG,
    averageDuration: Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length),
    totalTestDuration: results.reduce((sum, r) => sum + r.duration, 0),
    expectedBehavior: {
      "14": "Should be pronounced as 'fourteen'",
      "2024": "Should be pronounced as 'twenty twenty-four'", 
      "$19.99": "Should be pronounced as 'nineteen dollars and ninety-nine cents'",
      "95%": "Should be pronounced as 'ninety-five percent'",
      "************": "Should be pronounced as phone number format",
      "12/25/2024": "Should be pronounced as date format"
    }
  };

  console.log(`🎯 Smart Format Test Complete: ${successCount}/${testCases.length} tests passed`);

  return NextResponse.json({
    summary,
    results,
    testCases,
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: NextRequest) {
  // Test custom text with smart formatting
  const { text, voiceModel = 'aura-2-thalia-en', compareWithoutSmartFormat = false } = await request.json();
  
  if (!DEEPGRAM_API_KEY) {
    return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
  }

  if (!text) {
    return NextResponse.json({ error: 'Text is required' }, { status: 400 });
  }

  try {
    const results: any[] = [];

    // Test with smart_format=true (standardized)
    const smartFormatResponse = await fetch(buildDeepgramTTSUrl(voiceModel, 'linear16'), {
      method: 'POST',
      headers: {
        ...DEEPGRAM_HEADERS,
        'Authorization': `Token ${DEEPGRAM_API_KEY}`,
      },
      body: JSON.stringify({ text })
    });

    if (smartFormatResponse.ok) {
      const audioBuffer = await smartFormatResponse.arrayBuffer();
      results.push({
        type: 'smart_format_enabled',
        audioSize: audioBuffer.byteLength,
        smartFormat: true
      });
    }

    // Optionally test without smart format for comparison
    if (compareWithoutSmartFormat) {
      const noSmartFormatUrl = `https://api.deepgram.com/v1/speak?model=${encodeURIComponent(voiceModel)}&encoding=linear16&sample_rate=24000&smart_format=false`;
      
      const noSmartFormatResponse = await fetch(noSmartFormatUrl, {
        method: 'POST',
        headers: {
          ...DEEPGRAM_HEADERS,
          'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        },
        body: JSON.stringify({ text })
      });

      if (noSmartFormatResponse.ok) {
        const audioBuffer = await noSmartFormatResponse.arrayBuffer();
        results.push({
          type: 'smart_format_disabled',
          audioSize: audioBuffer.byteLength,
          smartFormat: false
        });
      }
    }

    // Return the smart format audio
    if (smartFormatResponse.ok) {
      const audioBuffer = await smartFormatResponse.arrayBuffer();
      
      return new Response(audioBuffer, {
        headers: {
          'Content-Type': 'audio/wav',
          'Content-Disposition': 'attachment; filename="smart-format-test.wav"',
          'X-Smart-Format': 'true',
          'X-Voice-Model': voiceModel,
          'X-Test-Text': text,
          'X-Audio-Size': audioBuffer.byteLength.toString()
        }
      });
    } else {
      const errorText = await smartFormatResponse.text();
      return NextResponse.json({ 
        error: `Deepgram API error: ${smartFormatResponse.status}`,
        details: errorText
      }, { status: smartFormatResponse.status });
    }
  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Smart format test failed',
      details: error.message
    }, { status: 500 });
  }
}
