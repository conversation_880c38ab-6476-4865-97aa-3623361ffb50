import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// This route completely bypasses all authentication and middleware
export const dynamic = 'force-dynamic';

// Common headers for all responses
const commonHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
  'Content-Security-Policy': "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:;"
};

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Await params to fix the error
    const { id } = await Promise.resolve(params);

    console.log('Public chat API request for ID:', id);

    if (!id) {
      console.error('Public chat API: Missing chat ID');
      return NextResponse.json({ error: 'Chat ID is required' }, {
        status: 400,
        headers: commonHeaders
      });
    }

    // Check environment variables first
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('Environment check:', {
      hasSupabaseUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey,
      supabaseUrlLength: supabaseUrl?.length || 0,
      serviceKeyLength: supabaseServiceKey?.length || 0
    });

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables:', {
        NEXT_PUBLIC_SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_ROLE_KEY: !!supabaseServiceKey
      });
      return NextResponse.json({ 
        error: 'Server configuration error',
        details: 'Missing required environment variables'
      }, {
        status: 500,
        headers: commonHeaders
      });
    }

    // Create Supabase client directly with service key
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('Attempting to fetch chat interface with ID:', id);
    
    const { data, error } = await supabase
      .from('chat_interfaces')
      .select('*')
      .eq('id', id)
      .single();

    console.log('Supabase query result:', {
      hasData: !!data,
      error: error?.message || null,
      errorCode: error?.code || null,
      errorDetails: error?.details || null
    });

    if (error) {
      console.error('Error fetching chat interface:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      
      // Return more specific error information
      return NextResponse.json({ 
        error: 'Database query failed',
        message: error.message,
        code: error.code,
        details: error.details
      }, {
        status: error.code === 'PGRST116' ? 404 : 500,
        headers: commonHeaders
      });
    }

    if (!data) {
      console.log('No chat interface found for ID:', id);
      return NextResponse.json({ error: 'Chat interface not found' }, {
        status: 404,
        headers: commonHeaders
      });
    }

    console.log('Successfully fetched chat interface:', {
      id: data.id,
      name: data.name || 'unnamed'
    });

    return NextResponse.json(data, {
      status: 200,
      headers: commonHeaders
    });

  } catch (error) {
    console.error('Unexpected error in public chat API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: commonHeaders
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: commonHeaders
  });
}
