'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'

export default function LandingPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-black via-purple-900 to-blue-900 text-white relative overflow-hidden font-sans">
      {/* Glowing background blobs */}
      <div className="absolute top-[-150px] left-[-150px] w-[500px] h-[500px] bg-gradient-to-tr from-purple-700 to-blue-600 rounded-full filter blur-3xl opacity-80 animate-blob animation-delay-2000"></div>
      <div className="absolute top-[80px] right-[-120px] w-[400px] h-[400px] bg-gradient-to-tr from-blue-600 to-purple-700 rounded-full filter blur-2xl opacity-70 animate-blob animation-delay-4000"></div>
      <div className="absolute bottom-[-120px] left-[50%] w-[600px] h-[600px] bg-gradient-to-tr from-purple-800 to-blue-500 rounded-full filter blur-3xl opacity-60 animate-blob animation-delay-6000"></div>

      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center min-h-[60vh] px-6 text-center relative z-10">
        <img src="/logo.png" alt="BotFusion X Logo" className="w-48 mb-6 drop-shadow-[0_0_15px_rgba(139,92,246,0.8)]" />
        <motion.h1
          initial={{ opacity: 0, y: -40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="text-6xl font-extrabold max-w-4xl mb-6 tracking-wider drop-shadow-[0_0_20px_rgba(139,92,246,0.9)] text-blue-300"
        >
          BotFusion X
        </motion.h1>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 1.2 }}
          className="text-2xl max-w-3xl mb-10 text-blue-400 drop-shadow-[0_0_10px_rgba(139,92,246,0.7)]"
        >
          Generate and customize chat interfaces connected to n8n workflows via webhooks.
        </motion.p>
        <a href="/login" className="inline-block">
          <Button className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 via-purple-700 to-blue-800 hover:from-blue-700 hover:via-purple-800 hover:to-blue-900 rounded-lg shadow-lg shadow-blue-700/50 transition-transform transform hover:scale-105">
            Connect Your Chat Flow
          </Button>
        </a>
      </section>

      <section className="py-10 px-6 relative z-10 max-w-4xl mx-auto">
        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="max-w-3xl mx-auto text-center text-blue-300 text-lg drop-shadow-[0_0_10px_rgba(139,92,246,0.7)] leading-relaxed"
        >
          BotFusion X empowers businesses to create and customize chat interfaces seamlessly integrated with n8n workflows via webhooks. Our platform offers flexibility, ease of use, and powerful customization options to enhance customer engagement and automate workflows efficiently.
        </motion.p>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-6 bg-gradient-to-b from-black via-purple-900 to-blue-900">
        <motion.h2
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-3xl font-bold text-center mb-12 text-blue-300 drop-shadow-[0_0_10px_rgba(139,92,246,0.8)]"
        >
          How It Works
        </motion.h2>
        <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-purple-700 to-blue-700 p-6 rounded-lg shadow-lg shadow-blue-700/70 border border-blue-500 hover:shadow-blue-900 hover:border-blue-900 transition-shadow duration-300"
          >
            <h3 className="text-xl font-semibold mb-4 text-blue-200 drop-shadow-[0_0_8px_rgba(139,92,246,0.8)]">Connect n8n</h3>
            <p className="text-blue-100">Link your chat interface to n8n workflows via webhook URLs.</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-purple-700 to-blue-700 p-6 rounded-lg shadow-lg shadow-blue-700/70 border border-blue-500 hover:shadow-blue-900 hover:border-blue-900 transition-shadow duration-300"
          >
            <h3 className="text-xl font-semibold mb-4 text-blue-200 drop-shadow-[0_0_8px_rgba(139,92,246,0.8)]">Customize</h3>
            <p className="text-blue-100">Personalize chat UI with colors, branding, and more.</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
            className="bg-gradient-to-r from-purple-700 to-blue-700 p-6 rounded-lg shadow-lg shadow-blue-700/70 border border-blue-500 hover:shadow-blue-900 hover:border-blue-900 transition-shadow duration-300"
          >
            <h3 className="text-xl font-semibold mb-4 text-blue-200 drop-shadow-[0_0_8px_rgba(139,92,246,0.8)]">Deploy</h3>
            <p className="text-blue-100">Get your unique URL and embed code to launch instantly.</p>
          </motion.div>
        </div>
      </section>

      {/* Pricing Plans Section */}
      <section className="py-20 px-6 bg-purple-800">
        <motion.h2
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-3xl font-bold text-center mb-12 text-purple-200 drop-shadow-md"
        >
          Pricing Plans
        </motion.h2>
        <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Free Plan */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="bg-purple-700 p-8 rounded-lg shadow-lg text-center"
          >
            <h3 className="text-xl font-semibold mb-4">Free</h3>
            <p className="mb-6">1 chat, 100 messages/month, "Powered by BotFusion X" badge</p>
            <p className="font-bold text-2xl mb-6">$0</p>
            <Button disabled className="opacity-50 cursor-not-allowed">
              Current Plan
            </Button>
          </motion.div>

          {/* Pro Plan */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="bg-purple-600 p-8 rounded-lg shadow-lg text-center"
          >
            <h3 className="text-xl font-semibold mb-4">Pro</h3>
            <p className="mb-6">Up to 10 chats, 10K messages/month, branding removal</p>
            <p className="font-bold text-2xl mb-6">$19/mo</p>
            <a href="/signup" className="inline-block">
              <Button className="bg-purple-700 hover:bg-purple-800">
                Upgrade
              </Button>
            </a>
          </motion.div>

          {/* Enterprise Plan */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="bg-purple-600 p-8 rounded-lg shadow-lg text-center"
          >
            <h3 className="text-xl font-semibold mb-4">Enterprise</h3>
            <p className="mb-6">Unlimited chats, white-label, API access, priority support</p>
            <p className="font-bold text-2xl mb-6">Custom Pricing</p>
            <a href="/contact" className="inline-block">
              <Button className="bg-purple-700 hover:bg-purple-800">
                Contact Us
              </Button>
            </a>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-6 bg-purple-900">
        <motion.h2
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-3xl font-bold text-center mb-12 text-purple-200 drop-shadow-md"
        >
          Testimonials
        </motion.h2>
        <div className="max-w-4xl mx-auto space-y-8">
          <motion.blockquote
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="border-l-4 border-purple-600 pl-6 italic text-purple-300"
          >
            "BotFusion X transformed our customer support with seamless chat workflows."
          </motion.blockquote>
          <motion.blockquote
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="border-l-4 border-purple-600 pl-6 italic text-purple-300"
          >
            "The customization options and ease of integration saved us hours of development."
          </motion.blockquote>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 bg-purple-800 text-center text-purple-300">
        <p>© 2025 BotFusion X. All rights reserved.</p>
        <div className="mt-4 space-x-4">
          <a href="mailto:<EMAIL>" className="hover:underline">
            Contact
          </a>
          <a href="https://twitter.com/botfusionx" target="_blank" rel="noopener noreferrer" className="hover:underline">
            Twitter
          </a>
          <a href="https://github.com/botfusionx" target="_blank" rel="noopener noreferrer" className="hover:underline">
            GitHub
          </a>
        </div>
      </footer>
    </main>
  )
}
