import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Fetching users for admin dashboard...')

    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    // Fetch user profiles with tier information
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (profilesError) {
      console.error('Error fetching user profiles:', profilesError)
      return NextResponse.json({ 
        error: 'Failed to fetch user profiles',
        details: profilesError.message
      }, { status: 500 })
    }

    console.log(`📊 Found ${profiles.length} user profiles`)

    // Fetch user emails from auth.users (requires service role)
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()

    if (authError) {
      console.error('Error fetching auth users:', authError)
      return NextResponse.json({ 
        error: 'Failed to fetch auth users',
        details: authError.message
      }, { status: 500 })
    }

    console.log(`👥 Found ${authUsers.users.length} auth users`)

    // Fetch chat interface counts for each user
    const userIds = profiles.map(p => p.id)
    const { data: chatCounts, error: chatError } = await supabase
      .from('chat_interfaces')
      .select('user_id')
      .in('user_id', userIds)

    if (chatError) {
      console.error('Error fetching chat interface counts:', chatError)
      // Continue without chat counts if there's an error
    }

    // Combine data
    const usersWithData = profiles.map(profile => {
      const authUser = authUsers.users.find(u => u.id === profile.id)
      const chatCount = chatCounts?.filter(c => c.user_id === profile.id).length || 0

      return {
        ...profile,
        email: authUser?.email || 'Unknown',
        chat_interface_count: chatCount
      }
    })

    console.log(`✅ Successfully combined data for ${usersWithData.length} users`)

    return NextResponse.json({
      users: usersWithData,
      totalUsers: usersWithData.length,
      tierCounts: {
        free: usersWithData.filter(u => u.tier === 'free').length,
        standard: usersWithData.filter(u => u.tier === 'standard').length,
        pro: usersWithData.filter(u => u.tier === 'pro').length
      }
    })

  } catch (error) {
    console.error('Error in admin users API:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
