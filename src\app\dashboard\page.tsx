'use client'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import ChatInterfaceForm from '@/components/ChatInterfaceForm'
import WebhookForm from '@/components/WebhookForm'
import WebhookEditModal from '@/components/WebhookEditModal'
import EmbedCodeModal from '@/components/EmbedCodeModal'
import ChatActivity<PERSON>hart from '@/components/ChatActivityChart'
import Link from 'next/link'

interface ChatInterface {
  id: string
  name: string
  description: string
  webhookurl: string // Note: lowercase property name to match database column
  createdat: string // Note: lowercase property name to match database column
  unique_url?: string
  embed_code?: string
  primary_color?: string
  use_gradient_header?: boolean
  gradient_start_color?: string
  gradient_end_color?: string
  gradient_direction?: string
  user_bubble_color?: string
  bot_bubble_color?: string
  user_text_color?: string
  bot_text_color?: string
  logo_url?: string
  dark_mode?: boolean
  show_name?: boolean
  use_black_outline?: boolean
  show_powered_by?: boolean
  powered_by_text?: string
  powered_by_url?: string
  chat_stats?: {
    totalMessages: number
    uniqueSessions: number
    lastActivity: string | null
  }
}

export default function DashboardPage() {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showWebhookForm, setShowWebhookForm] = useState(false)
  const [editingChat, setEditingChat] = useState<ChatInterface | null>(null)
  const [showEmbedModal, setShowEmbedModal] = useState(false)
  const [selectedChatForEmbed, setSelectedChatForEmbed] = useState<ChatInterface | null>(null)
  const [editingWebhook, setEditingWebhook] = useState<any>(null)
  const [webhookTestResults, setWebhookTestResults] = useState<{[key: string]: {
    success: boolean
    message: string
    timestamp: string
  }}>({})
  const [testingWebhooks, setTestingWebhooks] = useState<{[key: string]: boolean}>({})

  // Function to convert database format to form format
  const chatToFormData = (chat: ChatInterface) => ({
    id: chat.id,
    name: chat.name,
    description: chat.description || '',
    webhookUrl: chat.webhookurl, // Map from database field to form field
    primaryColor: chat.primary_color || '#3b82f6',
    useGradientHeader: chat.use_gradient_header || false,
    gradientStartColor: chat.gradient_start_color || '#3b82f6',
    gradientEndColor: chat.gradient_end_color || '#9333ea',
    gradientDirection: chat.gradient_direction || 'to right',
    userBubbleColor: chat.user_bubble_color || '#ffffff',
    botBubbleColor: chat.bot_bubble_color || '#3b82f6',
    userTextColor: chat.user_text_color || '#000000',
    botTextColor: chat.bot_text_color || '#ffffff',
    logoUrl: chat.logo_url || '',
    darkMode: chat.dark_mode || false,
    useBlackOutline: chat.use_black_outline || false
  })
  // eslint-disable-next-line no-unused-vars
  const [chatInterfaces, setChatInterfaces] = useState<ChatInterface[]>([])
  const [webhooks, setWebhooks] = useState<Array<{
    id: string
    name: string
    url: string
    description?: string
  }>>([])

  // Function to fetch chat stats for a specific chat interface
  const fetchChatStats = async (chatId: string, forceRefresh: boolean = false) => {
    try {
      console.log(`Fetching stats for chat ${chatId}${forceRefresh ? ' (force refresh)' : ''}`)
      const response = await fetch(`/api/chat-stats?chatId=${chatId}${forceRefresh ? '&forceRefresh=true' : ''}`, {
        // Add cache: 'no-store' to prevent caching
        cache: 'no-store',
        // Add a timestamp to the URL to prevent caching
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })
      if (!response.ok) {
        console.error(`Failed to fetch stats for chat ${chatId}:`, response.statusText)
        return null
      }
      const stats = await response.json()
      console.log(`Stats for chat ${chatId}:`, stats)
      return stats
    } catch (error) {
      console.error(`Error fetching stats for chat ${chatId}:`, error)
      return null
    }
  }

  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch chat interfaces
        const chatRes = await fetch('/api/chat-interfaces', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
        if (chatRes.ok) {
          const chatData = await chatRes.json()
          const interfaces = Array.isArray(chatData) ? chatData : []

          // Fetch stats for each chat interface
          console.log('Fetching stats for all chat interfaces:', interfaces)
          const interfacesWithStats = await Promise.all(
            interfaces.map(async (chat) => {
              const stats = await fetchChatStats(chat.id)
              const chatWithStats = {
                ...chat,
                chat_stats: stats ? {
                  totalMessages: stats.totalMessages || 0,
                  uniqueSessions: stats.uniqueSessions || 0,
                  lastActivity: stats.lastActivity
                } : {
                  totalMessages: 0,
                  uniqueSessions: 0,
                  lastActivity: null
                } // Provide default values instead of undefined
              }
              console.log(`Chat interface ${chat.id} with stats:`, chatWithStats)
              return chatWithStats
            })
          )

          console.log('All interfaces with stats:', interfacesWithStats)
          setChatInterfaces(interfacesWithStats)
        } else {
          console.error('Failed to fetch chat interfaces')
          setChatInterfaces([])
        }

        // Fetch webhooks
        const webhookRes = await fetch('/api/webhooks', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
        if (webhookRes.ok) {
          const webhookData = await webhookRes.json()
          setWebhooks(Array.isArray(webhookData) ? webhookData : [])
        } else {
          setWebhooks([])
          console.error('Failed to fetch webhooks')
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    // Fetch data immediately
    fetchData()

    // Set up a refresh interval (every 30 seconds)
    const refreshInterval = setInterval(fetchData, 30000)

    // Clean up the interval when the component unmounts
    return () => clearInterval(refreshInterval)
  }, [])

  // eslint-disable-next-line no-unused-vars


  // We've moved the handleSaveInterface functionality directly into the onSave callback
  // This is kept as a placeholder in case we need to reuse this logic elsewhere
  const handleSaveInterface = async (data: {
    name: string
    description: string
    webhookUrl: string
    primaryColor: string
    useGradientHeader: boolean
    gradientStartColor: string
    gradientEndColor: string
    gradientDirection: string
    userBubbleColor: string
    botBubbleColor: string
    userTextColor: string
    botTextColor: string
    logoUrl: string
    darkMode: boolean
    showName: boolean
    useBlackOutline: boolean
  }) => {
    console.log('This function is now handled directly in the onSave callback');
  }

  const handleCreateWebhook = async (data: {
    name: string
    url: string
    description?: string
  }) => {
    console.log('Dashboard: handleCreateWebhook called with data:', data)

    try {
      // Validate URL format
      console.log('Dashboard: Validating URL format...')
      new URL(data.url)
      console.log('Dashboard: URL format is valid')

      console.log('Dashboard: Making POST request to /api/webhooks...')
      const res = await fetch('/api/webhooks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      console.log('Dashboard: Response status:', res.status)
      console.log('Dashboard: Response ok:', res.ok)

      if (!res.ok) {
        const errorData = await res.json()
        console.error('Dashboard: Error response data:', errorData)
        throw new Error(errorData.error || 'Failed to create webhook')
      }

      const created = await res.json()
      console.log('Dashboard: Webhook created successfully:', created)
      setWebhooks((prev) => [created[0], ...prev])
      setShowWebhookForm(false)
      toast.success('Webhook created successfully')
    } catch (error) {
      console.error('Dashboard: Error in handleCreateWebhook:', error)
      toast.error(
        error instanceof Error ? error.message : 'Invalid webhook URL'
      )
      throw error // Re-throw to let WebhookForm handle it
    }
  }

  const toggleCreateForm = () => setShowCreateForm(!showCreateForm)
  const toggleWebhookForm = () => setShowWebhookForm(!showWebhookForm)
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <h2 className="text-2xl font-bold text-neon-blue">My Chat Interfaces</h2>
        <div className="flex gap-4">
          <Button
            onClick={toggleWebhookForm}
            className="glass-button transition-colors duration-300 cursor-pointer
              hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
              active:scale-95 active:bg-cyan-700"
          >
            Create Webhook
          </Button>
          <Button
            onClick={toggleCreateForm}
            className="glass-button transition-colors duration-300 cursor-pointer
              hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
              active:scale-95 active:bg-cyan-700"
          >
            Create Chat
          </Button>
        </div>
      </motion.div>

      {(showCreateForm || editingChat) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 p-6 rounded-lg max-w-md w-full">
            <ChatInterfaceForm
              onSave={async (data) => {
                try {
                  // Handle create or update
                  if (editingChat) {
                    // Update existing chat interface
                    const res = await fetch(`/api/chat-interfaces?id=${editingChat.id}`, {
                      method: 'PUT',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify(data)
                    });

                    if (!res.ok) {
                      const errorData = await res.json();
                      throw new Error(errorData.error || 'Failed to update chat interface');
                    }

                    const updated = await res.json();

                    // Preserve the chat stats when updating the chat interface
                    setChatInterfaces(prev =>
                      prev.map(chat => {
                        if (chat.id === editingChat.id) {
                          // Keep the existing chat_stats when updating
                          return {
                            ...updated,
                            chat_stats: chat.chat_stats
                          };
                        }
                        return chat;
                      })
                    );
                    toast.success('Chat interface updated successfully');
                  } else {
                    // Create new chat interface
                    console.log('Sending data to create chat interface:', data);

                    let responseData;
                    try {
                      const res = await fetch('/api/chat-interfaces', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                      });

                      responseData = await res.json();
                      console.log('Response from API:', responseData, 'Status:', res.status);

                      if (!res.ok) {
                        // Check if it's a name conflict error
                        if (responseData.error && (
                          responseData.error.includes('webhook with this name already exists') ||
                          responseData.error.includes('chat interface with this name already exists')
                        )) {
                          throw new Error(responseData.error);
                        }
                        throw new Error(responseData.error || 'Failed to create chat interface');
                      }
                    } catch (fetchError) {
                      console.error('Fetch error:', fetchError);
                      throw fetchError;
                    }

                    const newInterface = Array.isArray(responseData) ? responseData[0] : responseData;

                    // Update state with new interface
                    setChatInterfaces(prev => [newInterface, ...prev]);
                    toast.success('Chat interface created successfully');
                  }

                  // Close the modal
                  setShowCreateForm(false);
                  setEditingChat(null);
                } catch (error) {
                  toast.error(
                    error instanceof Error ? error.message : 'Failed to save chat interface'
                  );
                }
              }}
              webhooks={webhooks}
              initialData={editingChat || undefined}
            />
            <Button
              variant="ghost"
              onClick={() => {
                setShowCreateForm(false);
                setEditingChat(null);
              }}
              className="mt-4 w-full"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {showWebhookForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 p-6 rounded-lg max-w-md w-full">
            <WebhookForm onSave={handleCreateWebhook} />
            <Button
              variant="ghost"
              onClick={() => setShowWebhookForm(false)}
              className="mt-4 w-full"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {showEmbedModal && selectedChatForEmbed && (
        <EmbedCodeModal
          chatId={selectedChatForEmbed.id}
          chatName={selectedChatForEmbed.name}
          onClose={() => {
            setShowEmbedModal(false)
            setSelectedChatForEmbed(null)
          }}
        />
      )}

      {editingWebhook && (
        <WebhookEditModal
          webhook={editingWebhook}
          onClose={() => setEditingWebhook(null)}
          onUpdate={(updatedWebhook) => {
            setWebhooks(webhooks.map(w => w.id === editingWebhook.id ? updatedWebhook : w))
            setEditingWebhook(null)
          }}
        />
      )}

      <div className="space-y-8">
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
          {/* Chat interface cards */}
        {chatInterfaces.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="col-span-full py-12 text-center"
          >
            {/* eslint-disable-next-line react/no-unescaped-entities */}
            <p className="text-neon-blue/70">You haven't created any chat interfaces yet</p>
            <Button
              className="mt-4 glass-button transition-colors duration-300 cursor-pointer
                hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                active:scale-95 active:bg-cyan-700"
              onClick={toggleWebhookForm}
            >
              Create your first chat
            </Button>
          </motion.div>
        ) : (
          chatInterfaces.map((chat) => (
            <motion.div
              key={chat.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="glass-card p-6 transition-all duration-300
                hover:shadow-glow-blue hover:bg-neon-blue/5 hover:border-neon-blue/30
                group data-[selected=true]:bg-neon-blue/10 data-[selected=true]:border-neon-blue/50
                active:bg-neon-blue/20">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-medium text-neon-blue">{chat.name}</h3>
                    {chat.description && (
                      <p className="text-sm text-neon-blue/70 mt-1">
                        {chat.description}
                        <span className="text-xs text-neon-blue/50 ml-2">(dashboard only)</span>
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex flex-col items-center justify-center bg-gray-800/50 rounded-lg p-3 text-center">
                      <span className="text-2xl font-bold text-neon-blue">{chat.chat_stats?.totalMessages || 0}</span>
                      <span className="text-xs text-neon-blue/70">Messages</span>
                    </div>
                    <div className="flex flex-col items-center justify-center bg-gray-800/50 rounded-lg p-3 text-center">
                      <span className="text-2xl font-bold text-neon-blue">{chat.chat_stats?.uniqueSessions || 0}</span>
                      <span className="text-xs text-neon-blue/70">Conversations</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-neon-blue/70 hover:text-neon-blue hover:bg-neon-blue/10"
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          // Fetch fresh stats with force refresh
                          const stats = await fetchChatStats(chat.id, true);
                          if (stats) {
                            // Update this chat's stats
                            setChatInterfaces(prev =>
                              prev.map(c => c.id === chat.id ? {
                                ...c,
                                chat_stats: {
                                  totalMessages: stats.totalMessages || 0,
                                  uniqueSessions: stats.uniqueSessions || 0,
                                  lastActivity: stats.lastActivity
                                }
                              } : c)
                            );
                            toast.success('Statistics refreshed');
                          } else {
                            toast.error('Failed to refresh statistics');
                          }
                        } catch (error) {
                          console.error('Error refreshing stats:', error);
                          toast.error('Error refreshing statistics');
                        }
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                      </svg>
                    </Button>
                  </div>
                </div>

                {/* Activity Chart */}
                <ChatActivityChart
                  totalMessages={chat.chat_stats?.totalMessages || 0}
                  uniqueSessions={chat.chat_stats?.uniqueSessions || 0}
                  lastActivity={chat.chat_stats?.lastActivity}
                  chatId={chat.id}
                />

                <div className="mt-6 flex space-x-2">
                  <Button
                   variant="outline"
                   size="sm"
                   className="transition-colors duration-300 cursor-pointer
                     hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                     focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                     active:scale-95 active:bg-cyan-700"
                   onClick={() => {
                     setEditingChat(chat)
                   }}
                 >
                   Edit
                 </Button>
                 <Button
                   variant="outline"
                   size="sm"
                   className="transition-colors duration-300 cursor-pointer
                     hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                     focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                     active:scale-95 active:bg-cyan-700"
                   onClick={() => {
                     setSelectedChatForEmbed(chat)
                     setShowEmbedModal(true)
                   }}
                 >
                   Embed Code
                 </Button>
                 <Button
                   variant="outline"
                   size="sm"
                   className="transition-colors duration-300 cursor-pointer
                     hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                     focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                     active:scale-95 active:bg-cyan-700"
                    onClick={async () => {
                      if (!chat.id) {
                        toast.error('Chat ID is missing')
                        return
                      }
                      try {
                        const response = await fetch(`/api/chat-interfaces?id=${chat.id}`, {
                          method: 'DELETE',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                        })
                        if (!response.ok) {
                          throw new Error('Failed to delete chat interface')
                        }
                        setChatInterfaces((prev) => prev.filter((c) => c.id !== chat.id))
                        toast.success('Chat interface deleted successfully')
                      } catch (error) {
                        toast.error((error as Error).message || 'Error deleting chat interface')
                      }
                    }}
                  >
                    Delete
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))
        )}
        </div>

        {/* Webhook cards */}
        {webhooks.length > 0 && (
          <>
            <h2 className="text-2xl font-bold text-neon-blue">My Webhooks</h2>
            <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
              {webhooks.map((webhook) => (
                <motion.div
                  key={webhook.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <Card className="glass-card p-6 transition-all duration-300
                    hover:shadow-glow-blue hover:bg-neon-blue/5 hover:border-neon-blue/30
                    group data-[selected=true]:bg-neon-blue/10 data-[selected=true]:border-neon-blue/50
                    active:bg-neon-blue/20">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-medium text-neon-blue">{webhook.name}</h3>
                        <div className="mt-2 p-3 bg-gray-800/50 rounded-lg">
                          <p className="text-sm text-neon-blue/70 break-all font-mono">{webhook.url}</p>
                        </div>
                        {webhook.description && (
                          <p className="text-sm mt-3 text-neon-blue/70">
                            {webhook.description}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Visual confirmation area */}
                    {webhookTestResults[webhook.id] && (
                      <div className={`mt-4 p-3 rounded-lg border ${
                        webhookTestResults[webhook.id].success
                          ? 'bg-green-900/20 border-green-500/30 text-green-400'
                          : 'bg-red-900/20 border-red-500/30 text-red-400'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {webhookTestResults[webhook.id].success ? (
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            )}
                            <span className="font-medium">
                              {webhookTestResults[webhook.id].success ? 'Test Successful' : 'Test Failed'}
                            </span>
                          </div>
                          <span className="text-xs opacity-70">{webhookTestResults[webhook.id].timestamp}</span>
                        </div>
                        <div className="mt-2 text-sm">
                          {webhookTestResults[webhook.id].message}
                        </div>
                      </div>
                    )}

                    <div className="mt-6 flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="transition-colors duration-300 cursor-pointer
                          hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                          focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                          active:scale-95 active:bg-cyan-700"
                        onClick={() => {
                          setEditingWebhook(webhook)
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={testingWebhooks[webhook.id]}
                        className="transition-colors duration-300 cursor-pointer
                          hover:text-white hover:border-white hover:bg-green-600 hover:shadow-lg hover:shadow-green-500/60
                          focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 focus:ring-offset-gray-900
                          active:scale-95 active:bg-green-700 disabled:opacity-50"
                        onClick={async () => {
                          console.log('🧪 Starting webhook test...')
                          console.log('🔗 URL:', webhook.url)
                          console.log('📦 Payload:', {
                            message: "Test message from BotFusion webhook test",
                            chatId: "webhook-test",
                            sessionId: `test-${Date.now()}`
                          })

                          // Set testing state
                          setTestingWebhooks(prev => ({ ...prev, [webhook.id]: true }))

                          try {
                            await toast.promise(
                              fetch(webhook.url, {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                  message: "Test message from BotFusion webhook test",
                                  chatId: "webhook-test",
                                  sessionId: `test-${Date.now()}`
                                })
                              }).then(async (response) => {
                                if (!response.ok) {
                                  let errorMessage = `Test failed with status ${response.status}`
                                  try {
                                    const errorData = await response.json()
                                    if (errorData.message) {
                                      errorMessage += `: ${errorData.message}`
                                    } else if (errorData.error) {
                                      errorMessage += `: ${errorData.error}`
                                    }
                                  } catch {
                                    try {
                                      const errorText = await response.text()
                                      if (errorText) {
                                        errorMessage += `: ${errorText.substring(0, 100)}`
                                      }
                                    } catch {
                                      errorMessage += ': No error details available'
                                    }
                                  }
                                  throw new Error(errorMessage)
                                }

                                // Try to get response data for additional confirmation
                                let responseData = null
                                try {
                                  responseData = await response.json()
                                  console.log('Webhook test response:', responseData)
                                } catch {
                                  console.log('Webhook test successful (non-JSON response)')
                                }

                                // Set visual confirmation
                                setWebhookTestResults(prev => ({
                                  ...prev,
                                  [webhook.id]: {
                                    success: true,
                                    message: responseData?.output || 'Webhook test successful!',
                                    timestamp: new Date().toLocaleTimeString()
                                  }
                                }))

                                return response
                              }),
                              {
                                loading: 'Testing webhook...',
                                success: 'Webhook test successful! ✅',
                                error: (error) => {
                                  const errorMsg = error instanceof Error ? error.message : 'Webhook test failed'
                                  console.error('Webhook test failed:', errorMsg)

                                  // Set visual error confirmation
                                  setWebhookTestResults(prev => ({
                                    ...prev,
                                    [webhook.id]: {
                                      success: false,
                                      message: errorMsg,
                                      timestamp: new Date().toLocaleTimeString()
                                    }
                                  }))

                                  return `❌ ${errorMsg}`
                                }
                              }
                            )
                          } catch (error) {
                            // Error already handled by toast.promise
                            console.error('Webhook test error:', error)
                          } finally {
                            // Clear testing state
                            setTestingWebhooks(prev => ({ ...prev, [webhook.id]: false }))
                          }
                        }}
                      >
                        {testingWebhooks[webhook.id] ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Testing...
                          </>
                        ) : (
                          <>
                            <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Test
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="transition-colors duration-300 cursor-pointer
                          hover:text-white hover:border-white hover:bg-cyan-600 hover:shadow-lg hover:shadow-cyan-500/60
                          focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-1 focus:ring-offset-gray-900
                          active:scale-95 active:bg-cyan-700"
                        onClick={async () => {
                          if (!webhook.id) {
                            toast.error('Webhook ID is missing');
                            return;
                          }
                          try {
                            const response = await fetch('/api/webhooks', {
                              method: 'DELETE',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify({ id: webhook.id }),
                            });

                            if (!response.ok) {
                              const errorData = await response.json();
                              throw new Error(errorData.error || 'Failed to delete webhook');
                            }

                            setWebhooks((prev) => prev.filter((w) => w.id !== webhook.id));
                            toast.success('Webhook deleted successfully');
                          } catch (error) {
                            toast.error((error as Error).message || 'Error deleting webhook');
                          }
                        }}
                      >
                        Delete
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  )
}