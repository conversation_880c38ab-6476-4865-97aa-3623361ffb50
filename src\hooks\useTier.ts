// React hooks for tier management in BotFusion X
'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { UserTier, UserProfile, TierLimits, TIER_CONFIGS } from '@/lib/tiers'
import { User } from '@supabase/supabase-js'

export interface UseTierResult {
  userTier: UserTier
  tierLimits: TierLimits
  profile: UserProfile | null
  loading: boolean
  error: string | null
  hasFeature: (feature: keyof TierLimits) => boolean
  canCreateChatInterface: boolean
  chatInterfaceCount: number
  refreshTier: () => Promise<void>
}

export function useTier(user: User | null): UseTierResult {
  const [userTier, setUserTier] = useState<UserTier>('free')
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [chatInterfaceCount, setChatInterfaceCount] = useState(0)

  const supabase = createClient()

  const fetchUserTier = useCallback(async () => {
    if (!user) {
      setUserTier('free')
      setProfile(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        if (profileError.code === 'PGRST116') {
          // Profile doesn't exist, create one
          const { data: newProfile, error: createError } = await supabase
            .from('user_profiles')
            .insert({
              id: user.id,
              tier: 'free',
              subscription_status: 'active'
            })
            .select()
            .single()

          if (createError) {
            console.error('Error creating user profile:', createError)
            setError('Failed to create user profile')
          } else {
            setProfile(newProfile)
            setUserTier('free')
          }
        } else {
          console.error('Error fetching user profile:', profileError)
          setError('Failed to fetch user profile')
        }
      } else {
        setProfile(profileData)
        setUserTier(profileData.tier as UserTier)
      }

      // Get chat interface count
      const { count, error: countError } = await supabase
        .from('chat_interfaces')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      if (countError) {
        console.error('Error counting chat interfaces:', countError)
      } else {
        setChatInterfaceCount(count || 0)
      }

    } catch (err) {
      console.error('Error in fetchUserTier:', err)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }, [user, supabase])

  useEffect(() => {
    fetchUserTier()
  }, [fetchUserTier])

  const tierLimits = TIER_CONFIGS[userTier]

  const hasFeature = useCallback((feature: keyof TierLimits): boolean => {
    return Boolean(tierLimits[feature])
  }, [tierLimits])

  const canCreateChatInterface = tierLimits.maxChatInterfaces === -1 || 
    chatInterfaceCount < tierLimits.maxChatInterfaces

  return {
    userTier,
    tierLimits,
    profile,
    loading,
    error,
    hasFeature,
    canCreateChatInterface,
    chatInterfaceCount,
    refreshTier: fetchUserTier
  }
}

// Hook for checking specific feature access
export function useFeatureAccess(user: User | null, feature: keyof TierLimits) {
  const { hasFeature, loading, userTier } = useTier(user)
  
  return {
    hasAccess: hasFeature(feature),
    loading,
    userTier,
    upgradeRequired: !hasFeature(feature) ? getUpgradeRequiredTier(feature) : null
  }
}

// Hook for voice model access
export function useVoiceModelAccess(user: User | null, voiceModel: string) {
  const { tierLimits, loading, userTier } = useTier(user)
  
  const hasAccess = tierLimits.voiceFeaturesEnabled && 
    tierLimits.voiceModels.includes(voiceModel)
  
  return {
    hasAccess,
    loading,
    userTier,
    voiceFeaturesEnabled: tierLimits.voiceFeaturesEnabled,
    availableModels: tierLimits.voiceModels,
    upgradeRequired: !hasAccess ? (userTier === 'free' ? 'standard' : 'pro') : null
  }
}

// Hook for chat interface creation
export function useChatInterfaceCreation(user: User | null) {
  const { canCreateChatInterface, chatInterfaceCount, tierLimits, loading, userTier } = useTier(user)
  
  return {
    canCreate: canCreateChatInterface,
    currentCount: chatInterfaceCount,
    maxAllowed: tierLimits.maxChatInterfaces,
    loading,
    userTier,
    upgradeRequired: !canCreateChatInterface ? 'standard' : null
  }
}

// Helper function to determine upgrade required tier
function getUpgradeRequiredTier(feature: keyof TierLimits): UserTier {
  // Features available in Standard tier
  const standardFeatures: (keyof TierLimits)[] = [
    'voiceFeaturesEnabled',
    'brandingRemoval',
    'advancedCustomization'
  ]
  
  // Features available only in Pro tier
  const proFeatures: (keyof TierLimits)[] = [
    'whiteLabelingEnabled',
    'customBrandingText',
    'analyticsDashboard',
    'prioritySupport'
  ]
  
  if (proFeatures.includes(feature)) {
    return 'pro'
  } else if (standardFeatures.includes(feature)) {
    return 'standard'
  } else {
    return 'free'
  }
}

// Context provider for tier information
import { createContext, useContext } from 'react'

const TierContext = createContext<UseTierResult | null>(null)

export function TierProvider({ children, user }: { children: React.ReactNode, user: User | null }) {
  const tierData = useTier(user)
  
  return (
    <TierContext.Provider value={tierData}>
      {children}
    </TierContext.Provider>
  )
}

export function useTierContext(): UseTierResult {
  const context = useContext(TierContext)
  if (!context) {
    throw new Error('useTierContext must be used within a TierProvider')
  }
  return context
}

// Hook for tier upgrade prompts
export function useUpgradePrompt() {
  const showUpgradePrompt = useCallback((requiredTier: UserTier, feature: string) => {
    // This could be enhanced to show a modal or redirect to upgrade page
    console.log(`Upgrade to ${requiredTier} tier required for ${feature}`)
    
    // For now, just alert - in production this would show a proper upgrade modal
    alert(`This feature requires ${requiredTier} tier. Please upgrade your account to access ${feature}.`)
  }, [])
  
  return { showUpgradePrompt }
}
