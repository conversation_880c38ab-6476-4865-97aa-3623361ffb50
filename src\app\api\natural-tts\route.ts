import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@deepgram/sdk';
import { processTextForNaturalSpeech } from '@/lib/natural-speech-utils';
import { checkVoiceModelAccess } from '@/lib/middleware/tierMiddleware';
import { createClient as createSupabaseClient } from '@/lib/supabase/server';
import { monitorAudioQuality, logAudioQuality, getQualityRecommendations } from '@/lib/audio-quality-monitor';
import { buildDeepgramTTSUrl, DEEPGRAM_HEADERS } from '@/lib/tts-config-standard';

// Deepgram configuration
const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;
const SAMPLE_RATE = 24000;
const BYTES_PER_SAMPLE = 2;
const CHUNK_DURATION_MS = 100;
const SAMPLES_PER_CHUNK = Math.floor(SAMPLE_RATE * CHUNK_DURATION_MS / 1000);
const CHUNK_SIZE_BYTES = SAMPLES_PER_CHUNK * BYTES_PER_SAMPLE;

// Request timeout configuration
const REQUEST_TIMEOUT_MS = 25000;
const MAX_RETRIES = 3;
const RETRY_DELAY_BASE = 1000;

/**
 * Enhanced TTS endpoint with natural speech processing via gpt-4o-mini
 * This endpoint processes text through gpt-4o-mini first to make it sound more natural,
 * then sends it to Deepgram TTS for high-quality speech synthesis.
 */
export async function POST(request: NextRequest) {
  try {
    const {
      text,
      voiceModel = 'aura-2-thalia-en', // Changed to female voice
      format = 'linear16',
      sampleRate = 24000,
      enableNaturalSpeech = true,
      streamResponse = false,
      naturalSpeechTemperature = 0.7
    } = await request.json();

    if (!text) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    if (!DEEPGRAM_API_KEY) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    // Check user authentication and tier permissions (optional for backward compatibility)
    const supabase = createSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    // If user is authenticated, check tier permissions
    if (user && !authError) {
      // Extract voice model name from full model string (e.g., 'aura-2-thalia-en' -> 'thalia')
      const voiceModelName = voiceModel.replace('aura-2-', '').replace('-en', '')

      // Check tier permissions for voice features
      const tierCheck = await checkVoiceModelAccess(user.id, voiceModelName)

      if (!tierCheck.allowed) {
        return NextResponse.json({
          error: 'Voice feature access denied',
          reason: tierCheck.reason,
          userTier: tierCheck.userTier,
          upgradeRequired: tierCheck.upgradeRequired,
          details: `Voice features require ${tierCheck.upgradeRequired} tier or higher.`
        }, { status: 403 })
      }
    }
    // If no user authentication, allow for backward compatibility with existing chat interfaces

    console.log('🎤 Natural TTS - Starting enhanced processing...');
    console.log('📝 Original text:', text.substring(0, 100) + '...');
    console.log('🧠 Natural speech processing:', enableNaturalSpeech ? 'ENABLED' : 'DISABLED');
    console.log('🌡️ Natural speech temperature:', naturalSpeechTemperature);
    console.log('🎵 Voice model:', voiceModel);

    // Step 1: Process text for natural speech if enabled
    let finalText = text;
    let processingResult = null;

    if (enableNaturalSpeech) {
      console.log('🧠 Processing text through GPT-4o-mini for natural speech...');
      processingResult = await processTextForNaturalSpeech(text, true, 'gpt-4o-mini', naturalSpeechTemperature);
      finalText = processingResult.processedText;
      
      if (processingResult.processed) {
        console.log('✅ Text enhanced for natural speech');
        console.log('🔄 Enhanced text:', finalText.substring(0, 100) + '...');
      } else {
        console.log('⚠️ Natural speech processing skipped:', processingResult.warning || processingResult.error);
      }
    }

    // Step 2: Generate speech with Deepgram TTS
    console.log('🎵 Generating speech with Deepgram TTS...');
    const ttsStartTime = Date.now();

    if (streamResponse) {
      // Return streaming response for real-time playback
      return createStreamingResponse(finalText, voiceModel, format, sampleRate, processingResult, naturalSpeechTemperature, ttsStartTime);
    } else {
      // Return complete audio buffer
      return createBufferedResponse(finalText, voiceModel, format, sampleRate, processingResult, naturalSpeechTemperature, ttsStartTime);
    }

  } catch (error) {
    console.error('Natural TTS error:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Create a streaming TTS response with natural speech processing
 */
async function createStreamingResponse(
  text: string,
  voiceModel: string,
  format: string,
  sampleRate: number,
  processingResult: any,
  temperature: number,
  startTime: number
) {
  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send processing info
        const processingInfo = JSON.stringify({
          type: 'processing_info',
          naturalSpeechProcessed: processingResult?.processed || false,
          tokensUsed: processingResult?.tokensUsed || 0,
          originalLength: processingResult?.originalText?.length || 0,
          processedLength: text.length
        });
        controller.enqueue(encoder.encode(`data: ${processingInfo}\n\n`));

        // Generate TTS with standardized configuration
        const response = await fetch(buildDeepgramTTSUrl(voiceModel, format as 'linear16' | 'mp3'), {
          method: 'POST',
          headers: {
            ...DEEPGRAM_HEADERS,
            'Authorization': `Token ${DEEPGRAM_API_KEY}`,
            'Accept': 'audio/wav'
          },
          body: JSON.stringify({ text })
        });

        if (!response.ok) {
          throw new Error(`Deepgram TTS failed: ${response.status}`);
        }

        const audioBuffer = await response.arrayBuffer();
        const audioArray = new Uint8Array(audioBuffer);

        // Stream audio in chunks
        for (let i = 0; i < audioArray.length; i += CHUNK_SIZE_BYTES) {
          const chunk = audioArray.slice(i, i + CHUNK_SIZE_BYTES);
          const chunkData = JSON.stringify({
            type: 'audio_chunk',
            data: Array.from(chunk),
            chunkIndex: Math.floor(i / CHUNK_SIZE_BYTES),
            isLast: i + CHUNK_SIZE_BYTES >= audioArray.length
          });
          controller.enqueue(encoder.encode(`data: ${chunkData}\n\n`));
        }

        // Send completion
        const completionData = JSON.stringify({
          type: 'complete',
          totalChunks: Math.ceil(audioArray.length / CHUNK_SIZE_BYTES),
          totalBytes: audioArray.length
        });
        controller.enqueue(encoder.encode(`data: ${completionData}\n\n`));

      } catch (error) {
        const errorData = JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
      } finally {
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

/**
 * Create a buffered TTS response with natural speech processing
 */
async function createBufferedResponse(
  text: string,
  voiceModel: string,
  format: string,
  sampleRate: number,
  processingResult: any,
  temperature: number,
  startTime: number
) {
  try {
    const response = await fetch(`https://api.deepgram.com/v1/speak?model=${encodeURIComponent(voiceModel)}&encoding=${format}&sample_rate=${sampleRate}&smart_format=true`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        'Accept': 'audio/wav'
      },
      body: JSON.stringify({ text })
    });

    if (!response.ok) {
      throw new Error(`Deepgram TTS failed: ${response.status}`);
    }

    const audioBuffer = await response.arrayBuffer();
    const processingTime = Date.now() - startTime;

    // Monitor audio quality for potential voice tearing
    const qualityMetrics = monitorAudioQuality(
      processingResult?.originalText || text,
      temperature,
      voiceModel,
      processingTime,
      audioBuffer.byteLength,
      text
    );

    logAudioQuality(qualityMetrics);
    const recommendations = getQualityRecommendations(qualityMetrics);

    return new Response(audioBuffer, {
      headers: {
        'Content-Type': 'audio/wav',
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'X-Natural-Speech-Processed': processingResult?.processed ? 'true' : 'false',
        'X-Tokens-Used': processingResult?.tokensUsed?.toString() || '0',
        'X-Original-Length': processingResult?.originalText?.length?.toString() || '0',
        'X-Processed-Length': text.length.toString(),
        'X-Quality-Score': qualityMetrics.qualityScore.toString(),
        'X-Temperature': temperature.toString(),
        'X-Processing-Time': processingTime.toString(),
        'X-Potential-Issues': qualityMetrics.potentialIssues.length.toString(),
        'X-Quality-Recommendations': recommendations.length > 0 ? recommendations[0] : 'None'
      }
    });

  } catch (error) {
    console.error('Buffered TTS generation failed:', error);
    throw error;
  }
}

// GET endpoint for testing
export async function GET(request: NextRequest) {
  const testText = request.nextUrl.searchParams.get('text') || 
    'Hello! This is a test of the natural text-to-speech system. It uses GPT-4o-mini to make the text sound more natural before converting it to speech.';
  
  const enableNaturalSpeech = request.nextUrl.searchParams.get('natural') !== 'false';

  try {
    const response = await fetch(request.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        text: testText, 
        enableNaturalSpeech,
        streamResponse: false
      }),
    });

    if (response.ok) {
      const audioBuffer = await response.arrayBuffer();
      return new Response(audioBuffer, {
        headers: {
          'Content-Type': 'audio/wav',
          'Content-Disposition': 'attachment; filename="natural-tts-test.wav"'
        }
      });
    } else {
      const error = await response.json();
      return NextResponse.json({ error: 'Test failed', details: error });
    }

  } catch (error) {
    return NextResponse.json({
      error: 'Test failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
