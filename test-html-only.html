<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HTML-Only Embed</title>
    <!-- This CSP allows inline styles but no scripts -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'none'; style-src 'self' 'unsafe-inline'; frame-src https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Test HTML-Only Embed</h1>

    <div class="note">
        <strong>Important:</strong> This page has a Content Security Policy that blocks all scripts:
        <pre>script-src 'none';</pre>
        The HTML-only embed should work because it uses JavaScript only inside the iframe.
    </div>

    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget.</p>
        <p>Scroll down to see more content...</p>

        <div style="height: 500px;"></div>

        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
    </div>

    <!-- HTML-Only Embed - This should work with script-src 'none' CSP -->
    <iframe
      src="https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app/api/html-embed?chatId=b76a3980-9f8e-47cd-ae7d-f02747552c4d"
      style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
      frameborder="0"
      title="Chat Widget"
      loading="lazy"
      allow="microphone"
    ></iframe>
</body>
</html>
