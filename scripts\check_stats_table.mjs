import { createClient } from '../src/lib/supabase/server.ts'

async function checkStatsTable() {
  const supabase = createClient({
    cookies: () => ({ get: () => null, set: () => {} })
  })
  const { data, error } = await supabase
    .from('pg_tables')
    .select('*')
    .eq('tablename', 'chat_stats')

  if (error) {
    console.error('Error checking table:', error)
    return
  }

  console.log('Table exists:', data.length > 0)
  if (data.length > 0) {
    console.log('Table details:', data[0])
  }
}

checkStatsTable().catch(console.error)