<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Embedding Test - All Methods</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .method {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .method h2 {
            margin-top: 0;
        }
        .iframe-container {
            width: 400px;
            height: 600px;
            margin: 20px auto;
            border: 1px solid #ddd;
            border-radius: 12px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .code {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        #console-output {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin-top: 10px;
        }
        .log {
            margin: 5px 0;
            padding: 3px 0;
            border-bottom: 1px solid #ddd;
        }
        .error { color: #d32f2f; }
        .warn { color: #f57c00; }
        .info { color: #0288d1; }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #eee;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            z-index: 1;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 4px 4px 4px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>BotFusion Embedding Test - All Methods</h1>

    <div class="important">
        <strong>Important:</strong> This page tests all embedding methods for BotFusion Chat. Please open the browser console (F12) to see any errors.
    </div>

    <div class="tabs">
        <div class="tab active" onclick="openTab(event, 'method1')">Method 1: Direct Script</div>
        <div class="tab" onclick="openTab(event, 'method2')">Method 2: Simple Iframe</div>
        <div class="tab" onclick="openTab(event, 'method3')">Method 3: Custom Button</div>
        <div class="tab" onclick="openTab(event, 'debug')">Debug Info</div>
    </div>

    <div id="method1" class="tab-content active">
        <h2>Method 1: Direct Script Embed</h2>
        <p>This method uses a script tag with data attributes to embed the chat widget.</p>
        
        <div class="code">
            &lt;script 
                src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
                data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
                async 
                defer
            &gt;&lt;/script&gt;
        </div>
        
        <p>The chat widget should appear as a floating button in the bottom-right corner of the page.</p>
    </div>

    <div id="method2" class="tab-content">
        <h2>Method 2: Simple Iframe Embed</h2>
        <p>This method embeds the chat directly in an iframe on the page.</p>
        
        <div class="iframe-container">
            <iframe 
                src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                allow="microphone" 
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                loading="lazy"
                importance="high"
                referrerpolicy="origin"
                fetchpriority="high"
                title="BotFusion Chat"
            ></iframe>
        </div>
        
        <div class="code">
            &lt;iframe 
                src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                allow="microphone" 
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                loading="lazy"
                importance="high"
                referrerpolicy="origin"
                fetchpriority="high"
                title="BotFusion Chat"
                style="width: 400px; height: 600px; border: none; border-radius: 12px;"
            &gt;&lt;/iframe&gt;
        </div>
    </div>

    <div id="method3" class="tab-content">
        <h2>Method 3: Custom Button with Iframe</h2>
        <p>This method creates a custom button that opens a chat iframe when clicked.</p>
        
        <button id="custom-chat-button" style="padding: 10px 20px; background-color: #0070f3; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Open Chat
        </button>
        
        <div id="custom-chat-container" style="display: none; position: fixed; bottom: 20px; right: 20px; width: 350px; height: 500px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.15); z-index: 1000;">
            <div style="display: flex; justify-content: space-between; align-items: center; background-color: #0070f3; color: white; padding: 10px 15px;">
                <div>BotFusion Chat</div>
                <div id="custom-chat-close" style="cursor: pointer; font-size: 20px;">×</div>
            </div>
            <iframe 
                id="custom-chat-iframe"
                src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                allow="microphone" 
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                loading="lazy"
                importance="high"
                referrerpolicy="origin"
                fetchpriority="high"
                title="BotFusion Chat"
                style="width: 100%; height: calc(100% - 40px); border: none;"
            ></iframe>
        </div>
        
        <div class="code">
            &lt;button id="custom-chat-button"&gt;Open Chat&lt;/button&gt;
            
            &lt;div id="custom-chat-container" style="display: none;"&gt;
                &lt;div&gt;
                    &lt;div&gt;BotFusion Chat&lt;/div&gt;
                    &lt;div id="custom-chat-close"&gt;×&lt;/div&gt;
                &lt;/div&gt;
                &lt;iframe 
                    id="custom-chat-iframe"
                    src="https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
                    allow="microphone" 
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                &gt;&lt;/iframe&gt;
            &lt;/div&gt;
            
            &lt;script&gt;
                document.getElementById('custom-chat-button').addEventListener('click', function() {
                    document.getElementById('custom-chat-container').style.display = 'block';
                });
                
                document.getElementById('custom-chat-close').addEventListener('click', function() {
                    document.getElementById('custom-chat-container').style.display = 'none';
                });
            &lt;/script&gt;
        </div>
    </div>

    <div id="debug" class="tab-content">
        <h2>Debug Information</h2>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Direct Script Embed -->
    <script 
        src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
        data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
        data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
        async 
        defer
    ></script>

    <!-- Custom Button Script -->
    <script>
        document.getElementById('custom-chat-button').addEventListener('click', function() {
            document.getElementById('custom-chat-container').style.display = 'block';
        });
        
        document.getElementById('custom-chat-close').addEventListener('click', function() {
            document.getElementById('custom-chat-container').style.display = 'none';
        });
        
        // Tab functionality
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            
            // Hide all tab content
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].className = tabcontent[i].className.replace(" active", "");
            }
            
            // Remove active class from all tabs
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            
            // Show the current tab and add active class
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }

        // Debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        
        // Store original console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods
        console.log = function() {
            // Call original method
            originalConsole.log.apply(console, arguments);
            
            // Add to our display
            const logElement = document.createElement('div');
            logElement.className = 'log';
            logElement.textContent = Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.error = function() {
            originalConsole.error.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log error';
            logElement.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.warn = function() {
            originalConsole.warn.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log warn';
            logElement.textContent = 'WARNING: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.info = function() {
            originalConsole.info.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log info';
            logElement.textContent = 'INFO: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        // Log script loading
        console.info('Page loaded, waiting for BotFusion script to initialize...');

        // Check if script loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.BotFusionChat) {
                    console.info('BotFusion script loaded successfully!');
                } else {
                    console.error('BotFusion script not found after 3 seconds');
                }
            }, 3000);
        });
    </script>
</body>
</html>
