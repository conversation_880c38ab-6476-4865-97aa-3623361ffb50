# Tier System Testing Checklist

This comprehensive checklist ensures all tier-based features work correctly before deployment.

## Pre-Testing Setup

### ✅ Database Migrations
- [ ] Run all tier system migrations
- [ ] Verify `user_profiles` table exists with tier column
- [ ] Verify `tier_features` table is populated
- [ ] Verify RLS policies are active
- [ ] Test database functions (`can_create_chat_interface`, etc.)

### ✅ Test Accounts
- [ ] Create auth users for all 3 test accounts
- [ ] Run test account SQL script
- [ ] Verify test accounts appear in admin dashboard
- [ ] Confirm each account has appropriate tier assignment

## Tier Restriction Testing

### 🆓 Free Tier Testing (`<EMAIL>`)

#### Chat Interface Limits
- [ ] Login as free tier user
- [ ] Verify dashboard shows "Create Chat (1/1)" 
- [ ] Try to create 2nd chat interface → Should fail with upgrade prompt
- [ ] Verify error message mentions tier upgrade requirement

#### Voice Features
- [ ] Open chat interface form
- [ ] Navigate to Speech tab
- [ ] Verify voice features show "Requires Standard tier" message
- [ ] Verify all voice options are disabled/grayed out
- [ ] Verify voice preview button is disabled

#### Branding Controls
- [ ] Open chat interface form → Appearance → General
- [ ] Verify "Show Powered By" toggle is disabled
- [ ] Verify toggle shows "(Required for free tier)"
- [ ] Verify custom branding text/URL fields are disabled
- [ ] Verify upgrade prompts explain tier requirements

#### Advanced Customization
- [ ] Verify gradient header toggle is disabled
- [ ] Verify bubble color pickers are disabled
- [ ] Verify text color pickers are disabled
- [ ] Verify upgrade prompts show for all advanced features

#### Chat Interface Functionality
- [ ] Test existing chat interface works normally
- [ ] Verify "Powered by BotFusion X" appears and links correctly
- [ ] Verify no voice features are available in chat
- [ ] Test basic text chat functionality

### ⭐ Standard Tier Testing (`<EMAIL>`)

#### Chat Interface Limits
- [ ] Login as standard tier user
- [ ] Verify dashboard shows "Create Chat" without limits
- [ ] Create multiple chat interfaces → Should succeed
- [ ] Verify no limit warnings or restrictions

#### Voice Features
- [ ] Open chat interface form → Speech tab
- [ ] Verify voice features are enabled
- [ ] Verify 3 voice models available (thalia, asteria, helena)
- [ ] Verify 3 premium voices show "Pro tier" badges
- [ ] Test voice preview functionality
- [ ] Verify natural speech options are disabled (Pro only)

#### Branding Controls
- [ ] Verify "Show Powered By" toggle is enabled
- [ ] Test toggling branding on/off → Should work
- [ ] Verify custom text field is disabled (Pro only)
- [ ] Verify custom URL field is disabled (Pro only)
- [ ] Verify default "Powered by BotFusion X" text when enabled

#### Advanced Customization
- [ ] Verify gradient header toggle works
- [ ] Test gradient color pickers → Should work
- [ ] Test bubble color customization → Should work
- [ ] Test text color customization → Should work
- [ ] Verify all advanced styling options are available

#### Chat Interface Functionality
- [ ] Test voice features in chat interface
- [ ] Verify TTS works with available voice models
- [ ] Verify STT functionality works
- [ ] Test branding removal works correctly
- [ ] Test advanced styling appears correctly

### 💎 Pro Tier Testing (`<EMAIL>`)

#### Chat Interface Limits
- [ ] Login as pro tier user
- [ ] Verify unlimited chat interface creation
- [ ] Create multiple interfaces → Should succeed without limits

#### Voice Features
- [ ] Verify all 6 voice models are available
- [ ] Test all voice models (thalia, asteria, helena, arcas, apollo, zeus)
- [ ] Verify natural speech processing options are enabled
- [ ] Test natural speech with different AI models
- [ ] Verify voice preview works for all models

#### White-Labeling & Custom Branding
- [ ] Verify "Show Powered By" toggle works
- [ ] Test custom branding text → Should accept any text
- [ ] Test custom branding URL → Should accept any URL
- [ ] Test complete branding removal → Should work
- [ ] Verify custom branding appears correctly in chat

#### Advanced Features
- [ ] Verify all customization options are available
- [ ] Test analytics dashboard access (if implemented)
- [ ] Verify priority support indicators
- [ ] Test all Pro-exclusive features

#### Chat Interface Functionality
- [ ] Test all voice models in live chat
- [ ] Test natural speech processing
- [ ] Verify custom branding displays correctly
- [ ] Test white-labeled interface (no BotFusion branding)

## API Endpoint Testing

### Authentication & Authorization
- [ ] Test `/api/chat-interfaces` POST without auth → Should return 401
- [ ] Test voice APIs without auth → Should return 401
- [ ] Test tier checking middleware works correctly

### Chat Interface Creation
- [ ] Test free tier limit enforcement via API
- [ ] Test branding enforcement for each tier
- [ ] Test voice model restrictions via API
- [ ] Verify proper error messages for tier violations

### Voice API Restrictions
- [ ] Test `/api/natural-tts` with free tier → Should return 403
- [ ] Test voice model access with standard tier → Should allow 3 models
- [ ] Test voice model access with pro tier → Should allow all models
- [ ] Test STT API restrictions work correctly

## UI/UX Testing

### Upgrade Prompts
- [ ] Verify upgrade prompts appear for restricted features
- [ ] Test upgrade prompt styling and messaging
- [ ] Verify upgrade buttons link to pricing page
- [ ] Test pricing page displays correctly

### Visual Indicators
- [ ] Verify tier badges display correctly
- [ ] Test disabled state styling for restricted features
- [ ] Verify tooltips explain tier requirements
- [ ] Test responsive design on mobile devices

### Admin Dashboard
- [ ] Test tier management dashboard functionality
- [ ] Verify user tier updates work correctly
- [ ] Test feature comparison matrix displays properly
- [ ] Verify admin controls work as expected

## Integration Testing

### End-to-End Workflows
- [ ] Complete chat interface creation flow for each tier
- [ ] Test voice feature usage in live chat interfaces
- [ ] Verify branding appears correctly in embedded widgets
- [ ] Test tier restrictions in iframe embeds

### Cross-Browser Testing
- [ ] Test in Chrome, Firefox, Safari, Edge
- [ ] Verify voice features work across browsers
- [ ] Test responsive design on different screen sizes
- [ ] Verify tier restrictions work consistently

## Performance Testing

### Load Testing
- [ ] Test tier checking performance with multiple users
- [ ] Verify database queries are optimized
- [ ] Test voice API performance under load
- [ ] Monitor response times for tier-restricted features

### Caching & Optimization
- [ ] Verify tier data is cached appropriately
- [ ] Test tier checking doesn't slow down UI
- [ ] Verify voice model restrictions are efficient

## Security Testing

### Access Control
- [ ] Verify users cannot bypass tier restrictions via API
- [ ] Test that tier data cannot be manipulated client-side
- [ ] Verify proper authentication for all tier-restricted features
- [ ] Test that voice APIs properly validate user permissions

### Data Protection
- [ ] Verify user tier data is properly protected
- [ ] Test that test accounts don't affect production data
- [ ] Verify proper isolation between user tiers

## Deployment Checklist

### Pre-Deployment
- [ ] All tests pass successfully
- [ ] Database migrations are ready for production
- [ ] Environment variables are configured
- [ ] Backup current production database

### Deployment Steps
- [ ] Deploy database migrations
- [ ] Deploy application code
- [ ] Verify deployment health checks
- [ ] Test critical paths in production

### Post-Deployment Verification
- [ ] Test tier system works in production
- [ ] Verify existing users are not affected
- [ ] Test new user registration assigns correct default tier
- [ ] Monitor for any errors or issues

### Rollback Plan
- [ ] Document rollback procedures
- [ ] Prepare database rollback scripts if needed
- [ ] Monitor system for 24 hours post-deployment
- [ ] Have team available for immediate fixes

## Success Criteria

✅ **All tier restrictions work correctly**
✅ **No existing functionality is broken**
✅ **Performance remains acceptable**
✅ **Security is maintained**
✅ **User experience is smooth**
✅ **Admin tools function properly**

## Notes

- Test with real user scenarios, not just happy paths
- Pay special attention to edge cases and error handling
- Verify that tier changes take effect immediately
- Ensure upgrade prompts are helpful and not annoying
- Test that the system gracefully handles missing tier data

## Sign-off

- [ ] Developer Testing Complete
- [ ] QA Testing Complete  
- [ ] Security Review Complete
- [ ] Performance Review Complete
- [ ] Ready for Production Deployment
