import { readFileSync } from 'fs';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';

// This is a special API route that serves the embed script with proper CORS headers
// It's designed to be accessible from any origin
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId') || '';

    // Get the origin for the API
    const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app';

    // Read the embed script from the public directory
    const scriptPath = path.join(process.cwd(), 'public', 'new-embed-script.js');
    let scriptContent = readFileSync(scriptPath, 'utf8');

    // Replace the origin placeholder
    scriptContent = scriptContent.replace('ORIGIN_PLACEHOLDER', origin);

    // Add the initialization code if chatId is provided
    if (chatId) {
      // Get other parameters from the query string
      const primaryColor = request.nextUrl.searchParams.get('primaryColor') || '#3b82f6';
      const userBubbleColor = request.nextUrl.searchParams.get('userBubbleColor') || '#ffffff';
      const botBubbleColor = request.nextUrl.searchParams.get('botBubbleColor') || '#3b82f6';
      const userTextColor = request.nextUrl.searchParams.get('userTextColor') || '#000000';
      const botTextColor = request.nextUrl.searchParams.get('botTextColor') || '#ffffff';
      const logoUrl = request.nextUrl.searchParams.get('logoUrl') || '';
      const darkMode = request.nextUrl.searchParams.get('darkMode') === 'true';
      const greeting = request.nextUrl.searchParams.get('greeting') || 'Hello! How can I help you today?';

      // Add the initialization code
      scriptContent += `
bf('init', {
    chatId: "${chatId}",
    position: 'right',
    primaryColor: "${primaryColor}",
    userBubbleColor: "${userBubbleColor}",
    botBubbleColor: "${botBubbleColor}",
    userTextColor: "${userTextColor}",
    botTextColor: "${botTextColor}",
    logoUrl: "${logoUrl}",
    darkMode: ${darkMode},
    greeting: "${greeting}"
});`;
    }

    // Return the script with proper CORS headers
    return new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    });
  } catch (error) {
    console.error('Error serving embed script:', error);
    return new NextResponse('console.error("Error loading BotFusion Chat Widget");', {
      status: 500,
      headers: {
        'Content-Type': 'application/javascript; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
      },
    });
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self'; frame-ancestors *; font-src 'self' data:;",
    },
  });
}
