import React from 'react';
import Script from 'next/script';

export default function WidgetTestPage() {
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">BotFusion Widget Test Page</h1>
      
      <div className="grid gap-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-blue-600">Widget Embed Test</h2>
          <p className="mb-4">This page tests the BotFusion chat widget embedding.</p>
          
          <div className="bg-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto mb-4">
            {`<script src="/api/embed-widget"></script>`}
          </div>
          
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <strong className="font-medium">Note:</strong> The chat button should appear in the bottom-right corner of this page.
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-blue-600">Iframe Embed Test</h2>
          <p className="mb-4">This method embeds the chat directly in your page using an iframe.</p>
          
          <div className="w-full h-[500px] border border-gray-200 rounded-lg overflow-hidden mb-4">
            <iframe 
              src="/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
              allow="microphone" 
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
              loading="lazy"
              importance="high"
              referrerPolicy="origin"
              fetchPriority="high"
              title="BotFusion Chat"
              className="w-full h-full border-none"
            ></iframe>
          </div>
        </div>
      </div>

      {/* Widget Embed Script */}
      <Script src="/api/embed-widget" strategy="afterInteractive" />
    </div>
  );
}
