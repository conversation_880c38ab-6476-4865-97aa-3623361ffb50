import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

export async function GET(request: NextRequest) {
  try {
    // Get the chat ID from the query parameters
    const chatId = request.nextUrl.searchParams.get('chatId')
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
    }

    // Get the origin for the API
    const origin = process.env.NEXT_PUBLIC_SITE_URL || 'https://roo-bot-fusion-kgfs-l20g3q8lq-tellivisions-projects.vercel.app'

    // Get the chat interface details from the database
    const supabase = createServiceClient()
    const { data, error } = await supabase
      .from('chat_interfaces')
      .select('*')
      .eq('id', chatId)
      .single()

    if (error) {
      console.error('Error fetching chat interface:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!data) {
      return NextResponse.json({ error: 'Chat interface not found' }, { status: 404 })
    }

    // Get parameters from the query string or use defaults from the database
    const primaryColor = request.nextUrl.searchParams.get('primaryColor') || data.primary_color || '#3b82f6';

    // Generate the HTML for the iframe embed - NO JAVASCRIPT AT ALL
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BotFusion Chat Button</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
    }

    /* This route is deprecated - use main embed interface */
  </style>
</head>
<body>
  <!-- This route is deprecated - use the main embed interface which has built-in toggle functionality -->
  <div style="display: none;">This embed method is no longer supported. Please use the main embed interface.</div>
</body>
</html>`;

    // Return the HTML with proper headers
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Access-Control-Allow-Credentials': 'false',
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': "default-src 'none'; style-src 'unsafe-inline'; img-src data:; frame-ancestors *;",
        'Cross-Origin-Embedder-Policy': 'unsafe-none',
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      }
    })
  } catch (error) {
    console.error('Error serving pure HTML embed:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function OPTIONS() {
  // Handle OPTIONS requests for CORS preflight
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, Content-Type, Accept',
      'Access-Control-Max-Age': '86400',
      'X-Frame-Options': 'ALLOWALL',
      'Content-Security-Policy': "default-src 'none'; style-src 'unsafe-inline'; img-src data:; frame-ancestors *;",
    },
  });
}
