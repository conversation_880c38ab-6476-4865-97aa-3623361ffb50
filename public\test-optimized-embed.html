<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
        }
        #console-output {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin-top: 10px;
        }
        .log {
            margin: 5px 0;
            padding: 3px 0;
            border-bottom: 1px solid #ddd;
        }
        .error { color: #d32f2f; }
        .warn { color: #f57c00; }
        .info { color: #0288d1; }
    </style>
</head>
<body>
    <h1>Optimized Embed Test</h1>

    <div class="important">
        <strong>Important:</strong> This page is for testing the optimized embed approach. Please open the browser console (F12) to see any errors.
    </div>

    <div class="debug-info">
        <h3>Debug Information</h3>
        <p>Current URL: <span id="current-url"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <div id="console-output"></div>
    </div>

    <!-- Direct Script Embed -->
    <script 
        src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-script" 
        data-chat-id="b76a3980-9f8e-47cd-ae7d-f02747552c4d" 
        data-base-url="https://roo-bot-fusion-kgfs.vercel.app" 
        async 
        defer
    ></script>

    <!-- Debug Script -->
    <script>
        // Display debug information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        
        // Store original console methods
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // Override console methods
        console.log = function() {
            // Call original method
            originalConsole.log.apply(console, arguments);
            
            // Add to our display
            const logElement = document.createElement('div');
            logElement.className = 'log';
            logElement.textContent = Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.error = function() {
            originalConsole.error.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log error';
            logElement.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.warn = function() {
            originalConsole.warn.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log warn';
            logElement.textContent = 'WARNING: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        console.info = function() {
            originalConsole.info.apply(console, arguments);
            
            const logElement = document.createElement('div');
            logElement.className = 'log info';
            logElement.textContent = 'INFO: ' + Array.from(arguments).join(' ');
            consoleOutput.appendChild(logElement);
        };

        // Log script loading
        console.info('Page loaded, waiting for BotFusion script to initialize...');

        // Check if script loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.BotFusionChat) {
                    console.info('BotFusion script loaded successfully!');
                } else {
                    console.error('BotFusion script not found after 3 seconds');
                }
            }, 3000);
        });
    </script>
</body>
</html>
