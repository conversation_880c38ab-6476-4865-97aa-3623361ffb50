'use client'

import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { signOut } from '@/lib/supabase/auth'
import UserProfile from '@/components/UserProfile'
import { User } from '@supabase/supabase-js'

export default function Sidebar({ user }: { user: User | null }) {
  const router = useRouter()

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push('/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      className="hidden w-64 border-r glass-card md:block"
    >
      <div className="flex h-full flex-col p-4">
        {/* Logo and Title */}
        <div className="flex items-center mb-8">
          <img src="/logo.png" alt="BotFusion X Logo" className="w-10 h-10 mr-2 drop-shadow-glow" />
          <h1 className="text-xl font-bold text-neon-blue">BotFusion X</h1>
        </div>

        {/* Navigation */}
        <nav className="mb-6">
          <Link href="/dashboard" className="block px-3 py-2 text-white font-medium text-lg">
            Dashboard
          </Link>
        </nav>

        {/* User Profile */}
        <div className="mb-4">
          <UserProfile user={user} />
        </div>

        {/* User Email and Sign Out - positioned at the bottom */}
        <div className="mt-auto pt-4">
          {user && (
            <div className="text-center mb-3 p-3 glass-card rounded-lg">
              <p className="text-xs text-white mb-1">Signed in as:</p>
              <p className="text-sm text-white font-medium truncate">{user.email}</p>
            </div>
          )}

          {/* Sign Out Button */}
          <button
            onClick={handleSignOut}
            className="w-full glass-card border border-neon-blue hover:shadow-glow-blue py-2 rounded text-neon-blue font-semibold"
          >
            Sign Out
          </button>
        </div>
      </div>
    </motion.div>
  )
}