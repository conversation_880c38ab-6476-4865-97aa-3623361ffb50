-- Create chat_interfaces table
CREATE TABLE IF NOT EXISTS chat_interfaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  webhookUrl TEXT,
  createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set permissions
ALTER TABLE chat_interfaces ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON chat_interfaces
  FOR SELECT USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON chat_interfaces
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update access for authenticated users" ON chat_interfaces
  FOR UPDATE USING (auth.role() = 'authenticated');