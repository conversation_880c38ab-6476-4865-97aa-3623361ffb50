<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Chat Widget Toggle Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .features {
            margin-bottom: 30px;
        }
        .features h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .features li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .note strong {
            color: #007bff;
        }
        .feature-highlight {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .feature-highlight strong {
            color: #b8860b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 BotFusion Chat Widget Toggle Test</h1>

        <div class="feature-highlight">
            <strong>🎉 NEW FEATURE:</strong> Chat button now toggles open/close for better mobile UX!<br>
            <strong>✅ Removed:</strong> Small X button that was hard to click on mobile<br>
            <strong>✅ Added:</strong> Click the same chat bubble to open AND close the chat
        </div>

        <div class="description">
            This page tests the NEW toggle functionality for the BotFusion chat widget.
            The chat button now acts as a toggle - click once to open, click again to close!
        </div>

        <div class="features">
            <h2>🆕 New Toggle Features to Test:</h2>
            <ul>
                <li>Chat button appears with proper SVG icon</li>
                <li>Single click opens the chat interface</li>
                <li><strong>NO X button in chat header (REMOVED!)</strong></li>
                <li><strong>Click same button again to close chat (NEW!)</strong></li>
                <li>Button remains visible when chat is open</li>
                <li>Smooth animations for both open and close</li>
                <li>Works great on mobile devices</li>
            </ul>
        </div>

        <div class="note">
            <strong>📱 Mobile UX Improvement:</strong>
            This toggle functionality makes the chat widget much more mobile-friendly by using a larger,
            easier-to-tap button instead of the tiny X button. This follows standard chat widget UX patterns
            and provides a better user experience on touch devices.
        </div>

        <div class="features">
            <h2>🧪 Test Instructions:</h2>
            <ul>
                <li>Look for the blue chat button in bottom-right corner</li>
                <li>Click to open - verify smooth animation</li>
                <li>Notice there's NO X button in the header</li>
                <li>Click the same blue button to close</li>
                <li>Test multiple open/close cycles</li>
                <li>Try on mobile for the improved touch experience</li>
            </ul>
        </div>
    </div>

    <!-- BotFusion Chat Widget - NEW CSP EMBED WITH TOGGLE FUNCTIONALITY -->
    <script src="https://roo-bot-fusion-kgfs.vercel.app/api/csp-embed-script?chatId=b76a3980-9f8e-47cd-ae7d-f02747552c4d"></script>
</body>
</html>
