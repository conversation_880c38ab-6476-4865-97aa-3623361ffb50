<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Widget Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .features {
            margin-bottom: 30px;
        }
        .features h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .features li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .note strong {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chat Widget Test Page</h1>

        <div class="description">
            This page tests the BotFusion chat widget. You should see a blue chat button in the bottom-right corner.
        </div>

        <div class="features">
            <h2>Features to Test:</h2>
            <ul>
                <li>Chat button appears with proper SVG icon</li>
                <li>Single click opens the chat interface</li>
                <li>Click the X button in chat header to close the chat</li>
                <li>Chat loads immediately without blue bar</li>
                <li>Smooth animations and transitions</li>
            </ul>
        </div>

        <div class="note">
            <strong>Note:</strong> This is the reverted version before the close function was added.
            The chat should open when you click the blue button, and close when you click the X button in the chat header.
        </div>
    </div>

    <!-- BotFusion Chat Widget - REVERTED TO WORKING VERSION BEFORE CLOSE FUNCTION -->
    <script src="https://roo-bot-fusion-kgfs.vercel.app/api/embed-widget?chatId=c1e3b096-f902-424c-b51e-94a3b34a04a0"></script>
</body>
</html>
