import type { <PERSON>ada<PERSON> } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "../../app/globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Speech Recognition Iframe Test",
  description: "Iframe test page for speech recognition functionality",
};

export default function TestSpeechIframeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Add CSP meta tag to allow embedding */}
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: data: https: mediastream:; worker-src 'self' blob:;"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
