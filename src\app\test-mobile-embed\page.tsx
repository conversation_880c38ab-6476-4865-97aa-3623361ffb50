'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function TestMobileEmbedPage() {
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [embedCode, setEmbedCode] = useState('')

  useEffect(() => {
    // Generate test embed code
    const testChatId = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d' // Default test chat
    const baseUrl = window.location.origin
    
    const mobileOptimizedCode = `<!-- BotFusion Mobile-Optimized Chat Widget -->
<script type="text/javascript">
window.BOTFUSION_CHAT_ID = '${testChatId}';
window.BOTFUSION_BASE_URL = '${baseUrl}';
window.BOTFUSION_PRIMARY_COLOR = '#3b82f6';
window.BOTFUSION_DEBUG = true;

(function() {
    var script = document.createElement('script');
    script.src = '${baseUrl}/mobile-optimized-embed.js?v=2.0';
    script.async = true;
    document.head.appendChild(script);
})();
</script>`

    setEmbedCode(mobileOptimizedCode)
  }, [])

  const runMobileTests = async () => {
    setIsLoading(true)
    const results = []

    // Test 1: Mobile Detection
    const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    results.push({
      test: 'Mobile Detection',
      status: isMobile ? 'PASS' : 'INFO',
      message: isMobile ? 'Mobile device detected' : 'Desktop device detected',
      details: `Screen width: ${window.innerWidth}px, User agent: ${navigator.userAgent.substring(0, 50)}...`
    })

    // Test 2: Touch Events Support
    const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    results.push({
      test: 'Touch Events Support',
      status: touchSupport ? 'PASS' : 'WARN',
      message: touchSupport ? 'Touch events supported' : 'Touch events not supported',
      details: `Max touch points: ${navigator.maxTouchPoints || 0}`
    })

    // Test 3: Viewport Configuration
    const viewportMeta = document.querySelector('meta[name="viewport"]')
    results.push({
      test: 'Viewport Configuration',
      status: viewportMeta ? 'PASS' : 'FAIL',
      message: viewportMeta ? 'Viewport meta tag found' : 'Viewport meta tag missing',
      details: viewportMeta ? (viewportMeta as HTMLMetaElement).content : 'No viewport meta tag'
    })

    // Test 4: CSS Touch Action Support
    const testDiv = document.createElement('div')
    testDiv.style.touchAction = 'manipulation'
    const touchActionSupported = testDiv.style.touchAction === 'manipulation'
    results.push({
      test: 'CSS Touch Action Support',
      status: touchActionSupported ? 'PASS' : 'WARN',
      message: touchActionSupported ? 'Touch action CSS supported' : 'Touch action CSS not supported',
      details: `Browser supports touch-action: ${touchActionSupported}`
    })

    // Test 5: Scroll Behavior
    const originalOverflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    const scrollBlocked = document.body.style.overflow === 'hidden'
    document.body.style.overflow = originalOverflow
    results.push({
      test: 'Scroll Control',
      status: scrollBlocked ? 'PASS' : 'FAIL',
      message: scrollBlocked ? 'Can control body scroll' : 'Cannot control body scroll',
      details: `Body overflow control: ${scrollBlocked}`
    })

    // Test 6: Z-Index Support
    const testZIndex = document.createElement('div')
    testZIndex.style.zIndex = '9999'
    const zIndexSupported = testZIndex.style.zIndex === '9999'
    results.push({
      test: 'Z-Index Support',
      status: zIndexSupported ? 'PASS' : 'FAIL',
      message: zIndexSupported ? 'Z-index positioning works' : 'Z-index positioning failed',
      details: `High z-index support: ${zIndexSupported}`
    })

    // Test 7: Fixed Positioning
    const testFixed = document.createElement('div')
    testFixed.style.position = 'fixed'
    const fixedSupported = testFixed.style.position === 'fixed'
    results.push({
      test: 'Fixed Positioning',
      status: fixedSupported ? 'PASS' : 'FAIL',
      message: fixedSupported ? 'Fixed positioning supported' : 'Fixed positioning not supported',
      details: `Position fixed support: ${fixedSupported}`
    })

    // Test 8: Event Delegation
    let eventDelegationWorks = false
    const testEventDiv = document.createElement('div')
    testEventDiv.addEventListener('click', () => { eventDelegationWorks = true })
    testEventDiv.click()
    results.push({
      test: 'Event Delegation',
      status: eventDelegationWorks ? 'PASS' : 'FAIL',
      message: eventDelegationWorks ? 'Event delegation works' : 'Event delegation failed',
      details: `Click event handling: ${eventDelegationWorks}`
    })

    setTestResults(results)
    setIsLoading(false)
  }

  const loadMobileWidget = () => {
    // Remove existing widget if present
    const existingButton = document.getElementById('botfusion-mobile-button')
    const existingOverlay = document.getElementById('botfusion-mobile-overlay')
    if (existingButton) existingButton.remove()
    if (existingOverlay) existingOverlay.remove()

    // Set configuration
    ;(window as any).BOTFUSION_CHAT_ID = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d'
    ;(window as any).BOTFUSION_BASE_URL = window.location.origin
    ;(window as any).BOTFUSION_PRIMARY_COLOR = '#3b82f6'
    ;(window as any).BOTFUSION_DEBUG = true

    // Load the script
    const script = document.createElement('script')
    script.src = `${window.location.origin}/mobile-optimized-embed.js?v=2.0&t=${Date.now()}`
    script.async = true
    script.onload = () => {
      console.log('Mobile widget loaded successfully')
    }
    script.onerror = () => {
      console.error('Failed to load mobile widget')
    }
    document.head.appendChild(script)
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <Card className="p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Mobile Embed Testing Page</h1>
          <p className="text-gray-600 mb-4">
            This page tests the mobile-optimized chat widget to ensure it doesn't block parent page interactions.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Button onClick={runMobileTests} disabled={isLoading} className="w-full">
              {isLoading ? 'Running Tests...' : 'Run Mobile Compatibility Tests'}
            </Button>
            <Button onClick={loadMobileWidget} variant="outline" className="w-full">
              Load Mobile Widget
            </Button>
          </div>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">Test Results</h2>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className={`p-3 rounded border-l-4 ${
                  result.status === 'PASS' ? 'bg-green-50 border-green-500' :
                  result.status === 'WARN' ? 'bg-yellow-50 border-yellow-500' :
                  'bg-red-50 border-red-500'
                }`}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold">{result.test}</h3>
                      <p className="text-sm text-gray-600">{result.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{result.details}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-semibold ${
                      result.status === 'PASS' ? 'bg-green-100 text-green-800' :
                      result.status === 'WARN' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Mobile Interaction Test Area */}
        <Card className="p-6 mb-6">
          <h2 className="text-xl font-bold mb-4">Mobile Interaction Test Area</h2>
          <p className="text-gray-600 mb-4">
            Test that parent page interactions work when the chat widget is loaded:
          </p>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Button onClick={() => alert('Button 1 clicked!')} variant="outline">
                Test Button 1
              </Button>
              <Button onClick={() => alert('Button 2 clicked!')} variant="outline">
                Test Button 2
              </Button>
            </div>
            
            <div className="h-32 bg-blue-50 border-2 border-dashed border-blue-300 rounded p-4 overflow-auto">
              <p className="text-blue-600 font-semibold mb-2">Scrollable Area Test</p>
              <p>This area should remain scrollable even when the chat widget is open on mobile.</p>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
              <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
              <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            </div>
            
            <input 
              type="text" 
              placeholder="Test input field - should remain functional"
              className="w-full p-2 border rounded"
            />
          </div>
        </Card>

        {/* Embed Code */}
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">Mobile-Optimized Embed Code</h2>
          <pre className="bg-gray-800 text-green-400 p-4 rounded text-xs overflow-auto">
            {embedCode}
          </pre>
        </Card>
      </div>
    </div>
  )
}
