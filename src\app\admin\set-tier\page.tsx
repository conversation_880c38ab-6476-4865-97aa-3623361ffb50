'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { createClient } from '@/lib/supabase/client'
import { UserTier, getTierDisplayName } from '@/lib/tiers'

interface UserProfile {
  userId: string
  email: string
  tier: UserTier
  subscriptionStatus: string
  hasProfile: boolean
  createdAt?: string
  updatedAt?: string
}

export default function SetTierPage() {
  const [currentProfile, setCurrentProfile] = useState<UserProfile | null>(null)
  const [selectedTier, setSelectedTier] = useState<UserTier>('pro')
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  const supabase = createClient()

  useEffect(() => {
    fetchCurrentProfile()
  }, [])

  const fetchCurrentProfile = async () => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/admin/set-user-tier')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch profile')
      }

      setCurrentProfile(data)
      setSelectedTier(data.tier)
    } catch (error) {
      console.error('Error fetching profile:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to fetch profile')
    } finally {
      setLoading(false)
    }
  }

  const updateTier = async () => {
    if (!currentProfile) return

    try {
      setUpdating(true)

      const response = await fetch('/api/admin/set-user-tier', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentProfile.userId,
          tier: selectedTier
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update tier')
      }

      toast.success(data.message)
      fetchCurrentProfile() // Refresh profile
    } catch (error) {
      console.error('Error updating tier:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update tier')
    } finally {
      setUpdating(false)
    }
  }

  const getTierBadgeColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-500'
      case 'standard': return 'bg-blue-500'
      case 'pro': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
            <p className="mt-4">Loading profile...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🎯 Set User Tier
          </h1>
          <p className="text-xl text-gray-300">
            Manage your account tier and permissions
          </p>
        </div>

        {/* Current Profile */}
        {currentProfile && (
          <Card className="bg-white/10 border-gray-600 p-6">
            <h2 className="text-xl font-bold text-white mb-4">Current Profile</h2>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Email:</span>
                <span className="text-white">{currentProfile.email}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">User ID:</span>
                <span className="text-white font-mono text-sm">
                  {currentProfile.userId.slice(0, 8)}...
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Current Tier:</span>
                <Badge className={`${getTierBadgeColor(currentProfile.tier)} text-white`}>
                  {getTierDisplayName(currentProfile.tier)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Status:</span>
                <Badge className="bg-green-500 text-white">
                  {currentProfile.subscriptionStatus}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Profile:</span>
                <span className="text-white">
                  {currentProfile.hasProfile ? 'Exists' : 'Will be created'}
                </span>
              </div>
            </div>
          </Card>
        )}

        {/* Tier Selection */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Update Tier</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Select New Tier
              </label>
              <Select value={selectedTier} onValueChange={(value) => setSelectedTier(value as UserTier)}>
                <SelectTrigger className="bg-white/20 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">
                    <div className="flex items-center gap-2">
                      <span>🆓</span>
                      <span>Free</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="standard">
                    <div className="flex items-center gap-2">
                      <span>⭐</span>
                      <span>Standard</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="pro">
                    <div className="flex items-center gap-2">
                      <span>💎</span>
                      <span>Pro</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={updateTier}
              disabled={updating || !currentProfile || selectedTier === currentProfile.tier}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            >
              {updating ? 'Updating...' : `Update to ${getTierDisplayName(selectedTier)} Tier`}
            </Button>
          </div>
        </Card>

        {/* Tier Features */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Tier Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-white">🆓 Free</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• 1 Chat Interface</li>
                <li>• Basic Features</li>
                <li>• Required Branding</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-white">⭐ Standard</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Unlimited Interfaces</li>
                <li>• Voice Features (3 models)</li>
                <li>• Optional Branding</li>
                <li>• Advanced Customization</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-white">💎 Pro</h3>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• All Standard Features</li>
                <li>• Premium Voice (6 models)</li>
                <li>• White Labeling</li>
                <li>• Custom Branding</li>
                <li>• Analytics Dashboard</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <div className="flex gap-4 justify-center">
            <Button
              onClick={fetchCurrentProfile}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              🔄 Refresh Profile
            </Button>
            <Button
              onClick={() => window.open('/dashboard', '_blank')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              🏠 Go to Dashboard
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}
