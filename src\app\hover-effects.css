/* Explicit hover effects for Vercel deployment */

/* Glass card hover effects */
.glass-card:hover {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out !important;
}

/* Button glow hover effects */
.button-glow-hover:hover {
  box-shadow: 0 0 8px var(--glow-color) !important;
  transition: box-shadow 0.5s ease-in-out !important;
}

/* Glass button hover effects */
.glass-button:hover {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  transition: box-shadow 0.4s ease-in-out, border-color 0.4s ease-in-out !important;
}

/* Ambient glow hover effects */
.ambient-glow:hover {
  box-shadow: 0 0 12px var(--glow-color) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

/* Shadow glow blue hover effects */
.shadow-glow-blue:hover {
  box-shadow: 0 0 8px var(--glow-color) !important;
}

/* Ensure animations work properly */
.animate-blob {
  animation: blob 15s ease-in-out infinite !important;
}

.pulse-glow {
  animation: pulse-glow 4s infinite !important;
}

/* Ensure backdrop filters work */
.glass-card, .glass-button {
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}
