// BotFusion Chat Widget
// This script is designed to be embedded on any website to provide a chat interface
// Version: 1.1.0
// Last updated: 2023-12-01

(function() {
  // Function to initialize the chat widget
  function initChatWidget() {
    try {
      // Find the script element using multiple methods
      let scriptElement = document.getElementById('botfusion-chat-widget');

      // If not found by ID, try to find by src attribute
      if (!scriptElement) {
        const scripts = document.getElementsByTagName('script');
        for (let i = 0; i < scripts.length; i++) {
          const src = scripts[i].getAttribute('src') || '';
          if (src.includes('/api/embed-script') || src.includes('embed-script.js')) {
            scriptElement = scripts[i];
            break;
          }
        }
      }

      // If still not found, use document.currentScript as a last resort
      if (!scriptElement && document.currentScript) {
        scriptElement = document.currentScript;
      }

      // If we still couldn't find the script element, use default values
      let chatId = '1'; // Default chat ID
      let primaryColor = '#3b82f6'; // Default blue
      let userBubbleColor = '#ffffff';
      let botBubbleColor = '#3b82f6';
      let userTextColor = '#000000';
      let botTextColor = '#ffffff';
      let logoUrl = '';
      let darkMode = false;
      let textColor = '#ffffff';
      let useBlackOutline = false; // Default to no black outline
      let useGradientHeader = false; // Default to no gradient header
      let gradientStartColor = '#3b82f6'; // Default gradient start color
      let gradientEndColor = '#9333ea'; // Default gradient end color
      let gradientDirection = 'to bottom'; // Default gradient direction
      let origin = 'https://roo-bot-fusion-kgfs-9iskuiq3q-tellivisions-projects.vercel.app'; // Default origin

      // If we found the script element, get attributes from it
      if (scriptElement) {
        chatId = scriptElement.getAttribute('data-chat-id') || chatId;
        primaryColor = scriptElement.getAttribute('data-primary-color') || primaryColor;
        userBubbleColor = scriptElement.getAttribute('data-user-bubble-color') || userBubbleColor;
        botBubbleColor = scriptElement.getAttribute('data-bot-bubble-color') || botBubbleColor;
        userTextColor = scriptElement.getAttribute('data-user-text-color') || userTextColor;
        botTextColor = scriptElement.getAttribute('data-bot-text-color') || botTextColor;
        logoUrl = scriptElement.getAttribute('data-logo-url') || logoUrl;
        darkMode = scriptElement.getAttribute('data-dark-mode') === 'true' || darkMode;
        textColor = scriptElement.getAttribute('data-text-color') || textColor;
        useBlackOutline = scriptElement.getAttribute('data-use-black-outline') === 'true' || useBlackOutline;
        useGradientHeader = scriptElement.getAttribute('data-use-gradient-header') === 'true' || useGradientHeader;
        gradientStartColor = scriptElement.getAttribute('data-gradient-start-color') || gradientStartColor;
        gradientEndColor = scriptElement.getAttribute('data-gradient-end-color') || gradientEndColor;
        gradientDirection = scriptElement.getAttribute('data-gradient-direction') || gradientDirection;
        origin = scriptElement.getAttribute('data-origin') || origin;
      } else {
        console.warn('BotFusion Chat: Could not find script element, using default values');
      }

      console.log('BotFusion Chat: Initializing widget with chat ID:', chatId);

      // Create iframe element with custom parameters
      const iframe = document.createElement('iframe');

      console.log('BotFusion Chat: Using origin:', origin);

      try {
        // Add custom parameters to the URL - use chat-only mode for widget
        const url = new URL(`${origin}/embed/${chatId}`);
        url.searchParams.append('mode', 'chat-only');
        url.searchParams.append('embed', 'true');
        url.searchParams.append('primaryColor', encodeURIComponent(primaryColor));
        url.searchParams.append('userBubbleColor', encodeURIComponent(userBubbleColor));
        url.searchParams.append('botBubbleColor', encodeURIComponent(botBubbleColor));
        url.searchParams.append('userTextColor', encodeURIComponent(userTextColor));
        url.searchParams.append('botTextColor', encodeURIComponent(botTextColor));
        url.searchParams.append('logoUrl', encodeURIComponent(logoUrl));
        url.searchParams.append('darkMode', darkMode ? 'true' : 'false');
        url.searchParams.append('useBlackOutline', useBlackOutline ? 'true' : 'false');
        // Add gradient header parameters
        url.searchParams.append('useGradientHeader', useGradientHeader ? 'true' : 'false');
        url.searchParams.append('gradientStartColor', encodeURIComponent(gradientStartColor));
        url.searchParams.append('gradientEndColor', encodeURIComponent(gradientEndColor));
        url.searchParams.append('gradientDirection', encodeURIComponent(gradientDirection));
        // Add a timestamp to prevent caching issues
        url.searchParams.append('t', new Date().getTime());

        // Set iframe properties
        iframe.src = url.toString();
        console.log('BotFusion Chat: Setting iframe src to:', url.toString());

        // Important: Add proper attributes for security and performance
        iframe.setAttribute('allow', 'microphone');
        iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox');
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('importance', 'high');
        iframe.setAttribute('referrerpolicy', 'origin');
        iframe.setAttribute('credentialless', '');
        iframe.setAttribute('fetchpriority', 'high');
        iframe.setAttribute('title', 'Chat Widget');
      } catch (error) {
        console.error('BotFusion Chat: Error creating URL:', error);
        // Fallback to a simpler URL construction - use chat-only mode for widget
        iframe.src = `${origin}/embed/${chatId}?mode=chat-only&embed=true&primaryColor=${encodeURIComponent(primaryColor)}&t=${new Date().getTime()}`;
        console.log('BotFusion Chat: Using fallback URL:', iframe.src);
      }

      // Set iframe styles
      iframe.style.position = 'fixed';
      iframe.style.bottom = '20px';
      iframe.style.right = '20px';
      iframe.style.width = '400px';
      iframe.style.height = '600px';
      iframe.style.border = 'none';
      iframe.style.borderRadius = '12px';
      iframe.style.zIndex = '9999';
      iframe.style.display = 'none';
      iframe.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
      iframe.style.boxShadow = 'none !important';
      iframe.style.filter = 'none !important';
      iframe.style.backgroundColor = 'transparent';

      // Preload the iframe content but keep it hidden
      iframe.style.visibility = 'hidden';
      iframe.style.opacity = '0';
      iframe.style.pointerEvents = 'none';

      // Create a div container instead of a button to avoid browser's default button styling
      const button = document.createElement('div');

      // Set button role for accessibility
      button.setAttribute('role', 'button');
      button.setAttribute('aria-label', 'Open chat');
      button.setAttribute('tabindex', '0');

      // Apply all styles before setting content to prevent any default styling
      button.style.position = 'fixed';
      button.style.bottom = '20px';
      button.style.right = '20px';
      button.style.width = '60px';
      button.style.height = '60px';
      button.style.borderRadius = '50%';
      button.style.backgroundColor = primaryColor;
      button.style.color = 'white'; // Force white color for the icon
      button.style.border = 'none';
      button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      button.style.cursor = 'pointer';
      button.style.zIndex = '9999';
      button.style.display = 'flex';
      button.style.alignItems = 'center';
      button.style.justifyContent = 'center';
      button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
      button.style.padding = '0'; // Ensure no padding affects the icon
      button.style.margin = '0'; // Ensure no margin affects the icon
      button.style.appearance = 'none'; // Remove any browser-specific styling
      button.style.webkitAppearance = 'none'; // For Safari
      button.style.mozAppearance = 'none'; // For Firefox

      // Use a hardcoded SVG with explicit white color to avoid any color issues
      button.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      `;

      // SVG is already in the button via innerHTML

      // Add hover effect
      button.addEventListener('mouseover', function() {
        button.style.transform = 'scale(1.05)';
        button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
      });

      button.addEventListener('mouseout', function() {
        button.style.transform = 'scale(1)';
        button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      });

      // Toggle chat window when button is clicked or activated with keyboard
      function openChat() {
        // Make iframe visible
        iframe.style.display = 'block';
        iframe.style.visibility = 'visible';
        iframe.style.pointerEvents = 'auto';
        button.style.display = 'none';

        // Add animation
        iframe.style.transform = 'translateY(20px)';
        iframe.style.opacity = '0';

        setTimeout(function() {
          iframe.style.transform = 'translateY(0)';
          iframe.style.opacity = '1';
        }, 10);
      }

      // Add click event listener
      button.addEventListener('click', openChat);

      // Add keyboard event listener for accessibility
      button.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
          openChat();
        }
      });

      // Create a MessageChannel for secure communication
      const channel = new MessageChannel();

      // Add message listener to handle close button in iframe
      window.addEventListener('message', function(event) {
        // Check if the message is from our iframe
        if (event.data === 'botfusion-chat-close') {
          console.log('BotFusion Chat: Received close message from iframe');

          // Add closing animation
          iframe.style.transform = 'translateY(20px)';
          iframe.style.opacity = '0';

          setTimeout(function() {
            // Hide iframe but keep it loaded
            iframe.style.display = 'none';
            iframe.style.visibility = 'hidden';
            iframe.style.opacity = '0';
            iframe.style.pointerEvents = 'none';

            // Show button
            button.style.display = 'flex';

            // Always reset the SVG with explicit white color to ensure consistency
            button.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: block; width: 24px; height: 24px;">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            `;
          }, 300);

          // Acknowledge the message to prevent "message channel closed" error
          if (event.ports && event.ports.length > 0) {
            try {
              event.ports[0].postMessage({ status: 'acknowledged' });
            } catch (err) {
              console.log('BotFusion Chat: Could not acknowledge message', err);
            }
          }
        }
      });

      // Setup the iframe with the message port once it's loaded
      iframe.addEventListener('load', function() {
        try {
          console.log('BotFusion Chat: iframe loaded, setting up communication channel');
          iframe.contentWindow.postMessage('botfusion-chat-init', '*', [channel.port2]);
          console.log('BotFusion Chat: Communication channel established');
        } catch (err) {
          console.error('BotFusion Chat: Error setting up communication channel', err);
        }
      });

      // Append elements to the document
      document.body.appendChild(iframe);
      document.body.appendChild(button);

      // Preload the iframe but keep it hidden
      setTimeout(function() {
        // Keep the iframe hidden but load its content
        iframe.style.display = 'none';
        iframe.style.visibility = 'hidden';
        iframe.style.opacity = '0';
      }, 500);

      // Add custom styles using a style element
      const style = document.createElement('style');
      style.textContent = `
        /* Animation for pulse effect */
        @keyframes botfusion-pulse {
          0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
          70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
          100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }

        /* Reset for BotFusion chat button */
        .botfusion-chat-button {
          all: initial;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          cursor: pointer;
        }

        /* Reset for BotFusion chat button SVG */
        .botfusion-chat-button svg {
          display: block !important;
          width: 24px !important;
          height: 24px !important;
          stroke: white !important;
          fill: none !important;
        }
      `;
      document.head.appendChild(style);

      // Add class to button for CSS targeting
      button.classList.add('botfusion-chat-button');

      console.log('BotFusion Chat: Widget initialized successfully');
    } catch (error) {
      console.error('BotFusion Chat: Error initializing widget:', error);
    }
  }

  // Check if the DOM is already loaded
  if (document.readyState === 'loading') {
    // If not, wait for it to load
    document.addEventListener('DOMContentLoaded', initChatWidget);
  } else {
    // If already loaded, initialize immediately
    setTimeout(initChatWidget, 0); // Use setTimeout to ensure this runs after all synchronous code
  }
})();
