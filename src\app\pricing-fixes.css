/* Additional fixes for pricing section buttons */

/* Ensure all pricing buttons have the same style */
.pricing-button .glass-card {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: white !important;
  font-weight: 500 !important;
  text-align: center !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

/* Style for disabled button */
.pricing-button button[disabled],
.pricing-button .glass-card[disabled] {
  background: rgba(59, 130, 246, 0.05) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

/* Ensure all pricing cards have the same height */
.pricing-card {
  min-height: 350px !important;
}

/* Ensure pricing content is properly spaced */
.pricing-content {
  min-height: 220px !important;
}

/* Ensure pricing buttons are aligned at the bottom */
.pricing-button {
  padding-top: 20px !important;
}

/* Ensure all buttons have the same dimensions */
.pricing-button .glass-card,
.pricing-button button {
  width: 128px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Fix for the Current Plan button */
.pricing-button .inline-block {
  display: inline-block !important;
}
