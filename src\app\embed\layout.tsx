import { Metadata } from 'next'
import { <PERSON>ei<PERSON> } from 'next/font/google'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Chat Widget',
  description: 'BotFusion X Chat Widget',
}

// Add custom headers to allow embedding
export const headers = () => {
  return [
    {
      key: 'Content-Security-Policy',
      value: "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: data: https: mediastream:; worker-src 'self' blob:;",
    },

    {
      key: 'Access-Control-Allow-Origin',
      value: '*',
    },
    {
      key: 'Access-Control-Allow-Methods',
      value: 'GET, POST, OPTIONS',
    },
    {
      key: 'Access-Control-Allow-Headers',
      value: 'Content-Type, Authorization, X-Requested-With, Accept',
    },
    {
      key: 'Cross-Origin-Embedder-Policy',
      value: 'unsafe-none',
    },
    {
      key: 'Cross-Origin-Opener-Policy',
      value: 'unsafe-none',
    },
    {
      key: 'Cross-Origin-Resource-Policy',
      value: 'cross-origin',
    },
  ]
}

export default function EmbedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        {/* Add CSP meta tag to allow embedding */}
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: data: https: mediastream:; worker-src 'self' blob:;"
        />

      </head>
      <body className={`${geistSans.variable} antialiased`} style={{ background: 'transparent' }}>
        {children}
      </body>
    </html>
  )
}
