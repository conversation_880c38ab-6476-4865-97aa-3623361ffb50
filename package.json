{"name": "botfusion-x", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "migrate": "node scripts/migrate.js", "setup-db": "node scripts/setup_database.js", "start": "next start", "lint": "next lint", "deploy": "node scripts/deploy-vercel.js", "direct-deploy": "node scripts/direct-deploy.js", "vercel-build": "next build"}, "dependencies": {"@deepgram/sdk": "^4.2.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/uuid": "^10.0.0", "@upstash/redis": "^1.34.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "framer-motion": "^12.7.4", "lucide-react": "^0.501.0", "next": "15.3.1", "node-fetch": "^3.3.2", "openai": "^5.3.0", "puppeteer": "^24.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "supabase": "^2.22.4", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}