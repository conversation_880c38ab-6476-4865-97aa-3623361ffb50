/** @type {import('next').NextConfig} */
const nextConfig = {
  // Ignore TypeScript errors during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Output standalone build for easier deployment
  output: 'standalone',
  // Configure image domains for avatar images
  images: {
    domains: ['avatars.githubusercontent.com', 'lh3.googleusercontent.com'],
  },
  // Optimize CSS for production
  experimental: {
    // Disable CSS optimization to preserve hover effects and animations
    optimizeCss: false,
    // Enable CSS modules for better CSS isolation
    esmExternals: true,
    // Improve CSS loading performance
    optimizeServerReact: true,
  },
  // Disable X-Powered-By header
  poweredByHeader: false,
  // Transpile specific modules that need it
  transpilePackages: ['recharts', '@radix-ui/react-switch', '@radix-ui/react-tabs'],
  // Add headers to allow iframe embedding and CORS for embed script
  async rewrites() {
    return [
      {
        source: '/embed-script.js',
        destination: '/api/embed-script',
      },
    ];
  },

  // Configure headers for CORS and iframe embedding
  async headers() {
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: [
          // Remove X-Frame-Options header to allow embedding from any domain
          // No X-Frame-Options header means any site can embed this content
          { key: 'Content-Security-Policy', value: "frame-ancestors *; frame-src *; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:;" },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization, X-Requested-With, Accept' },
          // Add additional headers for better cross-origin support
          { key: 'Cross-Origin-Embedder-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Opener-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Resource-Policy', value: 'cross-origin' },
        ],
      },
      {
        // Special headers for embed routes - CRITICAL for iframe embedding
        source: '/(embed|chat)/:path*',
        headers: [
          // Remove X-Frame-Options header for embed routes
          // { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
          { key: 'Content-Security-Policy', value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; media-src 'self' data: https: blob:; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:;" },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization, X-Requested-With, Accept' },
          { key: 'Access-Control-Allow-Credentials', value: 'false' },
          { key: 'Access-Control-Max-Age', value: '86400' },
          { key: 'Cross-Origin-Embedder-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Opener-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Resource-Policy', value: 'cross-origin' },
        ],
      },
      {
        // Special headers for API routes
        source: '/api/:path*',
        headers: [
          // Remove X-Frame-Options header to allow embedding from any domain
          // No X-Frame-Options header means any site can embed this content
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, OPTIONS, PATCH, DELETE, POST, PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version' },
          { key: 'Content-Security-Policy', value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src *; frame-ancestors *;" },
        ],
      },
      {
        // Special headers for embed script
        source: '/api/embed-script',
        headers: [
          // Remove X-Frame-Options header to allow embedding from any domain
          // No X-Frame-Options header means any site can embed this content
          { key: 'Content-Type', value: 'application/javascript; charset=utf-8' },
          { key: 'Cache-Control', value: 'public, max-age=3600' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'X-Requested-With, Content-Type, Accept' },
          { key: 'Access-Control-Allow-Credentials', value: 'false' },
          { key: 'Content-Security-Policy', value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; frame-src *; frame-ancestors *;" },
          // Add additional headers for better cross-origin support
          { key: 'Cross-Origin-Embedder-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Opener-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Resource-Policy', value: 'cross-origin' },
        ],
      },
      {
        // Special headers for simple embed script
        source: '/api/simple-embed-script',
        headers: [
          // Remove X-Frame-Options header to allow embedding from any domain
          // No X-Frame-Options header means any site can embed this content
          { key: 'Content-Type', value: 'application/javascript; charset=utf-8' },
          { key: 'Cache-Control', value: 'public, max-age=3600' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'X-Requested-With, Content-Type, Accept' },
          { key: 'Access-Control-Allow-Credentials', value: 'false' },
          { key: 'Content-Security-Policy', value: "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;" },
          // Add additional headers for better cross-origin support
          { key: 'Cross-Origin-Embedder-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Opener-Policy', value: 'unsafe-none' },
          { key: 'Cross-Origin-Resource-Policy', value: 'cross-origin' },
        ],
      },
    ];
  },
}

export default nextConfig;
