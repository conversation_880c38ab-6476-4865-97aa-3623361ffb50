'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard'

// Default code template to use before client-side code runs
const DEFAULT_CODE = `<!-- BotFusion Direct Iframe Embed -->
<!-- This version shows the chat interface directly without a button -->
<iframe
  src="ORIGIN_PLACEHOLDER/embed/CHAT_ID_PLACEHOLDER"
  style="width: 400px; height: 600px; border: none; border-radius: 12px; box-shadow: none !important;"
  allow="microphone"
  title="BotFusion Chat"
></iframe>`;

export function ClientOnlyDirectIframeTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  // Always initialize with a default value
  const [directIframeCode, setDirectIframeCode] = useState(DEFAULT_CODE)
  const { isCopied, copyToClipboard } = useCopyToClipboard(2000)

  // Use a ref to track if the component is mounted
  const isMounted = useRef(true)

  useEffect(() => {
    // Set up cleanup function
    return () => {
      isMounted.current = false
    }
  }, [])

  // Update the code when the component mounts or props change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const siteOrigin = window.location.origin;
      const code = `<!-- BotFusion Direct Iframe Embed -->
<!-- This version shows the chat interface directly without a button -->
<iframe
  src="${siteOrigin}/embed/${chatId}"
  style="width: 400px; height: 600px; border: none; border-radius: 12px; box-shadow: none !important;"
  allow="microphone"
  title="BotFusion Chat"
></iframe>`;

      // Only update state if the component is still mounted
      if (isMounted.current) {
        setDirectIframeCode(code);
      }
    }
  }, [chatId, chatName])

  return (
    <div>
      <h3 className="text-lg font-medium text-neon-blue mb-2">Direct Iframe Embed</h3>
      <div className="relative">
        <div className="flex justify-end mb-2">
          <Button
            onClick={() => copyToClipboard(directIframeCode, 'Direct iframe code copied to clipboard!')}
            className="glass-card hover:shadow-glow-blue"
            size="sm"
          >
            {isCopied ? 'COPIED' : 'Copy'}
          </Button>
        </div>
        <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
          {directIframeCode}
        </pre>
      </div>
      <p className="text-sm text-gray-400 mt-3">
        <strong>Direct Iframe Embed</strong> - This embeds the chat interface directly in your page without a button.
        <br />
        Use this when you want to display the chat interface immediately without requiring a user click.
        <br /><br />
        <strong>Features:</strong>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>No JavaScript required</li>
          <li>Works with any CSP settings</li>
          <li>Displays chat interface immediately</li>
          <li>Can be placed anywhere in your page layout</li>
        </ul>
      </p>
    </div>
  )
}
