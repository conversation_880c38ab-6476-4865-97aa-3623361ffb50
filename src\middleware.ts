import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { runMigrations } from './lib/supabase/migrations/run-migrations'

// Flag to ensure migrations only run once
let migrationsRun = false

export async function middleware(request: NextRequest) {
  // Run migrations only once when the server starts
  if (!migrationsRun) {
    try {
      await runMigrations()
      migrationsRun = true
      console.log('Database migrations completed successfully')
    } catch (error) {
      console.error('Failed to run migrations:', error)
    }
  }

  const { pathname } = request.nextUrl

  // Check for Vercel Protection Bypass first
  const bypassSecret = process.env.VERCEL_AUTOMATION_BYPASS_SECRET || 'Q3W0KiXNSiZt6HCFH0e1zYfT2MwpCR2F';
  const bypassHeader = request.headers.get('x-vercel-protection-bypass');
  const bypassQuery = request.nextUrl.searchParams.get('x-vercel-protection-bypass');
  const hasBypass = bypassHeader === bypassSecret || bypassQuery === bypassSecret;

  // If bypass is present, allow the request to proceed with proper headers
  if (hasBypass) {
    console.log('Vercel protection bypass detected for:', pathname);
    const response = NextResponse.next();

    // Set headers to allow iframe embedding
    response.headers.delete('X-Frame-Options');
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, x-vercel-protection-bypass');
    response.headers.set('Cross-Origin-Embedder-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Opener-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Resource-Policy', 'cross-origin');

    // Set bypass cookie if requested
    const setCookieHeader = request.headers.get('x-vercel-set-bypass-cookie') || request.nextUrl.searchParams.get('x-vercel-set-bypass-cookie');
    if (setCookieHeader === 'true' || setCookieHeader === 'samesitenone') {
      const sameSite = setCookieHeader === 'samesitenone' ? 'None; Secure' : 'Lax';
      response.headers.set('Set-Cookie', `vercel-protection-bypass=${bypassSecret}; Path=/; SameSite=${sameSite}; Max-Age=86400`);
    }

    return response;
  }

  // Debug logging for API routes
  if (pathname.startsWith('/api/')) {
    console.log('Middleware processing API route:', {
      pathname,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries())
    });
  }

  // Special case for simple-embed-script - bypass all authentication and just return the script
  if (pathname === '/api/simple-embed-script') {
    console.log('Bypassing authentication for simple-embed-script');
    return NextResponse.next();
  }

  const protectedRoutes = ['/dashboard']
  const authRoutes = ['/login']
  const embedRoutes = [
    '/embed',
    '/chat',
    '/widget',
    '/widget-test',
    '/api/embed-script',
    '/api/embed-script-loader',
    '/api/csp-embed-script',
    '/api/html-embed',
    '/api/pure-html-embed',
    '/api/chat',
    '/api/iframe-embed',
    '/api/generate-embed-code',
    '/api/embed-simple',
    '/api/embed-final',
    '/api/embed-widget',
    '/api/embed-csp-safe'
  ]

  // Handle OPTIONS requests for CORS preflight
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  // Check if this is a public API route that should bypass authentication
  const publicApiRoutes = [
    '/api/simple-embed-script',
    '/api/embed-script',
    '/api/embed-script-loader',
    '/api/csp-embed-script',
    '/api/html-embed',
    '/api/pure-html-embed',
    '/api/embed-simple',
    '/api/embed-final',
    '/api/embed-widget',
    '/api/embed-csp-safe',
    '/api/chat', // Add chat API to public routes for embedding
    '/api/public-chat', // Add public chat API as backup
    '/api/webhook-proxy', // Add webhook proxy for chat functionality
    // TTS API routes
    '/api/deepgram-tts',
    '/api/fallback-tts',
    '/api/echo-tts',
    '/api/auth/token',
    // STT API routes
    '/api/deepgram-stt'
  ]

  // Check for exact matches and startsWith matches for public API routes
  const isPublicApiRoute = publicApiRoutes.some(route => {
    return pathname === route || pathname.startsWith(route + '/') || pathname.startsWith(route + '?');
  });

  console.log('Public API route check:', {
    pathname,
    isPublicApiRoute,
    publicApiRoutes: publicApiRoutes.filter(route => pathname.startsWith(route))
  });

  if (isPublicApiRoute) {
    console.log('Processing as public API route:', pathname);
    const response = NextResponse.next()

    // CRITICAL: Set proper headers for public API routes
    // Remove X-Frame-Options header completely to allow embedding from any domain
    response.headers.delete('X-Frame-Options');
    response.headers.set('Cross-Origin-Embedder-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Opener-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Resource-Policy', 'cross-origin');

    // Set appropriate Content-Security-Policy based on the route
    if (pathname.startsWith('/api/simple-embed-script')) {
      response.headers.set('Content-Security-Policy', "default-src 'none'; script-src 'unsafe-inline'; frame-ancestors *;");
    } else if (pathname.startsWith('/api/deepgram-tts') ||
               pathname.startsWith('/api/deepgram-stt') ||
               pathname.startsWith('/api/fallback-tts') ||
               pathname.startsWith('/api/echo-tts') ||
               pathname.startsWith('/api/auth/token')) {
      // For TTS/STT API routes, set a permissive CSP to allow audio playback and WebSocket connections
      response.headers.set('Content-Security-Policy',
        "default-src 'self' blob: data:; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "media-src 'self' blob: data: https: mediastream:; " +
        "connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; " +
        "frame-src *; " +
        "frame-ancestors *; " +
        "font-src 'self' data:; " +
        "worker-src 'self' blob:;"
      );
    } else {
      response.headers.set('Content-Security-Policy',
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; " +
        "frame-src *; " +
        "frame-ancestors *; " +
        "font-src 'self' data:;"
      );
    }

    // Set comprehensive CORS headers
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
    response.headers.set('Access-Control-Allow-Credentials', 'false');
    response.headers.set('Access-Control-Max-Age', '86400');

    // Set content type for script routes
    if (pathname.startsWith('/api/simple-embed-script') ||
        pathname.startsWith('/api/embed-script') ||
        pathname.startsWith('/api/csp-embed-script') ||
        pathname.startsWith('/api/embed-simple') ||
        pathname.startsWith('/api/embed-final') ||
        pathname.startsWith('/api/embed-widget') ||
        pathname.startsWith('/api/embed-csp-safe')) {
      response.headers.set('Content-Type', 'application/javascript; charset=utf-8');
      response.headers.set('Cache-Control', 'public, max-age=3600');
    }

    return response;
  }

  // Special handling for TTS test page
  if (pathname.startsWith('/tts-test')) {
    const response = NextResponse.next()

    // Set permissive CSP for TTS test page to allow audio playback and STT WebSocket connections
    response.headers.set('Content-Security-Policy',
      "default-src 'self' blob: data:; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "media-src 'self' blob: data: https: mediastream:; " +
      "connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; " +
      "frame-src *; " +
      "frame-ancestors *; " +
      "font-src 'self' data:; " +
      "worker-src 'self' blob:;"
    );

    return response;
  }

  // Set CORS headers for embed routes to allow iframe embedding
  if (embedRoutes.some(route => pathname.startsWith(route))) {
    const response = NextResponse.next()

    // CRITICAL: Set proper headers for iframe embedding
    // Remove X-Frame-Options header completely to allow embedding from any domain
    response.headers.delete('X-Frame-Options');

    // Set additional headers for better cross-origin support
    response.headers.set('Cross-Origin-Embedder-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Opener-Policy', 'unsafe-none');
    response.headers.set('Cross-Origin-Resource-Policy', 'cross-origin');

    // Set a permissive Content-Security-Policy that allows embedding, inline scripts, audio, and STT WebSocket connections
    response.headers.set('Content-Security-Policy',
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "media-src 'self' data: https: blob:; " +
      "connect-src 'self' https: wss: wss://api.deepgram.com blob: data:; " +
      "frame-src *; " +
      "frame-ancestors *; " +
      "font-src 'self' data:;"
    )

    // Remove X-Frame-Options header completely to allow embedding from any domain
    response.headers.delete('X-Frame-Options')

    // 3. Set comprehensive CORS headers
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept')
    response.headers.set('Access-Control-Allow-Credentials', 'false') // Important: set to false for public resources
    response.headers.set('Access-Control-Max-Age', '86400') // Cache preflight requests for 24 hours

    return response
  }

  // Redirect to login if trying to access protected route without session
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      // Ensure request.url is a valid string before creating a URL
      if (request.url) {
        const baseUrl = new URL(request.url).origin
        return NextResponse.redirect(new URL('/login', baseUrl))
      } else {
        // Fallback if request.url is undefined
        return NextResponse.redirect(new URL('/login', 'http://localhost:3000'))
      }
    }
  }

  // Redirect to dashboard if logged in and trying to access auth route
  if (authRoutes.includes(pathname)) {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (user) {
      // Ensure request.url is a valid string before creating a URL
      if (request.url) {
        const baseUrl = new URL(request.url).origin
        return NextResponse.redirect(new URL('/dashboard', baseUrl))
      } else {
        // Fallback if request.url is undefined
        return NextResponse.redirect(new URL('/dashboard', 'http://localhost:3000'))
      }
    }
  }

  return NextResponse.next()
}