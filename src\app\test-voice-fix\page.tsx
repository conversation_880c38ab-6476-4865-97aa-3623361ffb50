'use client';

import React, { useState } from 'react';

export default function TestVoiceFixPage() {
  const [testText, setTestText] = useState('Hello, this is a test message with the number 14 and some creative content to analyze for voice tearing issues.');
  const [temperature, setTemperature] = useState(0.8);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const testTTS = async () => {
    setIsLoading(true);
    setResult(null);
    setAudioUrl(null);

    try {
      console.log(`Testing TTS with temperature: ${temperature}`);
      
      const response = await fetch('/api/natural-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText,
          voiceModel: 'aura-2-thalia-en',
          format: 'mp3',
          enableNaturalSpeech: true,
          naturalSpeechTemperature: temperature
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Extract quality metrics from headers
      const qualityMetrics = {
        qualityScore: response.headers.get('X-Quality-Score'),
        temperature: response.headers.get('X-Temperature'),
        processingTime: response.headers.get('X-Processing-Time'),
        potentialIssues: response.headers.get('X-Potential-Issues'),
        recommendations: response.headers.get('X-Quality-Recommendations'),
        naturalSpeechProcessed: response.headers.get('X-Natural-Speech-Processed'),
        tokensUsed: response.headers.get('X-Tokens-Used'),
        originalLength: response.headers.get('X-Original-Length'),
        processedLength: response.headers.get('X-Processed-Length')
      };

      setResult(qualityMetrics);

      // Create audio URL for playback
      const audioBlob = await response.blob();
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);

      console.log('TTS Quality Metrics:', qualityMetrics);

    } catch (error) {
      console.error('TTS test failed:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setIsLoading(false);
    }
  };

  const getQualityColor = (score: string | null) => {
    if (!score) return 'text-gray-500';
    const numScore = parseInt(score);
    if (numScore >= 80) return 'text-green-600';
    if (numScore >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRiskLevel = (score: string | null) => {
    if (!score) return 'UNKNOWN';
    const numScore = parseInt(score);
    if (numScore >= 80) return 'LOW';
    if (numScore >= 60) return 'MEDIUM';
    return 'HIGH';
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            🔧 Voice Tearing Fix Test
          </h1>
          <p className="text-gray-600 mb-6">
            Test the voice tearing fix by trying different creativity/temperature settings.
            The system now properly validates temperature and monitors audio quality.
          </p>

          {/* Test Configuration */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Test Text
              </label>
              <textarea
                value={testText}
                onChange={(e) => setTestText(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Enter text to test..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Creativity/Temperature: {temperature}
              </label>
              <input
                type="range"
                min="0.1"
                max="1.0"
                step="0.1"
                value={temperature}
                onChange={(e) => setTemperature(parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Conservative (0.1)</span>
                <span>Balanced (0.5)</span>
                <span>Creative (1.0)</span>
              </div>
            </div>
          </div>

          {/* Test Button */}
          <button
            onClick={testTTS}
            disabled={isLoading || !testText.trim()}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-6"
          >
            {isLoading ? 'Testing TTS...' : 'Test Voice Quality'}
          </button>

          {/* Results */}
          {result && (
            <div className="space-y-4">
              {result.error ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">❌ Test Failed</h3>
                  <p className="text-red-600">{result.error}</p>
                </div>
              ) : (
                <>
                  {/* Quality Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">📊 Quality Assessment</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className={`text-2xl font-bold ${getQualityColor(result.qualityScore)}`}>
                          {result.qualityScore || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-600">Quality Score</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-lg font-bold ${getQualityColor(result.qualityScore)}`}>
                          {getRiskLevel(result.qualityScore)}
                        </div>
                        <div className="text-sm text-gray-600">Risk Level</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {result.temperature || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-600">Temperature Used</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">
                          {result.potentialIssues || '0'}
                        </div>
                        <div className="text-sm text-gray-600">Issues Found</div>
                      </div>
                    </div>
                  </div>

                  {/* Detailed Metrics */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">🔍 Processing Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Natural Speech Processed:</span>
                        <span className="ml-2">{result.naturalSpeechProcessed === 'true' ? '✅ Yes' : '❌ No'}</span>
                      </div>
                      <div>
                        <span className="font-medium">Processing Time:</span>
                        <span className="ml-2">{result.processingTime}ms</span>
                      </div>
                      <div>
                        <span className="font-medium">Tokens Used:</span>
                        <span className="ml-2">{result.tokensUsed}</span>
                      </div>
                      <div>
                        <span className="font-medium">Text Length:</span>
                        <span className="ml-2">{result.originalLength} → {result.processedLength} chars</span>
                      </div>
                    </div>
                  </div>

                  {/* Recommendations */}
                  {result.recommendations && result.recommendations !== 'None' && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-yellow-800 mb-2">💡 Recommendation</h3>
                      <p className="text-yellow-700">{result.recommendations}</p>
                    </div>
                  )}

                  {/* Audio Player */}
                  {audioUrl && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-green-800 mb-3">🎵 Generated Audio</h3>
                      <audio controls className="w-full">
                        <source src={audioUrl} type="audio/mpeg" />
                        Your browser does not support the audio element.
                      </audio>
                      <p className="text-sm text-green-600 mt-2">
                        Listen carefully for any voice tearing or audio artifacts.
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
