<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Positioning Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .content {
            height: 200vh;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .content p {
            margin: 20px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chat Widget Positioning Test</h1>
        
        <div class="test-section">
            <h2>Test 1: Direct Iframe Embed</h2>
            <div class="test-description">
                This test embeds the chat widget directly using an iframe positioned at bottom-right corner.
                The widget should appear at the bottom-right with proper dimensions and black outline.
            </div>
            <button class="test-button" onclick="loadDirectIframe()">Load Direct Iframe</button>
            <div id="iframe-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>Test 2: Mobile-Optimized Script</h2>
            <div class="test-description">
                This test loads the mobile-optimized embed script. The widget should position correctly
                at bottom-right corner (not offset to the left) and work on both desktop and mobile.
            </div>
            <button class="test-button" onclick="loadMobileScript()">Load Mobile Script</button>
            <div id="mobile-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>Test 3: Simple Embed Script</h2>
            <div class="test-description">
                This test loads the simple embed script with proper positioning.
                The chat window should appear at bottom-right when clicked.
            </div>
            <button class="test-button" onclick="loadSimpleScript()">Load Simple Script</button>
            <div id="simple-status" class="status" style="display: none;"></div>
        </div>

        <div class="content">
            <h3>Scroll Test Content</h3>
            <p>This is test content to verify that the chat widget remains properly positioned when scrolling.</p>
            <p>The widget should stay fixed at the bottom-right corner regardless of scroll position.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
            <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
            <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores.</p>
            <p>Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio.</p>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, isSuccess) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `status ${isSuccess ? 'success' : 'error'}`;
            statusEl.style.display = 'block';
        }

        function loadDirectIframe() {
            try {
                // Remove any existing iframe
                const existing = document.getElementById('test-iframe');
                if (existing) existing.remove();

                const iframe = document.createElement('iframe');
                iframe.id = 'test-iframe';
                iframe.src = 'https://roo-bot-fusion-kgfs.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d?mode=chat-only&embed=true&useBlackOutline=true';
                iframe.style.position = 'fixed';
                iframe.style.bottom = '20px';
                iframe.style.right = '20px';
                iframe.style.width = '400px';
                iframe.style.height = '600px';
                iframe.style.border = 'none';
                iframe.style.borderRadius = '12px';
                iframe.style.zIndex = '9999';
                iframe.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                iframe.setAttribute('allow', 'microphone; autoplay');
                iframe.setAttribute('title', 'Chat Widget Test');

                document.body.appendChild(iframe);
                showStatus('iframe-status', '✅ Direct iframe loaded successfully! Check bottom-right corner.', true);
            } catch (error) {
                showStatus('iframe-status', `❌ Error loading iframe: ${error.message}`, false);
            }
        }

        function loadMobileScript() {
            try {
                // Remove any existing mobile widget
                const existing = document.getElementById('botfusion-mobile-button');
                if (existing) existing.remove();
                const existingChat = document.getElementById('botfusion-mobile-chat');
                if (existingChat) existingChat.remove();

                window.BOTFUSION_CHAT_ID = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';
                window.BOTFUSION_BASE_URL = 'https://roo-bot-fusion-kgfs.vercel.app';
                window.BOTFUSION_PRIMARY_COLOR = '#3b82f6';

                const script = document.createElement('script');
                script.src = 'https://roo-bot-fusion-kgfs.vercel.app/mobile-optimized-embed.js?v=2.0';
                script.async = true;
                script.onload = function() {
                    showStatus('mobile-status', '✅ Mobile script loaded successfully! Check bottom-right corner.', true);
                };
                script.onerror = function() {
                    showStatus('mobile-status', '❌ Error loading mobile script', false);
                };
                document.head.appendChild(script);
            } catch (error) {
                showStatus('mobile-status', `❌ Error: ${error.message}`, false);
            }
        }

        function loadSimpleScript() {
            try {
                // Remove any existing simple widget
                const existing = document.getElementById('botfusion-chat-button');
                if (existing) existing.remove();
                const existingIframe = document.getElementById('botfusion-chat-iframe');
                if (existingIframe) existingIframe.remove();

                const script = document.createElement('script');
                script.src = 'https://roo-bot-fusion-kgfs.vercel.app/simple-embed-script.js';
                script.async = true;
                script.onload = function() {
                    showStatus('simple-status', '✅ Simple script loaded successfully! Check bottom-right corner.', true);
                };
                script.onerror = function() {
                    showStatus('simple-status', '❌ Error loading simple script', false);
                };
                document.head.appendChild(script);
            } catch (error) {
                showStatus('simple-status', `❌ Error: ${error.message}`, false);
            }
        }

        // Add some debugging
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
        });
    </script>
</body>
</html>
