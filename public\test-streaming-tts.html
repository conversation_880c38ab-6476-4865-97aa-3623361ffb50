<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Streaming TTS Test - Phase 2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .performance-metrics {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .metric {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            background: white;
            border-radius: 3px;
            font-weight: bold;
        }
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .test-controls button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .test-controls .streaming {
            background: #4caf50;
            color: white;
        }
        .test-controls .rest {
            background: #2196f3;
            color: white;
        }
        .test-controls .voice {
            background: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 COMPLETE FIX: Streaming TTS Test</h1>
        <p><strong>Testing WebSocket LINEAR16 + WAV headers for fastest response/playback time</strong></p>
        
        <div class="instructions">
            <h4>📋 Test Instructions:</h4>
            <ol>
                <li><strong>Voice Session Test:</strong> Click microphone button, then send a message to trigger streaming TTS</li>
                <li><strong>Regular Text Test:</strong> Send a message without microphone to use REST TTS</li>
                <li><strong>Performance Comparison:</strong> Compare latency between streaming vs REST modes</li>
                <li><strong>Fallback Test:</strong> Test error handling and fallback mechanisms</li>
            </ol>
            <p><strong>Expected Results:</strong></p>
            <ul>
                <li>🚀 WebSocket TTS: LINEAR16 + WAV headers, instant audio playback</li>
                <li>📡 REST TTS: MP3 encoding, traditional loading</li>
                <li>🔄 Automatic mode switching based on microphone state</li>
                <li>🛡️ Comprehensive error handling for silent failures</li>
            </ul>
        </div>

        <div class="performance-metrics">
            <h4>📊 Performance Metrics (Target vs Actual):</h4>
            <div class="metric">TTFB Target: &lt;200ms</div>
            <div class="metric">Latency Reduction: 70-80%</div>
            <div class="metric">Streaming Mode: Voice Sessions</div>
            <div class="metric">Fallback: REST TTS</div>
        </div>

        <div class="test-section">
            <h3>🎯 Live Chat Interface Test</h3>
            <div class="status loading">Loading chat interface...</div>
            
            <div class="test-controls">
                <h4>Test Scenarios:</h4>
                <button class="streaming" onclick="testStreamingMode()">🚀 Test Streaming TTS</button>
                <button class="rest" onclick="testRestMode()">📡 Test REST TTS</button>
                <button class="voice" onclick="testVoiceSession()">🎤 Test Voice Session</button>
            </div>
            
            <div class="iframe-container">
                <iframe
                    src="/embed/test-streaming-tts?microphoneActive=false&autoPlay=true&showDebug=true"
                    title="Streaming TTS Test Chat"
                    allow="microphone; autoplay"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals">
                </iframe>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Development Console</h3>
            <p>Open browser developer tools (F12) to monitor:</p>
            <ul>
                <li><strong>Console Logs:</strong> TTS mode switching, WebSocket connections</li>
                <li><strong>Network Tab:</strong> REST API calls vs WebSocket connections</li>
                <li><strong>Performance:</strong> Audio loading times and latency</li>
                <li><strong>Errors:</strong> Connection failures and fallback behavior</li>
            </ul>
            
            <div class="status loading" id="console-status">
                Monitor browser console for real-time debugging information
            </div>
        </div>

        <div class="test-section">
            <h3>📈 Performance Validation</h3>
            <p>Expected improvements with Phase 2 streaming:</p>
            <ul>
                <li><strong>Time to First Byte (TTFB):</strong> ~600ms → &lt;200ms (70% reduction)</li>
                <li><strong>Perceived Latency:</strong> Significant reduction for voice sessions</li>
                <li><strong>User Experience:</strong> Near real-time audio feedback</li>
                <li><strong>Reliability:</strong> Automatic fallback to REST if streaming fails</li>
            </ul>
            
            <div class="performance-metrics">
                <h4>🎯 Success Criteria:</h4>
                <div class="metric">✅ WebSocket connection established</div>
                <div class="metric">✅ Audio chunks received progressively</div>
                <div class="metric">✅ Sub-200ms TTFB achieved</div>
                <div class="metric">✅ Fallback mechanism working</div>
                <div class="metric">✅ Mode switching functional</div>
            </div>
        </div>
    </div>

    <script>
        // Test control functions
        function testStreamingMode() {
            const iframe = document.querySelector('iframe');
            iframe.src = '/embed/test-streaming-tts?microphoneActive=true&autoPlay=true&showDebug=true&mode=streaming';
            updateStatus('Testing Streaming TTS mode...', 'loading');
        }

        function testRestMode() {
            const iframe = document.querySelector('iframe');
            iframe.src = '/embed/test-streaming-tts?microphoneActive=false&autoPlay=true&showDebug=true&mode=rest';
            updateStatus('Testing REST TTS mode...', 'loading');
        }

        function testVoiceSession() {
            const iframe = document.querySelector('iframe');
            iframe.src = '/embed/test-streaming-tts?microphoneActive=true&autoPlay=true&showDebug=true&mode=voice';
            updateStatus('Testing Voice Session with Streaming TTS...', 'loading');
        }

        function updateStatus(message, type) {
            const status = document.querySelector('.test-section .status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // Monitor iframe loading
        document.querySelector('iframe').onload = function() {
            updateStatus('Chat interface loaded successfully! Ready for testing.', 'success');
        };

        document.querySelector('iframe').onerror = function() {
            updateStatus('Error loading chat interface. Check console for details.', 'error');
        };

        // Performance monitoring
        let startTime = Date.now();
        
        // Log performance metrics
        console.log('🚀 Phase 2 Streaming TTS Test Started');
        console.log('📊 Monitoring performance metrics...');
        console.log('🎯 Target: Sub-200ms TTFB, 70-80% latency reduction');
        
        // Monitor for TTS events
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'tts-performance') {
                console.log('📈 TTS Performance:', event.data);
                
                const metrics = document.querySelector('.performance-metrics');
                const newMetric = document.createElement('div');
                newMetric.className = 'metric';
                newMetric.textContent = `${event.data.mode}: ${event.data.latency}ms`;
                metrics.appendChild(newMetric);
            }
        });
    </script>
</body>
</html>
