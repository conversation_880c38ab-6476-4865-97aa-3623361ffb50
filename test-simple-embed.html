<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Embed</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .important {
            background-color: #fffbea;
            border-left: 4px solid #f0b429;
            padding: 15px;
            margin-bottom: 20px;
        }
        .content {
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <h1>Test Simple Embed</h1>

    <div class="important">
        <strong>Important:</strong> This page has a Content Security Policy that allows inline scripts and scripts from the BotFusion domain.
        <br>
        <code>script-src https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app</code>
    </div>

    <div class="content">
        <h2>Simple Embed Test</h2>
        <p>This test demonstrates the simple embed code which should work with most CSP settings.</p>
        <p>The chat button should appear in the bottom-right corner of the page.</p>
    </div>

    <div class="content">
        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
    </div>

    <!-- Simple Embed Code -->
    <div id="botfusion-chat-button" style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border-radius: 50%; background-color: #3b82f6; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); cursor: pointer; z-index: 9999; display: flex; align-items: center; justify-content: center; transition: transform 0.3s ease, box-shadow 0.3s ease;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
    </div>

    <iframe id="botfusion-chat-iframe" src="https://roo-bot-fusion-kgfs-jx5wtpjub-tellivisions-projects.vercel.app/embed/b76a3980-9f8e-47cd-ae7d-f02747552c4d" style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); z-index: 9999; display: none;" allow="microphone"></iframe>

    <style>
        #botfusion-chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 480px) {
            #botfusion-chat-iframe {
                width: 100%;
                height: 100%;
                bottom: 0;
                right: 0;
                border-radius: 0;
            }
        }
    </style>

    <script>
        // Get the elements
        const button = document.getElementById('botfusion-chat-button');
        const iframe = document.getElementById('botfusion-chat-iframe');

        // Add click event to button
        button.onclick = function() {
            iframe.style.display = 'block';
            this.style.display = 'none';
        };

        // Add message listener for close events
        window.addEventListener('message', function(event) {
            if (event.data === 'botfusion-chat-close') {
                iframe.style.display = 'none';
                button.style.display = 'flex';
            }
        });
    </script>
</body>
</html>
