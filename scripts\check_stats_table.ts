const { createClient } = require('../src/lib/supabase/client')

async function checkStatsTable() {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('pg_tables')
    .select('*')
    .eq('tablename', 'chat_stats')

  if (error) {
    console.error('Error checking table:', error)
    return
  }

  console.log('Table exists:', data.length > 0)
  if (data.length > 0) {
    console.log('Table details:', data[0])
  }
}

checkStatsTable()