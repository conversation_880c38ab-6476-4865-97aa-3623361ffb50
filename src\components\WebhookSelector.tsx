'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'

export default function WebhookSelector({
  onSelect,
  initialUrl = ''
}: {
  onSelect: (url: string) => void
  initialUrl?: string
}) {
  const [url, setUrl] = useState(initialUrl)
  const [savedWebhooks, setSavedWebhooks] = useState<string[]>([])

  const handleSave = () => {
    if (!url) return
    if (!savedWebhooks.includes(url)) {
      setSavedWebhooks([...savedWebhooks, url])
    }
    onSelect(url)
  }

  return (
    <Card className="p-4 space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-neon-blue">
          Webhook URL
        </label>
        <Input
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="https://your-n8n-webhook.com"
        />
      </div>

      <Button onClick={handleSave} className="w-full">
        {initialUrl ? 'Update' : 'Save'} Webhook
      </Button>

      {savedWebhooks.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-neon-blue/70">
            Saved Webhooks
          </h3>
          <div className="space-y-1">
            {savedWebhooks.map((hook) => (
              <div
                key={hook}
                className="flex items-center justify-between p-2 text-sm border rounded hover:bg-gray-900/50 cursor-pointer"
                onClick={() => {
                  setUrl(hook)
                  onSelect(hook)
                }}
              >
                <span className="truncate">{hook}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  )
}