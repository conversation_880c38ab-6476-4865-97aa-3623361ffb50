#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add natural speech settings columns to the chat_interfaces table
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function addNaturalSpeechColumns() {
  console.log('Adding natural speech columns to chat_interfaces table...')

  try {
    // Add the columns one by one to avoid timeout issues
    const columns = [
      {
        name: 'enable_natural_speech',
        definition: 'BOOLEAN DEFAULT true',
        comment: 'Enable o3-mini processing for more natural speech patterns'
      },
      {
        name: 'natural_speech_model',
        definition: 'TEXT DEFAULT \'o3-mini\'',
        comment: 'OpenAI model to use for natural speech processing'
      },
      {
        name: 'natural_speech_temperature',
        definition: 'DECIMAL(3,2) DEFAULT 0.7',
        comment: 'Temperature setting for natural speech processing (0.0-1.0)'
      },
      {
        name: 'natural_speech_max_tokens',
        definition: 'INTEGER DEFAULT 500',
        comment: 'Maximum tokens for natural speech processing'
      }
    ]

    for (const column of columns) {
      console.log(`Adding column: ${column.name}`)
      
      const sql = `
        ALTER TABLE chat_interfaces 
        ADD COLUMN IF NOT EXISTS ${column.name} ${column.definition};
        
        COMMENT ON COLUMN chat_interfaces.${column.name} IS '${column.comment}';
      `

      const { error } = await supabase.rpc('execute_sql', { sql })
      
      if (error) {
        console.error(`Error adding column ${column.name}:`, error)
        // Continue with other columns even if one fails
      } else {
        console.log(`✓ Successfully added column: ${column.name}`)
      }
    }

    console.log('\nNatural speech columns migration completed!')
    
    // Verify the columns were added
    console.log('\nVerifying columns...')
    const { data, error: selectError } = await supabase
      .from('chat_interfaces')
      .select('enable_natural_speech, natural_speech_model, natural_speech_temperature, natural_speech_max_tokens')
      .limit(1)

    if (selectError) {
      console.error('Error verifying columns:', selectError)
    } else {
      console.log('✓ Columns verified successfully')
    }

  } catch (error) {
    console.error('Error during migration:', error)
    process.exit(1)
  }
}

// Run the migration
addNaturalSpeechColumns()
  .then(() => {
    console.log('Migration completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Migration failed:', error)
    process.exit(1)
  })
