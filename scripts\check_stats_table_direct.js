import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'

async function checkStatsTable() {
  const envFile = readFileSync('.env.local', 'utf8')
  const envVars = Object.fromEntries(
    envFile.split('\n')
      .filter(line => line.includes('='))
      .map(line => line.split('='))
  )

  const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables in .env.local')
    return
  }

  const supabase = createClient(supabaseUrl, supabaseKey)
  
  // Try to query the table directly
  // Try to create the table if it doesn't exist using raw SQL
  const { data, error } = await supabase
    .from('pg_tables')
    .select('*')
    .eq('tablename', 'chat_stats')
    .eq('schemaname', 'public');

  if (error) {
    console.error('Error checking table:', error);
    return;
  }

  if (data.length === 0) {
    console.log('Creating chat_stats table...');
    const { error: createError } = await supabase
      .from('pg_tables')
      .insert({
        sql: `
          CREATE TABLE chat_stats (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            session_id TEXT NOT NULL,
            user_id UUID REFERENCES auth.users(id),
            message_count INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE chat_stats ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY "Enable read access for all users" ON chat_stats
            FOR SELECT USING (true);
          
          CREATE POLICY "Enable insert access for authenticated users" ON chat_stats
            FOR INSERT WITH CHECK (auth.role() = 'authenticated');
        `
      });

    if (createError) {
      console.error('Error creating table:', createError);
      return;
    }
    console.log('Table created successfully');
  } else {
    console.log('Table already exists');
  }

  if (error) {
    console.error('Error checking table:', error)
    return
  }

  console.log('Table exists:', data.length > 0)
  if (data.length > 0) {
    console.log('Table details:', data[0])
  }
}

checkStatsTable()