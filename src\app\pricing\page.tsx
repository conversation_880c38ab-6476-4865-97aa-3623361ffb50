'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { motion } from 'framer-motion'
import { UserTier, getTierDisplayName, getTierPrice, getTierFeatures, TIER_CONFIGS } from '@/lib/tiers'
import Link from 'next/link'

export default function PricingPage() {
  const [isYearly, setIsYearly] = useState(false)

  const tiers: UserTier[] = ['free', 'standard', 'pro']

  const getTierIcon = (tier: UserTier) => {
    switch (tier) {
      case 'free': return '🆓'
      case 'standard': return '⭐'
      case 'pro': return '💎'
      default: return '🎯'
    }
  }

  const getTierColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'border-gray-600 bg-gray-900/50'
      case 'standard': return 'border-blue-500 bg-blue-900/20 ring-2 ring-blue-500/20'
      case 'pro': return 'border-purple-500 bg-purple-900/20 ring-2 ring-purple-500/20'
      default: return 'border-gray-600'
    }
  }

  const getButtonColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-600 hover:bg-gray-700'
      case 'standard': return 'bg-blue-600 hover:bg-blue-700'
      case 'pro': return 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
      default: return 'bg-gray-600'
    }
  }

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold text-white mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Unlock the full potential of BotFusion X with our flexible pricing plans. 
            Start free and upgrade as you grow.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <Label htmlFor="billing-toggle" className="text-white">Monthly</Label>
            <Switch
              id="billing-toggle"
              checked={isYearly}
              onCheckedChange={setIsYearly}
            />
            <Label htmlFor="billing-toggle" className="text-white">
              Yearly
              <Badge className="ml-2 bg-green-600 text-white">Save 20%</Badge>
            </Label>
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {tiers.map((tier, index) => {
            const pricing = getTierPrice(tier)
            const features = getTierFeatures(tier)
            const config = TIER_CONFIGS[tier]
            const price = isYearly ? pricing.yearly : pricing.monthly
            const displayPrice = isYearly ? Math.round(pricing.yearly / 12) : pricing.monthly

            return (
              <motion.div
                key={tier}
                initial="hidden"
                animate="visible"
                variants={fadeIn}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className={`relative p-8 h-full ${getTierColor(tier)} transition-all duration-300 hover:scale-105`}>
                  {tier === 'standard' && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white">
                      Most Popular
                    </Badge>
                  )}

                  <div className="text-center mb-6">
                    <div className="text-4xl mb-2">{getTierIcon(tier)}</div>
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {getTierDisplayName(tier)}
                    </h3>
                    <div className="text-4xl font-bold text-white mb-1">
                      ${displayPrice}
                      {tier !== 'free' && <span className="text-lg text-gray-400">/month</span>}
                    </div>
                    {tier !== 'free' && isYearly && (
                      <p className="text-sm text-green-400">
                        Billed yearly (${pricing.yearly}/year)
                      </p>
                    )}
                    {tier === 'free' && (
                      <p className="text-sm text-gray-400">Forever free</p>
                    )}
                  </div>

                  <div className="space-y-4 mb-8">
                    <div className="space-y-3">
                      {/* Chat Interfaces */}
                      <div className="flex items-center gap-3">
                        <span className="text-green-400">✓</span>
                        <span className="text-gray-300">
                          {config.maxChatInterfaces === -1 ? 'Unlimited' : config.maxChatInterfaces} Chat Interface{config.maxChatInterfaces === 1 ? '' : 's'}
                        </span>
                      </div>

                      {/* Voice Features */}
                      <div className="flex items-center gap-3">
                        <span className={config.voiceFeaturesEnabled ? 'text-green-400' : 'text-red-400'}>
                          {config.voiceFeaturesEnabled ? '✓' : '✗'}
                        </span>
                        <span className="text-gray-300">
                          {config.voiceFeaturesEnabled 
                            ? `Voice Features (${config.voiceModels.length} voices)` 
                            : 'Voice Features'
                          }
                        </span>
                      </div>

                      {/* Branding */}
                      <div className="flex items-center gap-3">
                        <span className={config.brandingRemoval ? 'text-green-400' : 'text-red-400'}>
                          {config.brandingRemoval ? '✓' : '✗'}
                        </span>
                        <span className="text-gray-300">Branding Removal</span>
                      </div>

                      {/* Advanced Customization */}
                      <div className="flex items-center gap-3">
                        <span className={config.advancedCustomization ? 'text-green-400' : 'text-red-400'}>
                          {config.advancedCustomization ? '✓' : '✗'}
                        </span>
                        <span className="text-gray-300">Advanced Customization</span>
                      </div>

                      {/* White Labeling */}
                      <div className="flex items-center gap-3">
                        <span className={config.whiteLabelingEnabled ? 'text-green-400' : 'text-red-400'}>
                          {config.whiteLabelingEnabled ? '✓' : '✗'}
                        </span>
                        <span className="text-gray-300">White Labeling</span>
                      </div>

                      {/* Analytics */}
                      <div className="flex items-center gap-3">
                        <span className={config.analyticsDashboard ? 'text-green-400' : 'text-red-400'}>
                          {config.analyticsDashboard ? '✓' : '✗'}
                        </span>
                        <span className="text-gray-300">Analytics Dashboard</span>
                      </div>

                      {/* Support */}
                      <div className="flex items-center gap-3">
                        <span className="text-green-400">✓</span>
                        <span className="text-gray-300">
                          {config.prioritySupport ? 'Priority Support' : 'Email Support'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Button
                    className={`w-full ${getButtonColor(tier)} text-white`}
                    asChild
                  >
                    <Link href={tier === 'free' ? '/dashboard' : `/upgrade?tier=${tier}&billing=${isYearly ? 'yearly' : 'monthly'}`}>
                      {tier === 'free' ? 'Get Started' : 'Upgrade Now'}
                    </Link>
                  </Button>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* FAQ Section */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-8">Frequently Asked Questions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="bg-white/10 border-gray-600 p-6 text-left">
              <h3 className="text-lg font-semibold text-white mb-2">Can I change plans anytime?</h3>
              <p className="text-gray-300">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </Card>
            <Card className="bg-white/10 border-gray-600 p-6 text-left">
              <h3 className="text-lg font-semibold text-white mb-2">Is there a free trial?</h3>
              <p className="text-gray-300">Our Free tier is available forever with no time limits. Upgrade when you need more features.</p>
            </Card>
            <Card className="bg-white/10 border-gray-600 p-6 text-left">
              <h3 className="text-lg font-semibold text-white mb-2">What payment methods do you accept?</h3>
              <p className="text-gray-300">We accept all major credit cards and PayPal. All payments are processed securely.</p>
            </Card>
            <Card className="bg-white/10 border-gray-600 p-6 text-left">
              <h3 className="text-lg font-semibold text-white mb-2">Do you offer refunds?</h3>
              <p className="text-gray-300">Yes, we offer a 30-day money-back guarantee for all paid plans.</p>
            </Card>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-4">Ready to get started?</h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of businesses using BotFusion X to enhance their customer engagement.
          </p>
          <Button
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            asChild
          >
            <Link href="/dashboard">Start Building Today</Link>
          </Button>
        </motion.div>
      </div>
    </div>
  )
}
