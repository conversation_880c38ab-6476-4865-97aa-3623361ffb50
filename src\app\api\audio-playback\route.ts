import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import os from 'os';
import crypto from 'crypto';

// Create a temporary directory for audio files
const TEMP_DIR = path.join(os.tmpdir(), 'audio-playback');

// Ensure the temp directory exists
try {
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
    console.log(`Created temporary directory: ${TEMP_DIR}`);
  }
} catch (error) {
  console.error('Failed to create temporary directory:', error);
}

// Clean up old files (files older than 1 hour)
function cleanupOldFiles() {
  try {
    const files = fs.readdirSync(TEMP_DIR);
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    for (const file of files) {
      const filePath = path.join(TEMP_DIR, file);
      const stats = fs.statSync(filePath);

      if (stats.mtimeMs < oneHourAgo) {
        fs.unlinkSync(filePath);
        console.log(`Deleted old audio file: ${filePath}`);
      }
    }
  } catch (error) {
    console.error('Error cleaning up old files:', error);
  }
}

// Clean up old files on startup
cleanupOldFiles();

export async function POST(request: NextRequest) {
  try {
    // Parse the multipart form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    // Generate a unique filename using crypto instead of uuid
    const filename = `${crypto.randomBytes(16).toString('hex')}.mp3`;
    const filePath = path.join(TEMP_DIR, filename);

    // Convert the file to a buffer
    const buffer = Buffer.from(await audioFile.arrayBuffer());

    // Write the buffer to a file
    fs.writeFileSync(filePath, buffer);
    console.log(`Saved audio file: ${filePath}`);

    // Generate a URL for the file
    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : request.headers.get('origin') || '';

    const audioUrl = `${baseUrl}/api/audio-playback/${filename}`;

    // Return the URL
    return NextResponse.json({ audioUrl });
  } catch (error) {
    console.error('Error handling audio playback:', error);
    return NextResponse.json(
      { error: 'Failed to process audio file' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
