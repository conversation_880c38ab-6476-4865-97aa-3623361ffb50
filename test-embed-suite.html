<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BotFusion Embed Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-card h2 {
            margin-top: 0;
            color: #3b82f6;
            font-size: 1.2rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .test-card p {
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        .test-card .csp-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
            margin-bottom: 15px;
            overflow-x: auto;
        }
        .test-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background-color: #2563eb;
        }
        .note {
            background-color: #fffde7;
            padding: 15px;
            border-left: 4px solid #ffd600;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>BotFusion Embed Test Suite</h1>

    <div class="note">
        <strong>Important:</strong> This test suite allows you to test all embed options with various Content Security Policy settings.
        Each test opens in a new tab with the specified CSP settings.
    </div>

    <h2>Standard Embed Tests</h2>
    <div class="test-grid">
        <div class="test-card">
            <h2>Standard Embed - No CSP</h2>
            <p>Tests the standard script embed with no Content Security Policy restrictions.</p>
            <button class="test-button" onclick="openTest('standard', 'none')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>Standard Embed - Basic CSP</h2>
            <p>Tests the standard script embed with a basic Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'self' 'unsafe-inline' ORIGIN; connect-src 'self' ORIGIN;</div>
            <button class="test-button" onclick="openTest('standard', 'basic')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>Standard Embed - Moderate CSP</h2>
            <p>Tests the standard script embed with a moderate Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self' ORIGIN;</div>
            <button class="test-button" onclick="openTest('standard', 'moderate')">Run Test</button>
        </div>
    </div>

    <h2>CSP-Friendly Embed Tests</h2>
    <div class="test-grid">
        <div class="test-card">
            <h2>CSP-Friendly - Basic CSP</h2>
            <p>Tests the CSP-friendly embed with a basic Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'self' ORIGIN; connect-src 'self' ORIGIN;</div>
            <button class="test-button" onclick="openTest('csp', 'basic')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>CSP-Friendly - Moderate CSP</h2>
            <p>Tests the CSP-friendly embed with a moderate Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'self'; connect-src 'self' ORIGIN;</div>
            <button class="test-button" onclick="openTest('csp', 'moderate')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>CSP-Friendly - Strict CSP</h2>
            <p>Tests the CSP-friendly embed with a strict Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'self'; connect-src 'self';</div>
            <button class="test-button" onclick="openTest('csp', 'strict')">Run Test</button>
        </div>
    </div>

    <h2>HTML-Only Embed Tests</h2>
    <div class="test-grid">
        <div class="test-card">
            <h2>HTML-Only - No Scripts</h2>
            <p>Tests the HTML-only embed with a CSP that blocks all scripts.</p>
            <div class="csp-info">default-src 'self'; script-src 'none'; style-src 'self' 'unsafe-inline'; frame-src ORIGIN;</div>
            <button class="test-button" onclick="openTest('html-only', 'no-scripts')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>HTML-Only - Very Strict CSP</h2>
            <p>Tests the HTML-only embed with a very strict Content Security Policy.</p>
            <div class="csp-info">default-src 'self'; script-src 'none'; style-src 'self'; frame-src ORIGIN;</div>
            <button class="test-button" onclick="openTest('html-only', 'very-strict')">Run Test</button>
        </div>
    </div>

    <h2>Pure HTML Embed Tests</h2>
    <div class="test-grid">
        <div class="test-card">
            <h2>Pure HTML - Strictest CSP</h2>
            <p>Tests the pure HTML embed with the strictest possible Content Security Policy.</p>
            <div class="csp-info">default-src 'none'; frame-src ORIGIN;</div>
            <button class="test-button" onclick="openTest('pure-html', 'strictest')">Run Test</button>
        </div>

        <div class="test-card">
            <h2>Pure HTML - No Default Src</h2>
            <p>Tests the pure HTML embed with a CSP that has no default-src.</p>
            <div class="csp-info">script-src 'none'; style-src 'none'; frame-src ORIGIN;</div>
            <button class="test-button" onclick="openTest('pure-html', 'no-default')">Run Test</button>
        </div>
    </div>

    <script>
        // Get the origin for the embed
        const ORIGIN = 'https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app';
        const CHAT_ID = 'b76a3980-9f8e-47cd-ae7d-f02747552c4d';

        // Function to open a test in a new tab
        function openTest(embedType, cspType) {
            const testWindow = window.open('', '_blank', 'width=800,height=600');
            if (!testWindow) {
                alert('Please allow popups to run the tests');
                return;
            }

            let cspHeader = '';
            let embedCode = '';

            // Set CSP header based on test type
            switch (embedType) {
                case 'standard':
                    if (cspType === 'none') {
                        cspHeader = '';
                    } else if (cspType === 'basic') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' ${ORIGIN}; connect-src 'self' ${ORIGIN};">`;
                    } else if (cspType === 'moderate') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self' ${ORIGIN};">`;
                    }

                    embedCode = `
                    <!-- BotFusion Chat Widget -->
                    <script type="text/javascript">
                    (function(w, d, s, o, f, js, fjs) {
                        w['BotFusion-Widget'] = o;
                        w[o] = w[o] || function() {
                            (w[o].q = w[o].q || []).push(arguments);
                        };
                        js = d.createElement(s);
                        fjs = d.getElementsByTagName(s)[0];
                        js.id = o;
                        js.src = f;
                        js.async = 1;
                        fjs.parentNode.insertBefore(js, fjs);
                    }(window, document, 'script', 'bf', '${ORIGIN}/api/embed-script'));

                    bf('init', {
                        chatId: "${CHAT_ID}",
                        position: 'right',
                        primaryColor: "#3b82f6",
                        userBubbleColor: "#ffffff",
                        botBubbleColor: "#3b82f6",
                        userTextColor: "#000000",
                        botTextColor: "#ffffff",
                        logoUrl: "",
                        darkMode: false,
                        greeting: "Hello! How can I help you today?"
                    });
                    </script>`;
                    break;

                case 'csp':
                    if (cspType === 'basic') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' ${ORIGIN}; connect-src 'self' ${ORIGIN};">`;
                    } else if (cspType === 'moderate') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; connect-src 'self' ${ORIGIN};">`;
                    } else if (cspType === 'strict') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; connect-src 'self';">`;
                    }

                    embedCode = `
                    <!-- BotFusion Chat Widget - CSP-Friendly Version -->
                    <script src="${ORIGIN}/api/csp-embed-script?chatId=${CHAT_ID}"></script>`;
                    break;

                case 'html-only':
                    if (cspType === 'no-scripts') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'none'; style-src 'self' 'unsafe-inline'; frame-src ${ORIGIN};">`;
                    } else if (cspType === 'very-strict') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'none'; style-src 'self'; frame-src ${ORIGIN};">`;
                    }

                    embedCode = `
                    <!-- BotFusion Chat Widget - HTML-Only Version -->
                    <iframe
                      src="${ORIGIN}/api/html-embed?chatId=${CHAT_ID}"
                      style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
                      frameborder="0"
                      title="Chat Widget"
                      loading="lazy"
                      allow="microphone"
                    ></iframe>`;
                    break;

                case 'pure-html':
                    if (cspType === 'strictest') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="default-src 'none'; frame-src ${ORIGIN};">`;
                    } else if (cspType === 'no-default') {
                        cspHeader = `<meta http-equiv="Content-Security-Policy" content="script-src 'none'; style-src 'none'; frame-src ${ORIGIN};">`;
                    }

                    embedCode = `
                    <!-- BotFusion Chat Widget - Pure HTML Version -->
                    <iframe
                      src="${ORIGIN}/api/pure-html-embed?chatId=${CHAT_ID}"
                      style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
                      frameborder="0"
                      title="Chat Widget"
                      loading="lazy"
                    ></iframe>`;
                    break;
            }

            // Write the test page
            testWindow.document.write(`
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>BotFusion Embed Test: ${embedType} - ${cspType}</title>
                    ${cspHeader}
                    <style>
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 20px;
                            color: #333;
                        }
                        h1 {
                            color: #3b82f6;
                        }
                        .test-info {
                            background-color: #f8f9fa;
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .csp-display {
                            background-color: #f1f1f1;
                            padding: 10px;
                            border-radius: 4px;
                            font-family: monospace;
                            font-size: 0.9rem;
                            overflow-x: auto;
                            margin-top: 10px;
                        }
                        .content {
                            min-height: 1000px;
                            padding: 20px;
                            background-color: #f9f9f9;
                            border-radius: 8px;
                            margin-top: 20px;
                        }
                        .console-log {
                            background-color: #282c34;
                            color: #abb2bf;
                            padding: 15px;
                            border-radius: 8px;
                            font-family: monospace;
                            margin-top: 20px;
                            max-height: 200px;
                            overflow-y: auto;
                        }
                        .console-log .log-entry {
                            margin-bottom: 5px;
                        }
                        .console-log .error {
                            color: #e06c75;
                        }
                        .console-log .info {
                            color: #61afef;
                        }
                    </style>
                </head>
                <body>
                    <h1>BotFusion Embed Test</h1>

                    <div class="test-info">
                        <h2>Test Configuration</h2>
                        <p><strong>Embed Type:</strong> ${embedType}</p>
                        <p><strong>CSP Type:</strong> ${cspType}</p>
                        ${cspHeader ? `<p><strong>Content Security Policy:</strong></p><div class="csp-display">${cspHeader.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</div>` : '<p><strong>Content Security Policy:</strong> None</p>'}
                    </div>

                    <div class="content">
                        <h2>Sample Content</h2>
                        <p>This is a test page for the BotFusion Chat Widget. The chat button should appear in the bottom-right corner of the page.</p>
                        <p>If you see any errors, they will be displayed in the console log section below.</p>

                        <div style="height: 300px;"></div>

                        <h2>More Content</h2>
                        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
                    </div>

                    <div class="console-log" id="console-log">
                        <div class="log-entry info">Console Log:</div>
                    </div>

                    ${embedCode}

                    <script>
                        // Override console methods to display in the console log div
                        const consoleLog = document.getElementById('console-log');

                        if (consoleLog) {
                            const originalConsoleLog = console.log;
                            const originalConsoleError = console.error;
                            const originalConsoleInfo = console.info;

                            console.log = function() {
                                const entry = document.createElement('div');
                                entry.className = 'log-entry';
                                entry.textContent = Array.from(arguments).join(' ');
                                consoleLog.appendChild(entry);
                                originalConsoleLog.apply(console, arguments);
                            };

                            console.error = function() {
                                const entry = document.createElement('div');
                                entry.className = 'log-entry error';
                                entry.textContent = 'ERROR: ' + Array.from(arguments).join(' ');
                                consoleLog.appendChild(entry);
                                originalConsoleError.apply(console, arguments);
                            };

                            console.info = function() {
                                const entry = document.createElement('div');
                                entry.className = 'log-entry info';
                                entry.textContent = 'INFO: ' + Array.from(arguments).join(' ');
                                consoleLog.appendChild(entry);
                                originalConsoleInfo.apply(console, arguments);
                            };

                            // Log page load
                            console.info('Page loaded at ' + new Date().toLocaleTimeString());

                            // Log errors
                            window.addEventListener('error', function(event) {
                                console.error('Global error: ' + event.message + ' at ' + event.filename + ':' + event.lineno);
                            });
                        }
                    </script>
                </body>
                </html>
            `);

            testWindow.document.close();
        }
    </script>
</body>
</html>
