import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Log API key status at initialization time
console.log('Auth Token API: API_ACCESS_TOKEN configured:', !!process.env.API_ACCESS_TOKEN);
console.log('Auth Token API: DEEPGRAM_API_KEY configured:', !!process.env.DEEPGRAM_API_KEY);

// This is a simplified authentication token endpoint
export async function GET(req: NextRequest) {
  console.log('Auth Token API: Request received');

  try {
    // In a real application, you would validate the user's session or credentials
    // For this example, we'll use a simple API key approach

    // Check if the user has a valid session cookie
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get('session');

    // For demo purposes, we'll skip the session check and always return a token
    // In a production app, you would validate the session here

    // Generate a token - using the API_ACCESS_TOKEN or Deepgram API key as a simple solution
    // In a real app, you would use a proper JWT or other token system
    const token = process.env.API_ACCESS_TOKEN || process.env.DEEPGRAM_API_KEY;

    if (!token) {
      console.error('Auth Token API: No API access token or Deepgram API key configured');
      return NextResponse.json({
        error: 'Authentication configuration error',
        message: 'No API token is configured on the server.'
      }, {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // For development purposes, we'll create a temporary token if in development mode
    // This is not secure for production
    let responseToken = token;
    if (process.env.NODE_ENV === 'development') {
      // In development, we can use a fixed token for easier debugging
      responseToken = 'dev-token-' + Date.now();
      console.log('Auth Token API: Development mode - using temporary token');
    }

    console.log('Auth Token API: Token generated successfully');

    // Return the token
    return NextResponse.json({ token: responseToken }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error: any) {
    console.error('Auth Token API: Error generating token:', error);

    return NextResponse.json({
      error: 'Authentication failed',
      message: error.message || 'An unexpected error occurred during authentication.'
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  console.log('Auth Token API: OPTIONS request received (CORS preflight)');
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, Cache-Control, Pragma, Expires',
      'Access-Control-Max-Age': '86400',
      'Access-Control-Allow-Credentials': 'true',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}
