'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Link from 'next/link'

export default function NewChatPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Create New Chat Interface</h2>
        <Button variant="outline" asChild>
          <Link href="/dashboard">Cancel</Link>
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input id="name" placeholder="My Support Bot" />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="A chat interface for customer support"
          />
        </div>

        <div>
          <Label htmlFor="webhook">n8n Webhook URL</Label>
          <Input
            id="webhook"
            type="url"
            placeholder="https://your-n8n-instance.com/webhook/123"
          />
        </div>

        <div className="pt-4">
          <Button className="w-full">Create Chat Interface</Button>
        </div>
      </div>
    </div>
  )
}