'use client'

import { useEffect } from 'react'

export default function HoverEffectsProvider() {
  useEffect(() => {
    // Function to add hover effects using JavaScript
    const addHoverEffects = () => {
      // Get all elements with hover classes
      const glassCards = document.querySelectorAll('.glass-card')
      const glassButtons = document.querySelectorAll('.glass-button')
      const buttonGlowHovers = document.querySelectorAll('.button-glow-hover')
      const shadowGlowBlues = document.querySelectorAll('.shadow-glow-blue')
      const ambientGlows = document.querySelectorAll('.ambient-glow')

      // Add mouseenter/mouseleave event listeners to glass cards
      glassCards.forEach((card) => {
        card.addEventListener('mouseenter', () => {
          card.setAttribute('data-hover', 'true')
          card.style.boxShadow = '0 0 12px var(--glow-color)'
          card.style.borderColor = 'rgba(59, 130, 246, 0.5)'
        })
        card.addEventListener('mouseleave', () => {
          card.removeAttribute('data-hover')
          card.style.boxShadow = 'var(--glow-shadow)'
          card.style.borderColor = 'var(--card-border)'
        })
      })

      // Add mouseenter/mouseleave event listeners to glass buttons
      glassButtons.forEach((button) => {
        button.addEventListener('mouseenter', () => {
          button.setAttribute('data-hover', 'true')
          button.style.boxShadow = '0 0 12px var(--glow-color)'
          button.style.borderColor = 'rgba(59, 130, 246, 0.5)'
        })
        button.addEventListener('mouseleave', () => {
          button.removeAttribute('data-hover')
          button.style.boxShadow = 'var(--glow-shadow)'
          button.style.borderColor = 'var(--card-border)'
        })
      })

      // Add mouseenter/mouseleave event listeners to button glow hovers
      buttonGlowHovers.forEach((button) => {
        button.addEventListener('mouseenter', () => {
          button.setAttribute('data-hover', 'true')
          button.style.boxShadow = '0 0 8px var(--glow-color)'
        })
        button.addEventListener('mouseleave', () => {
          button.removeAttribute('data-hover')
          button.style.boxShadow = ''
        })
      })

      // Add mouseenter/mouseleave event listeners to shadow glow blues
      shadowGlowBlues.forEach((element) => {
        element.addEventListener('mouseenter', () => {
          element.setAttribute('data-hover', 'true')
          element.style.boxShadow = '0 0 8px var(--glow-color)'
        })
        element.addEventListener('mouseleave', () => {
          element.removeAttribute('data-hover')
          element.style.boxShadow = '0 0 4px var(--glow-color)'
        })
      })

      // Add mouseenter/mouseleave event listeners to ambient glows
      ambientGlows.forEach((element) => {
        element.addEventListener('mouseenter', () => {
          element.setAttribute('data-hover', 'true')
          element.style.boxShadow = '0 0 12px var(--glow-color)'
          element.style.borderColor = 'rgba(59, 130, 246, 0.5)'
        })
        element.addEventListener('mouseleave', () => {
          element.removeAttribute('data-hover')
          element.style.boxShadow = '0 0 2px rgba(59, 130, 246, 0.2)'
          element.style.borderColor = 'rgba(59, 130, 246, 0.2)'
        })
      })
    }

    // Add the hover effects
    addHoverEffects()

    // Set up a MutationObserver to handle dynamically added elements
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          addHoverEffects()
        }
      })
    })

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true })

    // Clean up the observer when the component unmounts
    return () => {
      observer.disconnect()
    }
  }, [])

  // This component doesn't render anything
  return null
}
