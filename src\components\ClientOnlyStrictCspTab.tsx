'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'

export function ClientOnlyStrictCspTab({ chatId, chatName }: { chatId: string, chatName: string }) {
  const [htmlOnlyCode, setHtmlOnlyCode] = useState('')
  const [pureHtmlCode, setPureHtmlCode] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Fetch the embed code from the API
    const fetchEmbedCode = async () => {
      try {
        const response = await fetch(`/api/generate-embed-code?chatId=${chatId}`);
        if (response.ok) {
          const data = await response.json();
          setHtmlOnlyCode(data.htmlOnlyEmbedCode);
          setPureHtmlCode(data.pureHtmlEmbedCode);
        } else {
          console.error('Failed to fetch embed code');
          // Fallback to a basic embed code
          const siteOrigin = window.location.origin;
          setHtmlOnlyCode(`<!-- BotFusion Chat Widget - HTML-Only Version (for sites with strict CSP) -->
<!-- This version works with script-src 'none' CSP settings -->
<iframe
  src="${siteOrigin}/api/html-embed?chatId=${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%; box-shadow: none !important; filter: none !important;"
  title="Chat Widget"
  loading="lazy"
  allow="microphone"
></iframe>`);

          setPureHtmlCode(`<!-- BotFusion Chat Widget - Pure HTML Version (for sites with the strictest CSP) -->
<!-- This version works with ANY Content Security Policy, even the most restrictive ones -->
<iframe
  src="${siteOrigin}/api/pure-html-embed?chatId=${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%; box-shadow: none !important; filter: none !important;"
  title="Chat Widget"
  loading="lazy"
></iframe>`);
        }
      } catch (error) {
        console.error('Error fetching embed code:', error);
        // Use fallback code here too
        const siteOrigin = window.location.origin;
        setHtmlOnlyCode(`<!-- BotFusion Chat Widget - HTML-Only Version (for sites with strict CSP) -->
<!-- This version works with script-src 'none' CSP settings -->
<iframe
  src="${siteOrigin}/api/html-embed?chatId=${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%; box-shadow: none !important; filter: none !important;"
  title="Chat Widget"
  loading="lazy"
  allow="microphone"
></iframe>`);

        setPureHtmlCode(`<!-- BotFusion Chat Widget - Pure HTML Version (for sites with the strictest CSP) -->
<!-- This version works with ANY Content Security Policy, even the most restrictive ones -->
<iframe
  src="${siteOrigin}/api/pure-html-embed?chatId=${chatId}"
  style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%; box-shadow: none !important; filter: none !important;"
  title="Chat Widget"
  loading="lazy"
></iframe>`);
      }
    };

    fetchEmbedCode();
  }, [chatId, chatName])

  const { isCopied: isHtmlOnlyCopied, copyToClipboard: copyHtmlOnly } = useCopyToClipboard(2000)
  const { isCopied: isPureHtmlCopied, copyToClipboard: copyPureHtml } = useCopyToClipboard(2000)

  if (!isClient) {
    return <div className="p-4 text-gray-400">Loading strict CSP embed code...</div>
  }

  return (
    <div>
      <h3 className="text-lg font-medium text-neon-blue mb-4">Strict CSP Embed Options</h3>

      <Tabs defaultValue="html-only" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4 bg-gray-800 text-gray-400">
          <TabsTrigger value="html-only" className="data-[state=active]:bg-gray-700 data-[state=active]:text-neon-blue">
            HTML-Only
          </TabsTrigger>
          <TabsTrigger value="pure-html" className="data-[state=active]:bg-gray-700 data-[state=active]:text-neon-blue">
            Pure HTML
          </TabsTrigger>
        </TabsList>

        <TabsContent value="html-only">
          <div className="relative">
            <div className="flex justify-end mb-2">
              <Button
                onClick={() => copyHtmlOnly(htmlOnlyCode, 'HTML-Only embed code copied to clipboard!')}
                className="glass-card hover:shadow-glow-blue"
                size="sm"
              >
                {isHtmlOnlyCopied ? 'COPIED' : 'Copy'}
              </Button>
            </div>
            <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
              {htmlOnlyCode}
            </pre>
          </div>
          <p className="text-sm text-gray-400 mt-3">
            <strong>HTML-Only Embed</strong> - Use this for sites with strict CSP that blocks JavaScript.
            <br /><br />
            This option:
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Works with <code className="bg-gray-700 px-1 py-0.5 rounded text-xs">script-src &apos;none&apos;</code> CSP settings</li>
              <li>Uses minimal JavaScript contained within the iframe</li>
              <li>Provides the full chat experience</li>
              <li>Only requires allowing frames from our domain</li>
            </ul>
            <br />
            <strong>Example CSP that works with this option:</strong>
            <pre className="bg-gray-700 p-2 rounded text-xs mt-2 overflow-x-auto">
              Content-Security-Policy: default-src &apos;self&apos;; script-src &apos;none&apos;; style-src &apos;self&apos; &apos;unsafe-inline&apos;; frame-src {window.location.origin}
            </pre>
          </p>
        </TabsContent>

        <TabsContent value="pure-html">
          <div className="relative">
            <div className="flex justify-end mb-2">
              <Button
                onClick={() => copyPureHtml(pureHtmlCode, 'Pure HTML embed code copied to clipboard!')}
                className="glass-card hover:shadow-glow-blue"
                size="sm"
              >
                {isPureHtmlCopied ? 'COPIED' : 'Copy'}
              </Button>
            </div>
            <pre className="bg-gray-800 border border-gray-700 rounded-md p-3 text-white text-sm overflow-auto max-h-[300px] scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 whitespace-pre-wrap">
              {pureHtmlCode}
            </pre>
          </div>
          <p className="text-sm text-gray-400 mt-3">
            <strong>Pure HTML Embed</strong> - Use this for sites with the strictest possible CSP.
            <br /><br />
            This option:
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Works with <strong>ANY</strong> Content Security Policy</li>
              <li>Contains <strong>NO JavaScript</strong> at all</li>
              <li>Opens the chat in a new tab when clicked</li>
              <li>Is the most compatible option for extremely restricted environments</li>
            </ul>
            <br />
            <strong>Example of the strictest possible CSP that still works:</strong>
            <pre className="bg-gray-700 p-2 rounded text-xs mt-2 overflow-x-auto">
              Content-Security-Policy: default-src &apos;none&apos;; frame-src {window.location.origin}
            </pre>
            <br />
            <strong>Note:</strong> This option will open the chat in a new tab when clicked, rather than showing an inline chat window.
          </p>
        </TabsContent>
      </Tabs>
    </div>
  )
}
