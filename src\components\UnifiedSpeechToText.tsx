import React, { forwardRef } from 'react';
import { STTProvider } from '@/types/deepgram-stt';
import { useSpeechToText, useSpeechToTextErrorHandlers } from '@/contexts/SpeechToTextContext';
import DeepgramSpeechToText from './DeepgramSpeechToText';
import SpeechToText from './SpeechToText'; // Original Web Speech API component
import { SpeechToTextRef } from './SpeechToText'; // Import the original ref type

// Use the same props interface as the original SpeechToText
interface UnifiedSpeechToTextProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
  color?: string;
  onMicrophoneActiveChange?: (active: boolean) => void;
  autoSend?: boolean;
  onSend?: (transcript?: string) => void;
}

const UnifiedSpeechToText = forwardRef<SpeechToTextRef, UnifiedSpeechToTextProps>((props, ref) => {
  const { currentProvider, fallbackReason } = useSpeechToText();
  const { handleDeepgramError, handleWebSpeechError } = useSpeechToTextErrorHandlers();

  // Enhanced props with error handling
  const enhancedProps = {
    ...props,
    onError: (error: Error) => {
      console.error('[UnifiedSTT] Error:', error);
      
      // Handle provider-specific errors
      if (currentProvider === STTProvider.DEEPGRAM) {
        handleDeepgramError();
      } else if (currentProvider === STTProvider.WEB_SPEECH) {
        handleWebSpeechError();
      }
    }
  };

  // Log current provider for debugging
  React.useEffect(() => {
    console.log('[UnifiedSTT] Using provider:', currentProvider, fallbackReason ? `(${fallbackReason})` : '');
  }, [currentProvider, fallbackReason]);

  // Render appropriate component based on current provider
  switch (currentProvider) {
    case STTProvider.DEEPGRAM:
      return <DeepgramSpeechToText ref={ref} {...enhancedProps} />;
      
    case STTProvider.WEB_SPEECH:
      return <SpeechToText ref={ref} {...props} />;
      
    case STTProvider.DISABLED:
      // Return disabled button that matches the original design
      return (
        <button
          type="button"
          disabled={true}
          style={{
            backgroundColor: '#9ca3af',
            minWidth: '36px',
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            cursor: 'not-allowed',
            opacity: 0.5
          }}
          title={fallbackReason || 'Speech recognition not available'}
          aria-label="Speech recognition disabled"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
            <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
            <line x1="12" y1="19" x2="12" y2="22" />
            <line x1="2" y1="2" x2="22" y2="22" />
          </svg>
        </button>
      );
      
    default:
      console.error('[UnifiedSTT] Unknown provider:', currentProvider);
      return <SpeechToText ref={ref} {...props} />;
  }
});

UnifiedSpeechToText.displayName = 'UnifiedSpeechToText';

export default UnifiedSpeechToText;
