'use client'

import { useState, useEffect, useRef } from 'react'
import EmbedCodeModalContent from './EmbedCodeModalContent'

interface EmbedCodeModalProps {
  chatId: string
  chatName: string
  onClose: () => void
}

export default function EmbedCodeModal({ chatId, chatName, onClose }: EmbedCodeModalProps) {
  // Simple loading state for the modal
  const [isLoading, setIsLoading] = useState(true)

  // Use a ref to track if the component is mounted
  const isMounted = useRef(true)

  // Set loading to false after component mounts
  useEffect(() => {
    // Only update state if component is still mounted
    const timer = setTimeout(() => {
      if (isMounted.current) {
        setIsLoading(false)
      }
    }, 300)

    // Cleanup function
    return () => {
      clearTimeout(timer)
      isMounted.current = false
    }
  }, [])

  return (
    <>
      {isLoading ? (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 p-6 rounded-lg shadow-xl border border-gray-800 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neon-blue"></div>
            <span className="ml-3 text-neon-blue">Loading...</span>
          </div>
        </div>
      ) : (
        <EmbedCodeModalContent
          chatId={chatId}
          chatName={chatName}
          onClose={onClose}
        />
      )}
    </>
  )
}
