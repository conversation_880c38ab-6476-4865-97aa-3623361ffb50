import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fs from 'fs'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function insertTestMessages() {
  try {
    // Get all chat interfaces
    const { data: chatInterfaces, error: chatError } = await supabase
      .from('chat_interfaces')
      .select('id, name')
    
    if (chatError) {
      console.error('Error fetching chat interfaces:', chatError)
      return
    }
    
    if (!chatInterfaces || chatInterfaces.length === 0) {
      console.log('No chat interfaces found. Please create a chat interface first.')
      return
    }
    
    console.log(`Found ${chatInterfaces.length} chat interfaces`)
    
    // For each chat interface, insert some test messages
    for (const chat of chatInterfaces) {
      console.log(`Inserting test messages for chat: ${chat.name} (${chat.id})`)
      
      // Create 3 different sessions with multiple messages each
      const sessionIds = [
        `test-session-1-${Date.now()}`,
        `test-session-2-${Date.now() + 1}`,
        `test-session-3-${Date.now() + 2}`
      ]
      
      // Sample conversations
      const conversations = [
        [
          { sender: 'user', content: 'Hello, how are you?' },
          { sender: 'bot', content: 'I\'m doing well, thank you for asking! How can I help you today?' },
          { sender: 'user', content: 'I need help with my account' },
          { sender: 'bot', content: 'I\'d be happy to help with your account. What specific issue are you having?' }
        ],
        [
          { sender: 'user', content: 'What services do you offer?' },
          { sender: 'bot', content: 'We offer a wide range of services including web development, mobile app development, and cloud solutions.' },
          { sender: 'user', content: 'How much does web development cost?' },
          { sender: 'bot', content: 'Web development costs vary depending on the complexity of the project. Basic websites start at $1,000, while more complex sites can range from $5,000 to $20,000+.' }
        ],
        [
          { sender: 'user', content: 'Do you have any discounts?' },
          { sender: 'bot', content: 'Yes, we currently offer a 10% discount for first-time customers and nonprofit organizations.' },
          { sender: 'user', content: 'Great, I\'m interested in your services' },
          { sender: 'bot', content: 'That\'s wonderful! I\'d be happy to connect you with one of our sales representatives who can provide more detailed information about our services and pricing.' }
        ]
      ]
      
      // Insert messages for each session with timestamps spread over the last week
      for (let i = 0; i < sessionIds.length; i++) {
        const sessionId = sessionIds[i]
        const conversation = conversations[i]
        
        // Create timestamps spread over the last week
        const now = new Date()
        const daysAgo = i * 2 // 0, 2, 4 days ago
        const baseDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
        
        for (let j = 0; j < conversation.length; j++) {
          const message = conversation[j]
          // Add some minutes to each message to simulate a real conversation
          const timestamp = new Date(baseDate.getTime() + (j * 2 * 60 * 1000))
          
          const { error: insertError } = await supabase
            .from('chat_messages')
            .insert({
              chat_interface_id: chat.id,
              session_id: sessionId,
              sender: message.sender,
              content: message.content,
              created_at: timestamp.toISOString()
            })
          
          if (insertError) {
            console.error(`Error inserting message for session ${sessionId}:`, insertError)
          }
        }
        
        console.log(`Inserted ${conversation.length} messages for session ${sessionId}`)
      }
    }
    
    console.log('Test messages inserted successfully')
  } catch (error) {
    console.error('Error inserting test messages:', error)
  }
}

insertTestMessages()
  .then(() => console.log('Done'))
  .catch(console.error)
