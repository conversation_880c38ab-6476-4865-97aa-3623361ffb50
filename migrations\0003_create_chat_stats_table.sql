-- Create chat_stats table
CREATE TABLE IF NOT EXISTS chat_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  message_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set permissions
ALTER TABLE chat_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON chat_stats
  FOR SELECT USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON chat_stats
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- <PERSON>reate trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_chat_stats_updated_at
BEFORE UPDATE ON chat_stats
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();