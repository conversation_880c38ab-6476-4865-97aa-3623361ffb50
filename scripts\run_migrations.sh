#!/bin/bash
# Run Supabase migrations
# Requires supabase CLI to be installed (npm install -g supabase)

echo "Running database migrations..."

# Apply migrations in order
for migration in $(ls migrations/*.sql | sort); do
  echo "Applying migration: $migration"
  supabase migration up --file "$migration"
  if [ $? -ne 0 ]; then
    echo "Error applying migration $migration"
    exit 1
  fi
done

echo "All migrations applied successfully"