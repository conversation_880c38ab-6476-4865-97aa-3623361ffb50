import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createServiceClient } from '@/lib/supabase/server';

// This route needs to be accessible without authentication for embedding
export const dynamic = 'force-dynamic'; // Ensure this is not cached by Next.js

// Common headers for all responses
const commonHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept',
  'Content-Security-Policy': "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; frame-ancestors *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:;"
};

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Await params to fix the error
    const { id } = await Promise.resolve(params);

    // Get embed parameter from URL
    const { searchParams } = new URL(request.url);
    const embedParam = searchParams.get('embed') === 'true';

    // Check for X-Embed-Request header to identify requests from the embed page
    const isEmbedRequest = request.headers.get('X-Embed-Request') === 'true';

    // Check for embed authorization header
    const authHeader = request.headers.get('Authorization');
    const hasEmbedAuth = authHeader === 'Bearer embed';

    // Consider it an embed request if any of these conditions are true
    const isEmbed = isEmbedRequest || embedParam || hasEmbedAuth;

    console.log('Chat API request:', {
      id,
      isEmbedRequest,
      embedParam,
      hasEmbedAuth,
      isEmbed,
      url: request.url,
      method: request.method
    });

    if (!id) {
      console.error('Chat API: Missing chat ID');
      return NextResponse.json({ error: 'Chat ID is required' }, {
        status: 400,
        headers: commonHeaders
      });
    }

    // Check environment variables first
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    console.log('Environment check:', {
      hasSupabaseUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey,
      supabaseUrlLength: supabaseUrl?.length || 0,
      serviceKeyLength: supabaseServiceKey?.length || 0
    });

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables:', {
        NEXT_PUBLIC_SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_ROLE_KEY: !!supabaseServiceKey
      });
      return NextResponse.json({
        error: 'Server configuration error',
        details: 'Missing required environment variables'
      }, {
        status: 500,
        headers: commonHeaders
      });
    }

    // Use service client to bypass RLS policies for public access
    const supabase = createServiceClient();

    // Check if the service client is properly initialized
    if (!supabase) {
      console.error('Service client not initialized properly');
      return NextResponse.json({
        error: 'Database connection error',
        details: 'Failed to initialize service client'
      }, {
        status: 500,
        headers: commonHeaders
      });
    }

    console.log('Attempting to fetch chat interface with ID:', id);

    const { data, error } = await supabase
      .from('chat_interfaces')
      .select('*')
      .eq('id', id)
      .single();

    console.log('Supabase query result:', {
      hasData: !!data,
      error: error?.message || null,
      errorCode: error?.code || null,
      errorDetails: error?.details || null
    });

    if (error) {
      console.error('Error fetching chat interface:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });

      // Return more specific error information
      return NextResponse.json({
        error: 'Database query failed',
        message: error.message,
        code: error.code,
        details: error.details
      }, {
        status: error.code === 'PGRST116' ? 404 : 500,
        headers: commonHeaders
      });
    }

    if (!data) {
      console.log('No chat interface found for ID:', id);
      return NextResponse.json({ error: 'Chat interface not found' }, {
        status: 404,
        headers: commonHeaders
      });
    }

    console.log('Successfully fetched chat interface:', {
      id: data.id,
      name: data.name || 'unnamed'
    });

    // Ensure the use_black_outline property is included in the response
    const enhancedData = {
      ...data,
      use_black_outline: data.use_black_outline !== undefined ? data.use_black_outline : false
    };

    // Return the data with proper CORS headers
    return NextResponse.json(enhancedData, {
      headers: {
        ...commonHeaders,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('Unexpected error fetching chat interface:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      {
        status: 500,
        headers: commonHeaders
      }
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      ...commonHeaders,
      'Access-Control-Max-Age': '86400'
    }
  });
}
