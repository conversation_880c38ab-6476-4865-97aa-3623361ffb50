'use client';

import { useState, useEffect } from 'react';
import TextToSpeech from '@/components/TextToSpeech';

export default function TestStreamingTTSPage() {
  const [messages, setMessages] = useState<Array<{id: string, text: string, sender: 'user' | 'bot'}>>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [microphoneActive, setMicrophoneActive] = useState(false);
  const [autoPlay, setAutoPlay] = useState(true);
  const [lastBotMessage, setLastBotMessage] = useState('');

  // Test bot responses
  const botResponses = [
    "Hello! I'm testing the streaming TTS functionality. This message should play with WebSocket streaming for the fastest possible response time.",
    "This is a longer test message to verify that the streaming TTS works correctly with multiple audio chunks. The system should process this text and deliver audio as it becomes available, providing a much better user experience compared to waiting for the entire text to be processed.",
    "Testing different voice characteristics and ensuring the LINEAR16 encoding with WAV headers works properly in the browser. The audio should be crystal clear and play immediately.",
    "Final test message to confirm that the WebSocket TTS streaming is working perfectly with the fastest response and playback time possible."
  ];

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: `user-${Date.now()}`,
      text: inputText,
      sender: 'user' as const
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Simulate bot response delay
    setTimeout(() => {
      const randomResponse = botResponses[Math.floor(Math.random() * botResponses.length)];
      const botMessage = {
        id: `bot-${Date.now()}`,
        text: randomResponse,
        sender: 'bot' as const
      };

      setMessages(prev => [...prev, botMessage]);
      setLastBotMessage(randomResponse);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h1 className="text-xl font-semibold text-gray-800">
          🚀 Streaming TTS Test Interface
        </h1>
        <p className="text-sm text-gray-600 mt-1">
          Testing WebSocket TTS with LINEAR16 + WAV headers for fastest response
        </p>
        
        {/* Controls */}
        <div className="flex gap-4 mt-3">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={microphoneActive}
              onChange={(e) => setMicrophoneActive(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Microphone Active (Streaming Mode)</span>
          </label>
          
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={autoPlay}
              onChange={(e) => setAutoPlay(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Auto Play</span>
          </label>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-gray-800 border border-gray-200'
              }`}
            >
              {message.text}
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white text-gray-800 border border-gray-200 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-pulse">...</div>
                <span>Bot is typing...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message to test streaming TTS..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputText.trim() || isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Send
          </button>
        </div>
      </div>

      {/* TTS Component */}
      {lastBotMessage && (
        <TextToSpeech
          text={lastBotMessage}
          autoPlay={autoPlay}
          microphoneActive={microphoneActive}
          onStart={() => console.log('TTS started')}
          onEnd={() => console.log('TTS ended')}
        />
      )}
    </div>
  );
}
