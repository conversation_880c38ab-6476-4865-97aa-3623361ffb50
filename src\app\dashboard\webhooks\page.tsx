'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import WebhookForm from '@/components/WebhookForm'
import { toast } from 'sonner'

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState<Array<{
    id: string
    name: string
    url: string
    description?: string
  }>>([])
  const [editingWebhook, setEditingWebhook] = useState<{
    id: string
    name: string
    url: string
    description?: string
  } | null>(null)
  const [loading, setLoading] = useState(true)

  // Load webhooks on component mount
  useEffect(() => {
    const loadWebhooks = async () => {
      try {
        const response = await fetch('/api/webhooks')
        if (response.ok) {
          const data = await response.json()
          setWebhooks(data)
        } else {
          console.error('Failed to load webhooks:', response.statusText)
        }
      } catch (error) {
        console.error('Error loading webhooks:', error)
      } finally {
        setLoading(false)
      }
    }

    loadWebhooks()
  }, [])

  const handleSave = async (data: {
    name: string
    url: string
    description?: string
  }) => {
    try {
      if (editingWebhook) {
        // Update existing webhook
        const response = await fetch('/api/webhooks', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: editingWebhook.id,
            ...data
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to update webhook')
        }

        const updatedWebhook = await response.json()
        setWebhooks(webhooks.map(w => w.id === editingWebhook.id ? updatedWebhook : w))
        toast.success('Webhook updated successfully')
        setEditingWebhook(null)
      } else {
        // Create new webhook
        const response = await fetch('/api/webhooks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create webhook')
        }

        const newWebhook = await response.json()
        setWebhooks([...webhooks, newWebhook])
        toast.success('Webhook created successfully')
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to save webhook')
      console.error(error)
    }
  }

  const handleDelete = async (webhookId: string) => {
    if (!confirm('Are you sure you want to delete this webhook?')) {
      return
    }

    try {
      const response = await fetch('/api/webhooks', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: webhookId })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete webhook')
      }

      setWebhooks(webhooks.filter(w => w.id !== webhookId))
      toast.success('Webhook deleted successfully')

      // If we're editing the webhook being deleted, cancel the edit
      if (editingWebhook?.id === webhookId) {
        setEditingWebhook(null)
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete webhook')
      console.error(error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-neon-blue">Webhooks</h1>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <a href="/dashboard">Back to Dashboard</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/dashboard/new">Create Chat</a>
          </Button>
        </div>
      </div>

      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium text-neon-blue">
            {editingWebhook ? 'Edit Webhook' : 'Create Webhook'}
          </h2>
          {editingWebhook && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setEditingWebhook(null)}
            >
              Cancel Edit
            </Button>
          )}
        </div>
        <WebhookForm
          onSave={handleSave}
          initialData={editingWebhook}
        />
      </Card>

      {loading ? (
        <Card className="p-6">
          <div className="text-center text-gray-500">Loading webhooks...</div>
        </Card>
      ) : webhooks.length > 0 ? (
        <Card className="p-6">
          <h2 className="text-xl font-medium mb-4 text-neon-blue">
            Your Webhooks ({webhooks.length})
          </h2>
          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <Card key={webhook.id} className={`p-4 ${editingWebhook?.id === webhook.id ? 'ring-2 ring-neon-blue' : ''}`}>
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-neon-blue">
                      {webhook.name}
                    </h3>
                    <p className="text-sm text-neon-blue/70">
                      {webhook.url}
                    </p>
                    {webhook.description && (
                      <p className="text-sm mt-1 text-neon-blue/50">
                        {webhook.description}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        setEditingWebhook(webhook)
                      }}
                      className="hover:bg-neon-blue/10 hover:scale-[1.02] transition-transform"
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.promise(
                          fetch(webhook.url, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              message: "Test message from BotFusion webhook test",
                              chatId: "webhook-test",
                              sessionId: `test-${Date.now()}`
                            })
                          }).then(response => {
                            if (!response.ok) {
                              throw new Error(`Test failed with status ${response.status}`)
                            }
                            return response
                          }),
                          {
                            loading: 'Testing webhook...',
                            success: 'Webhook test successful!',
                            error: (error) => error instanceof Error ? error.message : 'Webhook test failed'
                          }
                        )
                      }}
                      className="hover:bg-neon-blue/10 hover:scale-[1.02] transition-transform"
                    >
                      Test
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(webhook.id)}
                      className="hover:bg-red-500/10 hover:text-red-500 hover:border-red-500 hover:scale-[1.02] transition-all"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </Card>
      ) : (
        <Card className="p-6">
          <div className="text-center text-gray-500">
            <p>No webhooks created yet.</p>
            <p className="text-sm mt-2">Create your first webhook above to get started.</p>
          </div>
        </Card>
      )}
    </div>
  )
}