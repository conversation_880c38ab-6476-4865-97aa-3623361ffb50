'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { SpeechToTextRef } from '../../../components/SpeechToText'
import UnifiedSpeechToText from '../../../components/UnifiedSpeechToText'
import SpeechToTextProvider from '../../../contexts/SpeechToTextContext'
import TextToSpeech, { type DeepgramVoice } from '../../../components/TextToSpeech'
import { STTProvider } from '../../../types/deepgram-stt'

interface ChatInterface {
  id: string
  name: string
  description: string
  webhookurl: string
  primary_color?: string
  user_bubble_color?: string
  bot_bubble_color?: string
  user_text_color?: string
  bot_text_color?: string
  logo_url?: string
  dark_mode?: boolean
  show_name?: boolean
  use_black_outline?: boolean
  show_powered_by?: boolean
  powered_by_text?: string
  powered_by_url?: string
  powered_by_text_color?: string
  welcome_message?: string
  use_gradient_header?: boolean
  gradient_start_color?: string
  gradient_end_color?: string
  gradient_direction?: string
  enable_natural_speech?: boolean
  natural_speech_model?: string
  natural_speech_temperature?: number
  natural_speech_max_tokens?: number
  voice_model?: string
}

export default function EmbedChatPage() {
  const params = useParams()
  const id = params?.id as string

  const [chatInterface, setChatInterface] = useState<ChatInterface | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<Array<{
    id: string
    content: string
    sender: 'user' | 'bot'
    timestamp: string
    hidden?: boolean
  }>>([])
  const [sending, setSending] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [messagePort, setMessagePort] = useState<MessagePort | null>(null)
  const [isFloatingMode, setIsFloatingMode] = useState(false)
  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false)
  const [messageWasVoiceInput, setMessageWasVoiceInput] = useState(false) // Track if current message was created via voice
  const [lastBotMessage, setLastBotMessage] = useState('')
  const [pendingBotMessageId, setPendingBotMessageId] = useState<string | null>(null)

  // Use refs to track state changes and prevent infinite loops
  const audioReadyCalledRef = useRef<{[messageId: string]: boolean}>({})
  const speechToTextRef = useRef<SpeechToTextRef>(null)

  // Create a memoized callback for the audio ready event
  const handleAudioReady = useCallback(() => {
    // Check if we've already handled this message to prevent infinite loops
    const messageId = pendingBotMessageId || 'unknown';

    if (audioReadyCalledRef.current[messageId]) {
      console.log('Audio ready callback already called for message:', messageId);
      return;
    }

    // Mark this message as handled
    audioReadyCalledRef.current[messageId] = true;

    // When audio is ready, reveal the hidden message if there is one
    if (pendingBotMessageId) {
      console.log('Audio ready, revealing message:', pendingBotMessageId);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === pendingBotMessageId
            ? { ...msg, hidden: false }
            : msg
        )
      );
      setPendingBotMessageId(null);
    }

    // Keep voice session active until audio actually plays
    // Don't reset voiceSessionActive here - let it stay true for autoplay
    console.log('Audio is ready, keeping voice session active for autoplay');

    // Reset microphone active state after audio is ready
    // This ensures that the next message will start fresh
    console.log('Resetting microphone active state after audio is ready');

    // Use the ref method if available, otherwise fall back to the state setter
    if (speechToTextRef.current) {
      speechToTextRef.current.resetMicrophoneActive();
    } else {
      setIsMicrophoneActive(false);
    }

    // Clean up the ref after a while to prevent memory leaks
    setTimeout(() => {
      if (messageId !== 'unknown' && audioReadyCalledRef.current[messageId]) {
        delete audioReadyCalledRef.current[messageId];
      }
    }, 5000);
  }, [pendingBotMessageId, setMessages, setIsMicrophoneActive])

  // Setup message channel for communication with parent window
  useEffect(() => {
    // Listen for the init message from the parent window
    const handleMessage = (event: MessageEvent) => {
      console.log('Received message from parent window:', event.data);

      // Check for initialization message with MessageChannel
      if (event.data === 'botfusion-chat-init' && event.ports && event.ports.length > 0) {
        console.log('Received init message from parent window with port');

        // Store the message port for later use
        const port = event.ports[0];
        setMessagePort(port);

        // Set up port message handler
        port.onmessage = (portEvent) => {
          console.log('Received message through port:', portEvent.data);

          // Handle specific messages
          if (portEvent.data === 'botfusion-chat-open') {
            setIsOpen(true);
          } else if (portEvent.data === 'botfusion-chat-close') {
            setIsOpen(false);
          }
        };

        // Acknowledge receipt of the port
        try {
          port.postMessage('botfusion-chat-init-ack');
        } catch (error) {
          console.error('Error sending acknowledgment through port:', error);
        }
      }
      // Handle direct messages (fallback)
      else if (event.data === 'botfusion-chat-open') {
        console.log('Received open message from parent window');
        setIsOpen(true);
      } else if (event.data === 'botfusion-chat-close') {
        console.log('Received close message from parent window');

        // IMMEDIATE GLOBAL AUDIO STOP and cleanup when chat is closed
        if (typeof window !== 'undefined' && window.globalAudioManager) {
          console.log('Chat closing: Stopping all audio and clearing buffers');
          window.globalAudioManager.stopAllAudio();
        }

        // Clear any TTS state and audio buffers
        setLastBotMessage('');
        setMessageWasVoiceInput(false);
        setIsMicrophoneActive(false);

        setIsOpen(false);
      } else if (event.data === 'botfusion-chat-toggle') {
        console.log('Received toggle message from parent window');

        if (isOpen) {
          // Close the chat
          console.log('Toggling chat closed');

          // IMMEDIATE GLOBAL AUDIO STOP and cleanup when chat is closed
          if (typeof window !== 'undefined' && window.globalAudioManager) {
            console.log('Chat closing via toggle: Stopping all audio and clearing buffers');
            window.globalAudioManager.stopAllAudio();
          }

          // Clear any TTS state and audio buffers
          setLastBotMessage('');
          setMessageWasVoiceInput(false);
          setIsMicrophoneActive(false);

          setIsOpen(false);
        } else {
          // Open the chat
          console.log('Toggling chat open');
          setIsOpen(true);
        }
      }
    };

    // Add the message event listener
    window.addEventListener('message', handleMessage);

    // Send ready message to parent window
    try {
      if (window.parent && window.parent !== window) {
        console.log('Sending ready message to parent window');
        window.parent.postMessage('botfusion-chat-ready', '*');
      }
    } catch (error) {
      console.error('Error sending ready message to parent window:', error);
    }

    return () => {
      window.removeEventListener('message', handleMessage);

      // Clean up message port
      if (messagePort) {
        messagePort.onmessage = null;
        // No need to close the port as it will be garbage collected
      }
    };
  }, [messagePort, isOpen]);

  useEffect(() => {
    const fetchChatInterface = async () => {
      try {
        console.log('Embed: Fetching chat interface for ID:', id);

        // Get the bypass secret from URL params or use default
        const urlParams = new URLSearchParams(window.location.search);
        const bypassSecret = urlParams.get('x-vercel-protection-bypass') || 'Q3W0KiXNSiZt6HCFH0e1zYfT2MwpCR2F';

        // Try the main API first, then fallback to public API
        let response;
        let apiUrl = `/api/chat/${id}?t=${new Date().getTime()}&embed=true&x-vercel-protection-bypass=${bypassSecret}`;
        console.log('Embed: Primary API URL:', apiUrl);

        try {
          response = await fetch(apiUrl, {
            credentials: 'omit', // Don't send credentials
            cache: 'no-store', // Don't use cache
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'X-Embed-Request': 'true', // Add this header to identify embed requests
              'Authorization': 'Bearer embed', // Add a special authorization header for embed requests
              'x-vercel-protection-bypass': bypassSecret // Add bypass secret header
            }
          });

          console.log('Embed: Primary response status:', response.status);

          // If we get a 401, try the public API as fallback
          if (response.status === 401) {
            console.log('Primary API returned 401, trying public API fallback');
            apiUrl = `/api/public-chat/${id}?t=${new Date().getTime()}&embed=true&x-vercel-protection-bypass=${bypassSecret}`;
            console.log('Embed: Fallback API URL:', apiUrl);

            response = await fetch(apiUrl, {
              credentials: 'omit',
              cache: 'no-store',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Embed-Request': 'true',
                'Authorization': 'Bearer embed',
                'x-vercel-protection-bypass': bypassSecret
              }
            });

            console.log('Embed: Fallback response status:', response.status);
          }
        } catch (fetchError) {
          console.error('Fetch error:', fetchError);
          throw fetchError;
        }

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Failed to load chat interface:', response.status, response.statusText, errorText);

          // Don't retry on 401/403/404 errors - these are likely permanent
          if (response.status === 401 || response.status === 403 || response.status === 404) {
            setError(`Chat interface not available (${response.status})`);
            setLoading(false);
            return;
          }

          throw new Error(`Failed to load chat interface: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()

        // Check for URL parameters that might override the chat interface settings
        if (typeof window !== 'undefined') {
          const urlParams = new URLSearchParams(window.location.search);

          // Apply URL parameters if they exist
          if (urlParams.has('primaryColor')) {
            data.primary_color = decodeURIComponent(urlParams.get('primaryColor') || '');
          }

          if (urlParams.has('userBubbleColor')) {
            data.user_bubble_color = decodeURIComponent(urlParams.get('userBubbleColor') || '');
          }

          if (urlParams.has('botBubbleColor')) {
            data.bot_bubble_color = decodeURIComponent(urlParams.get('botBubbleColor') || '');
          }

          if (urlParams.has('userTextColor')) {
            data.user_text_color = decodeURIComponent(urlParams.get('userTextColor') || '');
          }

          if (urlParams.has('botTextColor')) {
            data.bot_text_color = decodeURIComponent(urlParams.get('botTextColor') || '');
          }

          if (urlParams.has('logoUrl')) {
            data.logo_url = decodeURIComponent(urlParams.get('logoUrl') || '');
          }

          if (urlParams.has('darkMode')) {
            data.dark_mode = urlParams.get('darkMode') === 'true';
          }

          if (urlParams.has('showName')) {
            data.show_name = urlParams.get('showName') === 'true';
          }

          if (urlParams.has('showPoweredBy')) {
            data.show_powered_by = urlParams.get('showPoweredBy') === 'true';
          }

          if (urlParams.has('poweredByText')) {
            data.powered_by_text = decodeURIComponent(urlParams.get('poweredByText') || '');
          }

          if (urlParams.has('poweredByUrl')) {
            data.powered_by_url = decodeURIComponent(urlParams.get('poweredByUrl') || '');
          }

          if (urlParams.has('poweredByTextColor')) {
            data.powered_by_text_color = decodeURIComponent(urlParams.get('poweredByTextColor') || '');
          }

          if (urlParams.has('useBlackOutline')) {
            data.use_black_outline = urlParams.get('useBlackOutline') === 'true';
            console.log('Setting useBlackOutline from URL param:', data.use_black_outline);
          }

          // Add gradient header parameters
          if (urlParams.has('useGradientHeader')) {
            data.use_gradient_header = urlParams.get('useGradientHeader') === 'true';
            console.log('Setting useGradientHeader from URL param:', data.use_gradient_header);
          }

          if (urlParams.has('gradientStartColor')) {
            data.gradient_start_color = decodeURIComponent(urlParams.get('gradientStartColor') || '');
          }

          if (urlParams.has('gradientEndColor')) {
            data.gradient_end_color = decodeURIComponent(urlParams.get('gradientEndColor') || '');
          }

          if (urlParams.has('gradientDirection')) {
            data.gradient_direction = decodeURIComponent(urlParams.get('gradientDirection') || '');
          }

          // Check if we're in floating mode
          if (urlParams.has('floating')) {
            setIsFloatingMode(urlParams.get('floating') === 'true');
          }
        }

        setChatInterface(data)

        // If there's a welcome message, add it to the messages
        if (data.welcome_message) {
          setMessages([{
            id: 'welcome-message',
            content: data.welcome_message,
            sender: 'bot',
            timestamp: new Date().toISOString()
          }])
        }
      } catch (error) {
        console.error('Error fetching chat interface:', error)
        toast.error('Failed to load chat interface')
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchChatInterface()
    }
  }, [id])

  // Function to aggressively stop all audio elements in the DOM
  const stopAllAudioElements = useCallback(() => {
    console.log('Embed: Stopping all audio elements in DOM');

    // Stop all audio elements in the audio container
    const container = document.getElementById('audio-container');
    if (container) {
      const audioElements = container.querySelectorAll('audio');
      audioElements.forEach((audio) => {
        try {
          if (!audio.paused) {
            audio.pause();
            audio.currentTime = 0;
            console.log('Embed: Stopped existing audio element in container');
          }
          // Clear source to stop any ongoing requests
          audio.removeAttribute('src');
          audio.load();
        } catch (e) {
          console.log('Embed: Error stopping audio element in container:', e);
        }
      });
    }

    // Also stop any audio elements that might be elsewhere in the DOM
    const allAudioElements = document.querySelectorAll('audio');
    allAudioElements.forEach((audio) => {
      try {
        if (!audio.paused) {
          audio.pause();
          audio.currentTime = 0;
          console.log('Embed: Stopped audio element found in DOM');
        }
      } catch (e) {
        console.log('Embed: Error stopping DOM audio element:', e);
      }
    });
  }, []);

  // Handle microphone state changes
  const handleMicrophoneActiveChange = useCallback((active: boolean) => {
    console.log('Microphone active state changed:', active);
    setIsMicrophoneActive(active);

    // If microphone becomes active, mark this message as voice input
    if (active) {
      setMessageWasVoiceInput(true);
      console.log('Voice input detected, will enable TTS for next bot response');

      // IMMEDIATE GLOBAL AUDIO STOP using Global Audio Manager
      if (typeof window !== 'undefined' && window.globalAudioManager) {
        window.globalAudioManager.stopAllAudio();
      }

      // AGGRESSIVE CLEANUP: Stop all existing audio elements before new voice session
      stopAllAudioElements();

      // Clear the last bot message to prevent replaying old audio
      console.log('Clearing last bot message to prevent audio replay');
      setLastBotMessage('');
    }
  }, [stopAllAudioElements]);

  // Track the last message sent to prevent duplicates
  const lastSentMessageRef = useRef<string>('');
  const lastSentTimeRef = useRef<number>(0);

  // Ref for auto-scrolling to bottom
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const sendMessage = async (voiceTranscript?: string) => {
    const messageToSend = voiceTranscript || message;
    console.log('[DEBUG] Embed sendMessage called, message state:', `"${message}"`, 'voiceTranscript:', `"${voiceTranscript || 'none'}"`, 'using:', `"${messageToSend}"`);
    if (!messageToSend.trim() || !chatInterface) {
      console.log('[DEBUG] Embed sendMessage early return - message empty or no chatInterface');
      return;
    }

    // Check for duplicate messages sent within a short time window (1.5 seconds)
    const currentTime = Date.now();
    const timeSinceLastMessage = currentTime - lastSentTimeRef.current;

    if (messageToSend === lastSentMessageRef.current && timeSinceLastMessage < 1500) {
      console.log('Preventing duplicate message send:', messageToSend);
      console.log(`Time since last message: ${timeSinceLastMessage}ms`);
      return;
    }

    // Update last sent message tracking
    lastSentMessageRef.current = messageToSend;
    lastSentTimeRef.current = currentTime;

    // Add user message to chat
    const userMessage = {
      id: currentTime.toString(),
      content: messageToSend,
      sender: 'user' as const,
      timestamp: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    // Only clear message state if this wasn't a voice transcript
    if (!voiceTranscript) {
      setMessage('')
    }
    setSending(true)

    // Add typing indicator
    const typingIndicator = {
      id: 'typing-indicator',
      content: '•••',
      sender: 'bot' as const,
      timestamp: new Date().toISOString()
    }
    setMessages(prev => [...prev, typingIndicator])

    try {
      console.log('Sending message to webhook via proxy:', chatInterface.webhookurl);

      // Prepare the request body for our proxy
      const requestBody = {
        webhookUrl: chatInterface.webhookurl,
        message: userMessage.content,
        chatId: id,
        sessionId: localStorage.getItem('chatSessionId') || Date.now().toString()
      };

      console.log('Request body:', requestBody);

      // Send message to our proxy API instead of directly to the webhook
      const response = await fetch('/api/webhook-proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Webhook response status:', response.status);

      if (!response.ok) {
        console.error('Webhook response not OK:', response.status, response.statusText);
        throw new Error(`Failed to send message: ${response.status} ${response.statusText}`)
      }

      // Get response from webhook
      const responseText = await response.text();
      console.log('Webhook response text:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log('Parsed webhook response:', data);
      } catch (e) {
        console.error('Failed to parse webhook response as JSON:', e);
        data = { response: responseText };
      }

      // Create bot message with unique ID
      const botMessageId = (Date.now() + 1).toString();
      const botMessageContent = data.response || data.output || data.text || data.message || responseText || 'Sorry, I couldn\'t process your request.';

      // Create the bot message object
      const botMessage = {
        id: botMessageId,
        content: botMessageContent,
        sender: 'bot' as const,
        timestamp: new Date().toISOString(),
        // Always show the message immediately - we'll handle TTS separately
        hidden: false
      }

      console.log('Created bot message:', botMessage);
      console.log('Microphone active:', isMicrophoneActive);

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing-indicator'))

      // Store the message ID so we can unhide it when audio is ready
      // Use messageWasVoiceInput to determine if TTS should play
      if (messageWasVoiceInput) {
        setPendingBotMessageId(botMessageId);
      }

      // Add a small delay to make the typing animation more realistic
      setTimeout(() => {
        setMessages(prev => [...prev, botMessage])
        // Save the bot message for TTS
        setLastBotMessage(botMessageContent)
      }, 500)

      // Store session ID for future messages
      if (!localStorage.getItem('chatSessionId')) {
        localStorage.setItem('chatSessionId', Date.now().toString())
      }

    } catch (error) {
      console.error('Error sending message:', error)

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing-indicator'))

      // Add error message from bot
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, there was an error processing your message. Please try again later.',
        sender: 'bot' as const,
        timestamp: new Date().toISOString()
      }

      setTimeout(() => {
        setMessages(prev => [...prev, errorMessage])
      }, 500)
    } finally {
      setSending(false)
    }
  }

  if (loading) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Card className="p-4 max-w-[300px] w-full text-center">
          <p className="text-sm text-red-500">{error}</p>
        </Card>
      </div>
    )
  }

  if (!chatInterface) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Card className="p-4 max-w-[300px] w-full text-center">
          <p className="text-sm text-red-500">Chat not available</p>
        </Card>
      </div>
    )
  }

  // Get custom colors and settings from chat interface
  const primaryColor = chatInterface.primary_color || '#3b82f6';
  const userBubbleColor = chatInterface.user_bubble_color || '#ffffff';
  const botBubbleColor = chatInterface.bot_bubble_color || '#3b82f6';
  const userTextColor = chatInterface.user_text_color || '#000000';
  const botTextColor = chatInterface.bot_text_color || '#ffffff';
  const logoUrl = chatInterface.logo_url || '';
  const isDarkMode = chatInterface.dark_mode || false;
  const showName = chatInterface.show_name !== false; // Default to true if not specified
  const showPoweredBy = chatInterface.show_powered_by !== undefined ? chatInterface.show_powered_by : true;
  const poweredByText = chatInterface.powered_by_text || 'Powered by BotFusion';
  const poweredByUrl = chatInterface.powered_by_url || 'https://botfusion.io';
  const poweredByTextColor = chatInterface.powered_by_text_color || primaryColor;
  const useBlackOutline = chatInterface.use_black_outline || false;
  const useGradientHeader = chatInterface.use_gradient_header || false;
  const gradientStartColor = chatInterface.gradient_start_color || '#3b82f6';
  const gradientEndColor = chatInterface.gradient_end_color || '#9333ea';
  const gradientDirection = chatInterface.gradient_direction || 'to bottom';

  // Determine background colors based on dark mode
  const bgColor = isDarkMode ? 'bg-gray-900' : 'bg-white';
  const chatAreaBg = isDarkMode ? 'bg-gray-800' : 'bg-gray-50';
  const inputBgColor = isDarkMode ? 'bg-gray-800' : 'bg-white';
  const inputBorderColor = isDarkMode ? 'border-gray-700' : 'border-gray-300';

  // If we're in floating mode, render the button (always visible for toggle functionality)
  if (isFloatingMode) {
    return (
      <Button
        onClick={() => {
          // When in floating mode, clicking the button should toggle the chat
          try {
            if (window.parent) {
              console.log('Sending toggle message to parent window');
              // Send simple toggle message (no MessageChannel needed for CSP script)
              window.parent.postMessage('botfusion-chat-toggle', '*');
            }
          } catch (error) {
            console.error('Error sending toggle message to parent window:', error);
            // Fallback to just toggling locally
            setIsOpen(!isOpen);
          }
        }}
        className="w-full h-full rounded-full shadow-lg flex items-center justify-center"
        style={{ backgroundColor: primaryColor, color: 'white' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      </Button>
    );
  }

  return (
    <SpeechToTextProvider provider={STTProvider.AUTO}>
      {/* Chat button - only show if not in floating mode */}
      {!isOpen && !isFloatingMode && (
        <Button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-4 right-4 rounded-full w-14 h-14 shadow-lg flex items-center justify-center"
          style={{ backgroundColor: primaryColor, color: 'white' }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
        </Button>
      )}

      {/* Chat window */}
      {isOpen && (
        <div
          className={`chat-interface fixed bottom-4 right-4 z-50 w-80 sm:w-96 rounded-lg overflow-hidden flex flex-col ${bgColor} ${useBlackOutline ? 'border-2 border-black' : `border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`} h-[500px] max-h-[80vh]`}
          style={{
            borderWidth: useBlackOutline ? '2px' : '1px',
            borderColor: useBlackOutline ? 'black' : (isDarkMode ? '#374151' : '#e5e7eb'),
            borderStyle: 'solid',
            boxShadow: 'none'
          }}
        >
          {/* Chat header */}
          <div
            style={useGradientHeader
              ? { background: `linear-gradient(${gradientDirection}, ${gradientStartColor}, ${gradientEndColor})` }
              : { backgroundColor: primaryColor }
            }
            className="p-3 flex items-center">
            <div className="flex items-center gap-2">
              {logoUrl && (
                <div className="relative h-10 w-10">
                  <Image
                    src={logoUrl}
                    alt={`${chatInterface.name} Logo`}
                    fill
                    sizes="40px"
                    className="object-contain"
                    unoptimized={logoUrl.startsWith('data:') || logoUrl.includes('blob:')}
                  />
                </div>
              )}
              {showName && (
                <h3 className="font-medium text-white">{chatInterface.name}</h3>
              )}
            </div>
          </div>

          {/* Chat messages */}
          <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${chatAreaBg}`}>
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <p className={isDarkMode ? "text-gray-400 text-sm" : "text-gray-500 text-sm"}>Send a message to start chatting</p>
              </div>
            ) : (
              messages.map(msg => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      msg.sender === 'user'
                        ? 'border border-gray-200'
                        : ''
                    }`}
                    style={{
                      backgroundColor: msg.sender === 'user' ? userBubbleColor : botBubbleColor,
                      color: msg.sender === 'user' ? userTextColor : botTextColor
                    }}
                  >
                    {msg.id === 'typing-indicator' ? (
                      <div className="flex space-x-1 items-center">
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                    ) : msg.hidden ? (
                      <div className="flex space-x-1 items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                    ) : (
                      <>
                        <p className="text-base whitespace-pre-wrap font-normal leading-relaxed" style={{ color: msg.sender === 'user' ? userTextColor : botTextColor }}>{msg.content}</p>
                        <span
                          className="text-xs mt-1 block"
                          style={{
                            color: msg.sender === 'user'
                              ? (userTextColor === '#000000' ? '#666666' : userTextColor)
                              : (botTextColor === '#ffffff' ? 'rgba(255, 255, 255, 0.8)' : botTextColor)
                          }}
                        >
                          {new Date(msg.timestamp).toLocaleTimeString()}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              ))
            )}
            {/* Invisible element to scroll to */}
            <div ref={messagesEndRef} />
          </div>

          {/* Message input */}
          <div className={`p-3 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${inputBgColor}`}>
            <form
              onSubmit={(e) => {
                e.preventDefault()
                // If user submits via form (typing), this is NOT a voice input
                // But don't reset if this was triggered by voice auto-send
                if (!isMicrophoneActive && !messageWasVoiceInput) {
                  setMessageWasVoiceInput(false);
                  console.log('Form submitted via typing, TTS disabled for bot response');
                } else if (messageWasVoiceInput) {
                  console.log('Form submitted via voice auto-send, TTS enabled for bot response');
                }
                sendMessage()
              }}
              className="flex gap-2"
            >
              <div className="flex-1 relative">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className={`w-full ${inputBgColor} ${inputBorderColor} focus:border-opacity-100`}
                  style={{
                    borderColor: primaryColor,
                    "--tw-ring-color": primaryColor,
                    color: isDarkMode ? "white" : "black"
                  } as React.CSSProperties}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      // If user presses Enter (typing), this is NOT a voice input
                      // But don't reset if this was set by voice input
                      if (!isMicrophoneActive && !messageWasVoiceInput) {
                        setMessageWasVoiceInput(false);
                        console.log('Enter key pressed (typing), TTS disabled for bot response');
                      } else if (messageWasVoiceInput) {
                        console.log('Enter key pressed after voice input, TTS enabled for bot response');
                      }
                      sendMessage()
                    }
                  }}
                />
              </div>
              <UnifiedSpeechToText
                ref={speechToTextRef}
                onTranscript={(text: string) => {
                  // Only update text input if auto-send is disabled
                  // For auto-send, transcript goes directly to chat without showing in input
                  console.log('[DEBUG] Voice transcript received:', text, 'Auto-send enabled, skipping text input update');
                }}
                disabled={sending}
                color={primaryColor}
                onMicrophoneActiveChange={handleMicrophoneActiveChange}
                autoSend={true}
                onSend={sendMessage}
              />
              <Button
                type="submit"
                disabled={sending || !message.trim()}
                size="sm"
                className="text-white"
                style={{ backgroundColor: primaryColor }}
              >
                {sending ? (
                  <span className="animate-pulse">...</span>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                  </svg>
                )}
              </Button>
            </form>

            {/* Text-to-Speech component */}
            <TextToSpeech
              text={lastBotMessage}
              autoPlay={messageWasVoiceInput}
              voice={(chatInterface.voice_model || 'thalia') as DeepgramVoice} // Use interface-specific voice
              microphoneActive={isMicrophoneActive}
              onAudioReady={handleAudioReady}
              messageId={pendingBotMessageId || undefined} // Pass the bot message ID
              enableNaturalSpeech={chatInterface.enable_natural_speech !== false} // Default to true if not specified
              onStart={() => {
                console.log('TTS audio started playing, resetting voice input flag');
                setMessageWasVoiceInput(false);
              }}
            />

            {/* Powered By section */}
            {showPoweredBy && (
              <div className="mt-3 pt-2 text-center">
                <a
                  href={poweredByUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-xs hover:underline ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}
                  style={{ color: poweredByTextColor }}
                >
                  {poweredByText}
                </a>
              </div>
            )}
          </div>
        </div>
      )}
    </SpeechToTextProvider>
  )
}

