{"version": 2, "framework": "nextjs", "public": true, "functions": {"src/app/api/*/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}]}, {"source": "/embed/(.*)", "headers": [{"key": "X-Frame-Options", "value": ""}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}]}, {"source": "/api/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS, HEAD"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, Cache-Control, Pragma, Expires, Range"}, {"key": "Access-Control-Expose-Headers", "value": "Content-Length, Content-Range, Content-Type"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Timing-Allow-Origin", "value": "*"}]}, {"source": "/api/embed-script", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}]}, {"source": "/api/csp-embed-script", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}]}, {"source": "/api/direct-embed", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}]}, {"source": "/api/(deepgram-tts|echo-tts|tts-direct)(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS, HEAD"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, Range, X-Requested-With, Cache-Control, Pragma, Expires, Accept, Origin, Referer"}, {"key": "Access-Control-Expose-Headers", "value": "Content-Length, Content-Range, Content-Type, Content-Disposition"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "Timing-Allow-Origin", "value": "*"}]}, {"source": "/api/chat/(.*)", "headers": [{"key": "X-Frame-Options", "value": ""}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS, HEAD"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/chat/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/widget/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "frame-ancestors *; default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: blob: data:; frame-src *; font-src 'self' data:; media-src * 'self' blob: https: data: mediastream:; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; child-src 'self' blob: data: mediastream:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With, Accept, Origin, Referer"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}