
'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { PricingButton } from '@/components/PricingButton'

// Animation variants for smoother transitions
const fadeIn = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: [0.22, 1, 0.36, 1] // Custom cubic-bezier for smooth easing
    }
  }
}

const slideUp = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: [0.22, 1, 0.36, 1]
    }
  }
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
}

export default function LandingPage() {
  return (
    <main className="min-h-screen relative overflow-hidden font-sans tech-grid-bg text-glow-blue">
      {/* Glowing background elements with smoother animations */}
      <div className="absolute top-[-150px] left-[-150px] w-[500px] h-[500px] bg-gradient-to-tr from-blue-900 to-indigo-800 rounded-full filter blur-3xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute top-[80px] right-[-120px] w-[400px] h-[400px] bg-gradient-to-tr from-indigo-800 to-blue-900 rounded-full filter blur-2xl opacity-30 animate-blob animation-delay-4000"></div>
      <div className="absolute bottom-[-120px] left-[50%] w-[600px] h-[600px] bg-gradient-to-tr from-blue-900 to-indigo-800 rounded-full filter blur-3xl opacity-25 animate-blob animation-delay-6000"></div>

      {/* Tech grid lines */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(0,255,255,0.1)_0%,transparent_70%)]"></div>

      {/* Header */}
      <header className="absolute top-0 left-0 right-0 p-6 flex justify-end z-20">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
          className="flex space-x-4"
        >
          <Button
            asChild
            variant="outline"
            className="glass-card border-neon-blue hover:shadow-glow-blue"
          >
            <Link href="/login">Login</Link>
          </Button>
          <Button
            asChild
            className="glass-card bg-neon-blue/20 border border-neon-blue hover:bg-neon-blue/30 hover:shadow-glow-blue text-neon-blue signup-button"
            style={{ color: '#3b82f6' }} /* Inline style to ensure blue text */
          >
            <Link href="/login?signup=true" style={{ color: '#3b82f6' }}>Sign Up</Link>
          </Button>
        </motion.div>
      </header>

      {/* Hero Section with improved animations */}
      <section className="flex flex-col items-center justify-center min-h-[60vh] px-6 text-center relative z-10">
        <motion.img
          src="/logo.png"
          alt="BotFusion X Logo"
          className="w-48 mb-6"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 0.8,
            ease: [0.22, 1, 0.36, 1]
          }}
        />
        <motion.h1
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            ease: [0.22, 1, 0.36, 1]
          }}
          className="text-6xl font-extrabold max-w-4xl mb-6 tracking-wider text-neon-blue text-glow-blue"
        >
          BotFusion X
        </motion.h1>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            delay: 0.2,
            duration: 0.8,
            ease: [0.22, 1, 0.36, 1]
          }}
          className="text-2xl max-w-3xl mb-10 text-neon-blue"
        >
          Generate and customize chat interfaces connected to n8n workflows via webhooks.
        </motion.p>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.4,
            duration: 0.8,
            ease: [0.22, 1, 0.36, 1]
          }}
        >
          <a href="/login?signup=true" className="inline-block">
            <Button className="px-12 py-4 text-lg font-semibold glass-card border border-neon-blue hover:border-neon-blue hover:shadow-glow-blue button-glow-hover shadow-glow-blue">
              Connect Your Chat Flow
            </Button>
          </a>
        </motion.div>
      </section>

      <motion.section
        className="py-10 px-6 relative z-10 max-w-4xl mx-auto"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={fadeIn}
      >
        <motion.p className="max-w-3xl mx-auto text-center text-indigo-300 text-lg text-glow-blue leading-relaxed">
          BotFusion X empowers businesses to create and customize chat interfaces seamlessly integrated with n8n workflows via webhooks. Our platform offers flexibility, ease of use, and powerful customization options to enhance customer engagement and automate workflows efficiently.
        </motion.p>
      </motion.section>

      {/* How It Works Section with staggered animations */}
      <motion.section
        className="py-20 px-6 glass-card max-w-5xl mx-auto ambient-glow"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <motion.h2
          variants={slideUp}
          className="text-3xl font-bold text-center mb-12 text-neon-blue text-glow-blue"
        >
          How It Works
        </motion.h2>
        <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
          <motion.div
            variants={slideUp}
            className="glass-card p-6 ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue"
          >
            <h3 className="text-xl font-semibold mb-4 text-neon-blue">Connect n8n</h3>
            <p className="text-neon-blue">Link your chat interface to n8n workflows via webhook URLs.</p>
          </motion.div>
          <motion.div
            variants={slideUp}
            className="glass-card p-6 ambient-glow pulse-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue"
          >
            <h3 className="text-xl font-semibold mb-4 text-neon-blue">Voice Agent</h3>
            <p className="text-neon-blue">Advanced speech-to-text and text-to-speech with multiple voice options and natural conversation processing.</p>
          </motion.div>
          <motion.div
            variants={slideUp}
            className="glass-card p-6 ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue"
          >
            <h3 className="text-xl font-semibold mb-4 text-neon-blue">Deploy</h3>
            <p className="text-neon-blue">Get your unique URL and embed code to launch instantly.</p>
          </motion.div>
        </div>
      </motion.section>

      {/* Pricing Plans Section with staggered animations */}
      <motion.section
        className="py-20 px-6 glass-card max-w-5xl mx-auto ambient-glow"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <motion.h2
          variants={slideUp}
          className="text-3xl font-bold text-center mb-12 text-neon-blue text-glow-blue"
        >
          Pricing Plans
        </motion.h2>
        <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Free Plan */}
          <motion.div
            variants={slideUp}
            className="glass-card p-8 text-center ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue pricing-card"
          >
            <div className="pricing-content">
              <h3 className="text-xl font-semibold mb-4">Free</h3>
              <p className="mb-6">1 chat, 100 messages/month, "Powered by BotFusion X" badge</p>
              <p className="font-bold text-2xl mb-6">$0</p>
            </div>
            <div className="pricing-button">
              <PricingButton text="Current Plan" disabled={true} />
            </div>
          </motion.div>

          {/* Pro Plan */}
          <motion.div
            variants={slideUp}
            className="glass-card p-8 text-center ambient-glow pulse-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue pricing-card"
          >
            <div className="pricing-content">
              <h3 className="text-xl font-semibold mb-4">Pro</h3>
              <p className="mb-6">Up to 10 chats, 10K messages/month, branding removal</p>
              <p className="font-bold text-2xl mb-6">$19/mo</p>
            </div>
            <div className="pricing-button">
              <PricingButton text="Upgrade" href="/login?signup=true" />
            </div>
          </motion.div>

          {/* Enterprise Plan */}
          <motion.div
            variants={slideUp}
            className="glass-card p-8 text-center ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue pricing-card"
          >
            <div className="pricing-content">
              <h3 className="text-xl font-semibold mb-4">Enterprise</h3>
              <p className="mb-6">Unlimited chats, white-label, API access, priority support</p>
              <p className="font-bold text-2xl mb-6">Custom Pricing</p>
            </div>
            <div className="pricing-button">
              <PricingButton text="Contact Us" href="/contact" />
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Testimonials Section */}
      <section className="py-20 px-6 glass-card max-w-5xl mx-auto ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue">
        <motion.h2
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1.2 }} // Increased from default
          className="text-3xl font-bold text-center mb-12 text-neon-blue text-glow-blue"
        >
          Testimonials
        </motion.h2>
        <div className="max-w-4xl mx-auto space-y-8">
          <motion.blockquote
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 1.5, ease: [0.22, 1, 0.36, 1] }} // Slower, smoother animation
            className="border-l-4 border-neon-blue pl-6 italic text-neon-blue p-4 glass-card ambient-glow"
          >
            "BotFusion X transformed our customer support with seamless chat workflows."
          </motion.blockquote>
          <motion.blockquote
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 1.5, ease: [0.22, 1, 0.36, 1] }} // Slower, smoother animation
            className="border-l-4 border-neon-blue pl-6 italic text-neon-blue p-4 glass-card ambient-glow"
          >
            "The customization options and ease of integration saved us hours of development."
          </motion.blockquote>
        </div>
      </section>

      {/* Footer with fade-in animation */}
      <motion.footer
        className="py-12 px-6 glass-card text-center text-neon-blue ambient-glow hover:shadow-glow-blue transition-all duration-300 shadow-glow-blue"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
      >
        <p>© 2025 BotFusion X. All rights reserved.</p>
        <div className="mt-4 space-x-4">
          <a href="mailto:<EMAIL>" className="hover:underline">
            Contact
          </a>
          <a href="https://twitter.com/botfusionx" target="_blank" rel="noopener noreferrer" className="hover:underline">
            Twitter
          </a>
          <a href="https://github.com/botfusionx" target="_blank" rel="noopener noreferrer" className="hover:underline">
            GitHub
          </a>
        </div>
      </motion.footer>
    </main>
  )
}
