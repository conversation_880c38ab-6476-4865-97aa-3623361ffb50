<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Strict CSP Embed</title>
    <!-- This is the strictest possible CSP - no scripts, no inline styles -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'none'; style-src 'none'; frame-src https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app">
    <style>
        /* This style will be blocked by the CSP */
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Test Strict CSP Embed</h1>

    <div class="note">
        <strong>Important:</strong> This page has the strictest possible Content Security Policy:
        <pre>script-src 'none'; style-src 'none';</pre>
        This blocks all scripts and inline styles. Only the pure HTML embed should work.
    </div>

    <!-- Pure HTML Embed - This should work with any CSP -->
    <iframe
      src="https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app/api/pure-html-embed?chatId=b76a3980-9f8e-47cd-ae7d-f02747552c4d"
      style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border: none; z-index: 9999; border-radius: 50%;"
      frameborder="0"
      title="Chat Widget"
      loading="lazy"
    ></iframe>
</body>
</html>
