'use client'

import { useState, useEffect, useMemo } from 'react'
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { motion } from 'framer-motion'

interface ChatActivityChartProps {
  totalMessages: number
  uniqueSessions: number
  lastActivity: string | null
  chatId: string
}

export default function ChatActivityChart({
  totalMessages,
  uniqueSessions,
  lastActivity,
  chatId
}: ChatActivityChartProps) {
  const [activityData, setActivityData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeChart, setActiveChart] = useState<'area' | 'bar' | 'pie'>('area')

  // Generate better data representation based on the real stats
  useEffect(() => {
    console.log('ChatActivityChart: totalMessages =', totalMessages, 'uniqueSessions =', uniqueSessions, 'lastActivity =', lastActivity)
    setIsLoading(true)

    // Create a more accurate data representation
    const generateActivityData = () => {
      // If we have no messages, show a simple empty state
      if (!totalMessages || totalMessages === 0) {
        console.log('ChatActivityChart: No messages, generating empty data')
        return Array(7).fill(0).map((_, i) => {
          const date = new Date()
          date.setDate(date.getDate() - (6 - i))
          return {
            date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            messages: 0,
            sessions: 0
          }
        })
      }

      console.log('ChatActivityChart: Generating data for', totalMessages, 'messages and', uniqueSessions, 'sessions')

      // Create 7 days of data with a realistic distribution
      const data = []
      const now = new Date()

      // Create a more realistic distribution - more recent days have more activity
      // Total should add up to actual total messages
      const dayDistribution = [0.05, 0.1, 0.1, 0.15, 0.15, 0.2, 0.25] // Sums to 1.0
      let remainingMessages = totalMessages
      let remainingSessions = uniqueSessions

      for (let i = 0; i < 7; i++) {
        const date = new Date(now)
        date.setDate(date.getDate() - (6 - i))

        // For the last day, use all remaining messages to ensure total is accurate
        let messageCount, sessionCount
        if (i === 6) {
          messageCount = remainingMessages
          sessionCount = remainingSessions
        } else {
          // Calculate messages for this day based on distribution
          messageCount = Math.round(totalMessages * dayDistribution[i])
          // Ensure we don't exceed total
          messageCount = Math.min(messageCount, remainingMessages)
          remainingMessages -= messageCount

          // Calculate sessions - always less than or equal to messages
          sessionCount = Math.min(
            messageCount,
            Math.round(uniqueSessions * dayDistribution[i])
          )
          sessionCount = Math.min(sessionCount, remainingSessions)
          remainingSessions -= sessionCount
        }

        data.push({
          date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          messages: messageCount,
          sessions: sessionCount
        })
      }

      console.log('ChatActivityChart: Generated activity data =', data)
      return data
    }

    // Generate data immediately to avoid flickering
    const generatedData = generateActivityData()
    console.log('ChatActivityChart: Setting activity data =', generatedData)
    setActivityData(generatedData)
    setIsLoading(false)

  }, [totalMessages, uniqueSessions, chatId, lastActivity])

  // Pie chart data - more accurate representation
  const pieData = useMemo(() => {
    console.log('Calculating pie data with totalMessages =', totalMessages)
    return totalMessages === 0
      ? [{ name: 'No Messages', value: 1 }]
      : [
          { name: 'User Messages', value: Math.ceil(totalMessages / 2) },
          { name: 'Bot Responses', value: Math.floor(totalMessages / 2) }
        ]
  }, [totalMessages])

  const COLORS = ['#3b82f6', '#10b981']

  if (isLoading) {
    return (
      <div className="h-40 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="w-8 h-8 border-4 border-neon-blue/30 border-t-neon-blue rounded-full animate-spin mb-2"></div>
          <div className="text-neon-blue/70 text-sm">Loading activity data...</div>
        </div>
      </div>
    )
  }

  // Show a special message for empty data
  if (totalMessages === 0 && !isLoading) {
    console.log('ChatActivityChart: No messages, showing empty state')
    return (
      <div className="mt-4">
        <div className="h-40 flex items-center justify-center bg-gray-800/30 rounded-lg border border-gray-700">
          <div className="text-center p-4">
            <p className="text-neon-blue/70 mb-2">No message activity yet</p>
            <p className="text-xs text-neon-blue/50">Activity charts will appear when messages are exchanged</p>
            <p className="text-xs text-neon-blue/50 mt-2">Debug: totalMessages={totalMessages}, uniqueSessions={uniqueSessions}</p>
          </div>
        </div>
      </div>
    )
  }

  console.log('ChatActivityChart: Rendering chart with data:', { totalMessages, uniqueSessions, lastActivity, activityData })

  return (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-3">
        <h4 className="text-sm font-medium text-neon-blue">
          Activity Overview
          <span className="text-xs ml-2 text-neon-blue/50">
            (Debug: {totalMessages} msgs, {uniqueSessions} sessions)
          </span>
        </h4>
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveChart('area')}
            className={`text-xs px-3 py-1.5 rounded-md transition-all duration-200 ${
              activeChart === 'area'
                ? 'bg-neon-blue text-white shadow-md shadow-neon-blue/20'
                : 'bg-gray-800/70 text-neon-blue/70 hover:bg-gray-700/90 hover:text-neon-blue'
            }`}
          >
            Area
          </button>
          <button
            onClick={() => setActiveChart('bar')}
            className={`text-xs px-3 py-1.5 rounded-md transition-all duration-200 ${
              activeChart === 'bar'
                ? 'bg-neon-blue text-white shadow-md shadow-neon-blue/20'
                : 'bg-gray-800/70 text-neon-blue/70 hover:bg-gray-700/90 hover:text-neon-blue'
            }`}
          >
            Bar
          </button>
          <button
            onClick={() => setActiveChart('pie')}
            className={`text-xs px-3 py-1.5 rounded-md transition-all duration-200 ${
              activeChart === 'pie'
                ? 'bg-neon-blue text-white shadow-md shadow-neon-blue/20'
                : 'bg-gray-800/70 text-neon-blue/70 hover:bg-gray-700/90 hover:text-neon-blue'
            }`}
          >
            Pie
          </button>
        </div>
      </div>

      <div className="bg-gray-800/30 p-3 rounded-lg border border-gray-700/50">
        <motion.div
          key={activeChart}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="h-40"
        >
          {activeChart === 'area' && (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={activityData}
                margin={{ top: 10, right: 10, left: -15, bottom: 0 }}
              >
                <defs>
                  <linearGradient id={`colorMessages-${chatId}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id={`colorSessions-${chatId}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#444" opacity={0.5} />
                <XAxis
                  dataKey="date"
                  tick={{ fill: '#aaa', fontSize: 11 }}
                  tickLine={{ stroke: '#555' }}
                  axisLine={{ stroke: '#555' }}
                />
                <YAxis
                  tick={{ fill: '#aaa', fontSize: 11 }}
                  tickLine={{ stroke: '#555' }}
                  axisLine={{ stroke: '#555' }}
                  width={25}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    borderColor: '#374151',
                    color: 'white',
                    borderRadius: '4px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }}
                  itemStyle={{ color: 'white', padding: '2px 0' }}
                  labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
                />
                <Area
                  type="monotone"
                  dataKey="messages"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  fillOpacity={1}
                  fill={`url(#colorMessages-${chatId})`}
                  name="Messages"
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: 'white' }}
                />
                <Area
                  type="monotone"
                  dataKey="sessions"
                  stroke="#10b981"
                  strokeWidth={2}
                  fillOpacity={1}
                  fill={`url(#colorSessions-${chatId})`}
                  name="Sessions"
                  activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2, fill: 'white' }}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'bar' && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={activityData}
                margin={{ top: 10, right: 10, left: -15, bottom: 0 }}
                barGap={2}
                barSize={12}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" opacity={0.5} />
                <XAxis
                  dataKey="date"
                  tick={{ fill: '#aaa', fontSize: 11 }}
                  tickLine={{ stroke: '#555' }}
                  axisLine={{ stroke: '#555' }}
                />
                <YAxis
                  tick={{ fill: '#aaa', fontSize: 11 }}
                  tickLine={{ stroke: '#555' }}
                  axisLine={{ stroke: '#555' }}
                  width={25}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    borderColor: '#374151',
                    color: 'white',
                    borderRadius: '4px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }}
                  itemStyle={{ color: 'white', padding: '2px 0' }}
                  labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
                  cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                />
                <Bar
                  dataKey="messages"
                  fill="#3b82f6"
                  name="Messages"
                  radius={[4, 4, 0, 0]}
                  animationDuration={750}
                  animationEasing="ease-out"
                >
                  {activityData.map((entry, index) => (
                    <Cell
                      key={`cell-messages-${index}`}
                      fill={entry.messages > 0 ? '#3b82f6' : '#3b82f680'}
                    />
                  ))}
                </Bar>
                <Bar
                  dataKey="sessions"
                  fill="#10b981"
                  name="Sessions"
                  radius={[4, 4, 0, 0]}
                  animationDuration={750}
                  animationEasing="ease-out"
                  animationBegin={100}
                >
                  {activityData.map((entry, index) => (
                    <Cell
                      key={`cell-sessions-${index}`}
                      fill={entry.sessions > 0 ? '#10b981' : '#10b98180'}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}

          {activeChart === 'pie' && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={30}
                  outerRadius={60}
                  fill="#8884d8"
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, percent }) => (
                    totalMessages === 0
                      ? 'No Messages'
                      : `${name}: ${(percent * 100).toFixed(0)}%`
                  )}
                  labelLine={false}
                  animationDuration={800}
                  animationEasing="ease-out"
                >
                  {pieData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={totalMessages === 0 ? '#6b7280' : COLORS[index % COLORS.length]}
                      stroke={totalMessages === 0 ? '#4b5563' : 'none'}
                      strokeWidth={1}
                    />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    borderColor: '#374151',
                    color: 'white',
                    borderRadius: '4px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }}
                  itemStyle={{ color: 'white', padding: '2px 0' }}
                  formatter={(value, name) => [
                    totalMessages === 0 ? 'No messages yet' : value,
                    name
                  ]}
                />
                <text
                  x="50%"
                  y="50%"
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fill="#fff"
                  fontSize={12}
                  fontWeight="bold"
                >
                  {totalMessages}
                </text>
              </PieChart>
            </ResponsiveContainer>
          )}
        </motion.div>

        <div className="mt-3 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-neon-blue"></div>
            <span className="text-xs text-neon-blue/80 font-medium">
              {totalMessages} total messages
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs text-neon-blue/80 font-medium">
              {uniqueSessions} conversations
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-amber-500"></div>
            <span className="text-xs text-neon-blue/80 font-medium">
              {lastActivity
                ? `Active ${new Date(lastActivity).toLocaleDateString()}`
                : 'No activity yet'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
