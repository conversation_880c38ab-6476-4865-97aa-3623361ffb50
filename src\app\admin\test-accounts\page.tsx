'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { createClient } from '@/lib/supabase/client'
import { UserTier, getTierDisplayName } from '@/lib/tiers'

interface TestAccount {
  id: string
  email: string
  tier: UserTier
  subscription_status: string
  chat_interface_count: number
  created_at: string
}

const TEST_ACCOUNT_IDS = [
  '********-1111-1111-1111-********1111', // Free tier
  '********-2222-2222-2222-********2222', // Standard tier
  '********-3333-3333-3333-********3333'  // Pro tier
]

export default function TestAccountsPage() {
  const [testAccounts, setTestAccounts] = useState<TestAccount[]>([])
  const [loading, setLoading] = useState(true)

  const supabase = createClient()

  useEffect(() => {
    fetchTestAccounts()
  }, [])

  const fetchTestAccounts = async () => {
    try {
      setLoading(true)

      // Fetch user profiles for test accounts
      const { data: profiles, error: profilesError } = await supabase
        .from('user_profiles')
        .select('*')
        .in('id', TEST_ACCOUNT_IDS)

      if (profilesError) {
        console.error('Error fetching test account profiles:', profilesError)
        toast.error('Failed to fetch test account profiles')
        return
      }

      // Fetch auth users for emails
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()

      if (authError) {
        console.error('Error fetching auth users:', authError)
        // Continue without email data if auth admin access is not available
      }

      // Fetch chat interface counts
      const { data: chatCounts, error: chatError } = await supabase
        .from('chat_interfaces')
        .select('user_id')
        .in('user_id', TEST_ACCOUNT_IDS)

      if (chatError) {
        console.error('Error fetching chat interface counts:', chatError)
      }

      // Combine data
      const accountsWithData = profiles.map(profile => {
        const authUser = authUsers?.users.find(u => u.id === profile.id)
        const chatCount = chatCounts?.filter(c => c.user_id === profile.id).length || 0

        return {
          id: profile.id,
          email: authUser?.email || getTestEmail(profile.id),
          tier: profile.tier,
          subscription_status: profile.subscription_status,
          chat_interface_count: chatCount,
          created_at: profile.created_at
        }
      })

      setTestAccounts(accountsWithData)
    } catch (error) {
      console.error('Error in fetchTestAccounts:', error)
      toast.error('Failed to fetch test accounts')
    } finally {
      setLoading(false)
    }
  }

  const getTestEmail = (userId: string) => {
    switch (userId) {
      case '********-1111-1111-1111-********1111':
        return '<EMAIL>'
      case '********-2222-2222-2222-********2222':
        return '<EMAIL>'
      case '********-3333-3333-3333-********3333':
        return '<EMAIL>'
      default:
        return '<EMAIL>'
    }
  }

  const createTestData = async () => {
    try {
      const response = await fetch('/api/admin/create-test-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create test data')
      }

      toast.success('Test data created successfully')
      fetchTestAccounts()
    } catch (error) {
      console.error('Error creating test data:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create test data')
    }
  }

  const cleanupTestData = async () => {
    if (!confirm('Are you sure you want to delete all test data? This cannot be undone.')) {
      return
    }

    try {
      const response = await fetch('/api/admin/cleanup-test-data', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to cleanup test data')
      }

      toast.success('Test data cleaned up successfully')
      fetchTestAccounts()
    } catch (error) {
      console.error('Error cleaning up test data:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to cleanup test data')
    }
  }

  const getTierBadgeColor = (tier: UserTier) => {
    switch (tier) {
      case 'free': return 'bg-gray-500'
      case 'standard': return 'bg-blue-500'
      case 'pro': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'cancelled': return 'bg-yellow-500'
      case 'expired': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
            <p className="mt-4">Loading test accounts...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🧪 Test Accounts Management
          </h1>
          <p className="text-xl text-gray-300">
            Manage test accounts for tier system testing
          </p>
        </div>

        {/* Actions */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <div className="flex gap-4 justify-center">
            <Button
              onClick={createTestData}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              🔧 Create Test Data
            </Button>
            <Button
              onClick={fetchTestAccounts}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              🔄 Refresh
            </Button>
            <Button
              onClick={cleanupTestData}
              variant="destructive"
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              🗑️ Cleanup Test Data
            </Button>
          </div>
        </Card>

        {/* Test Accounts */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {TEST_ACCOUNT_IDS.map(accountId => {
            const account = testAccounts.find(a => a.id === accountId)
            const email = getTestEmail(accountId)
            const tier = accountId.startsWith('1') ? 'free' : 
                        accountId.startsWith('2') ? 'standard' : 'pro'

            return (
              <Card key={accountId} className="bg-white/10 border-gray-600 p-6">
                <div className="space-y-4">
                  <div className="text-center">
                    <Badge className={`${getTierBadgeColor(tier)} text-white mb-2`}>
                      {getTierDisplayName(tier)} Tier
                    </Badge>
                    <h3 className="text-lg font-semibold text-white">
                      {email}
                    </h3>
                  </div>

                  {account ? (
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-300">Status:</span>
                        <Badge className={`${getStatusBadgeColor(account.subscription_status)} text-white`}>
                          {account.subscription_status}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Chat Interfaces:</span>
                        <span className="text-white">{account.chat_interface_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Created:</span>
                        <span className="text-white">
                          {new Date(account.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-red-400 text-sm">Account not found</p>
                      <p className="text-gray-400 text-xs mt-1">
                        Run "Create Test Data" to set up this account
                      </p>
                    </div>
                  )}

                  <div className="pt-2 border-t border-gray-600">
                    <p className="text-xs text-gray-400 text-center">
                      ID: {accountId.slice(0, 8)}...
                    </p>
                  </div>
                </div>
              </Card>
            )
          })}
        </div>

        {/* Instructions */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">📋 Testing Instructions</h2>
          <div className="space-y-3 text-gray-300">
            <div>
              <h3 className="font-semibold text-white">1. Create Auth Users</h3>
              <p className="text-sm">
                First, create the auth users manually in Supabase dashboard or use the admin API.
                See the setup guide for detailed instructions.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white">2. Create Test Data</h3>
              <p className="text-sm">
                Click "Create Test Data" to set up user profiles and sample chat interfaces.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white">3. Test Tier Features</h3>
              <p className="text-sm">
                Login with each test account to verify tier restrictions work correctly.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white">4. Cleanup</h3>
              <p className="text-sm">
                Use "Cleanup Test Data" to remove test data when done testing.
              </p>
            </div>
          </div>
        </Card>

        {/* Test Credentials */}
        <Card className="bg-white/10 border-gray-600 p-6">
          <h2 className="text-xl font-bold text-white mb-4">🔑 Test Credentials</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-gray-800/50 p-3 rounded">
              <h3 className="font-semibold text-white mb-2">Free Tier</h3>
              <p className="text-gray-300">Email: <EMAIL></p>
              <p className="text-gray-300">Password: TestFree123!</p>
            </div>
            <div className="bg-gray-800/50 p-3 rounded">
              <h3 className="font-semibold text-white mb-2">Standard Tier</h3>
              <p className="text-gray-300">Email: <EMAIL></p>
              <p className="text-gray-300">Password: TestStandard123!</p>
            </div>
            <div className="bg-gray-800/50 p-3 rounded">
              <h3 className="font-semibold text-white mb-2">Pro Tier</h3>
              <p className="text-gray-300">Email: <EMAIL></p>
              <p className="text-gray-300">Password: TestPro123!</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
