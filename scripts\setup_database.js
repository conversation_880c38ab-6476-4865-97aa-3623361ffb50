import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

// Get current directory path in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables from .env.local
dotenv.config({ path: path.join(__dirname, '../.env.local') })

async function setupDatabase() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase URL or API Key')
    process.exit(1)
  }

  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('Connected to Supabase')
  
  // Check if required tables exist
  const requiredTables = ['webhooks', 'chat_interfaces', 'chat_stats']
  
  for (const tableName of requiredTables) {
    const { data, error } = await supabase
      .from('pg_tables')
      .select('*')
      .eq('tablename', tableName)
      .eq('schemaname', 'public')
    
    if (error) {
      console.error(`Error checking table ${tableName}:`, error)
      continue
    }
    
    if (data.length === 0) {
      console.log(`Table ${tableName} does not exist. Creating...`)
      
      // Find the migration file for this table
      const migrationFiles = fs.readdirSync(path.join(__dirname, '../migrations'))
        .filter(file => file.endsWith('.sql') && file.includes(tableName))
        .sort()
      
      if (migrationFiles.length === 0) {
        console.error(`No migration file found for table ${tableName}`)
        continue
      }
      
      // Read and execute the migration
      const sql = fs.readFileSync(path.join(__dirname, '../migrations', migrationFiles[0]), 'utf8')
      
      try {
        // Execute the SQL directly
        const { error: sqlError } = await supabase.rpc('execute_sql', { sql })
        
        if (sqlError) {
          console.error(`Error creating table ${tableName}:`, sqlError)
          continue
        }
        
        console.log(`Table ${tableName} created successfully`)
      } catch (err) {
        console.error(`Error executing SQL for ${tableName}:`, err)
        
        // Fallback: Try to create the table using the SQL from the migration file
        // This is a simplified approach and may not work for complex migrations
        if (tableName === 'webhooks') {
          await createWebhooksTable(supabase)
        } else if (tableName === 'chat_interfaces') {
          await createChatInterfacesTable(supabase)
        } else if (tableName === 'chat_stats') {
          await createChatStatsTable(supabase)
        }
      }
    } else {
      console.log(`Table ${tableName} already exists`)
    }
  }
  
  console.log('Database setup completed')
}

async function createWebhooksTable(supabase) {
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS webhooks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        description TEXT,
        createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "Enable read access for all users" ON webhooks
        FOR SELECT USING (true);
      
      CREATE POLICY "Enable insert access for authenticated users" ON webhooks
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    `
  })
  
  if (error) {
    console.error('Error creating webhooks table:', error)
  } else {
    console.log('Webhooks table created successfully')
  }
}

async function createChatInterfacesTable(supabase) {
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS chat_interfaces (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        description TEXT,
        webhookUrl TEXT,
        user_id UUID REFERENCES auth.users(id),
        createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      ALTER TABLE chat_interfaces ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "Enable read access for all users" ON chat_interfaces
        FOR SELECT USING (true);
      
      CREATE POLICY "Enable insert access for authenticated users" ON chat_interfaces
        FOR INSERT WITH CHECK (auth.uid() = user_id);
      
      CREATE POLICY "Enable update access for authenticated users" ON chat_interfaces
        FOR UPDATE USING (auth.uid() = user_id);
    `
  })
  
  if (error) {
    console.error('Error creating chat_interfaces table:', error)
  } else {
    console.log('Chat interfaces table created successfully')
  }
}

async function createChatStatsTable(supabase) {
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS chat_stats (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        session_id TEXT NOT NULL,
        user_id UUID REFERENCES auth.users(id),
        message_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      ALTER TABLE chat_stats ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "Enable read access for all users" ON chat_stats
        FOR SELECT USING (true);
      
      CREATE POLICY "Enable insert access for authenticated users" ON chat_stats
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
      
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
      
      CREATE TRIGGER update_chat_stats_updated_at
      BEFORE UPDATE ON chat_stats
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `
  })
  
  if (error) {
    console.error('Error creating chat_stats table:', error)
  } else {
    console.log('Chat stats table created successfully')
  }
}

setupDatabase().catch(err => {
  console.error('Database setup failed:', err)
  process.exit(1)
})