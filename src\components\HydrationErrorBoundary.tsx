'use client'

import { useEffect, useState } from 'react'

export default function HydrationErrorBoundary({
  children,
}: {
  children: React.ReactNode
}) {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    const handleError = (error: Error) => {
      if (error.message.includes('hydration')) {
        console.warn('Hydration mismatch detected:', error.message)
        if (error.message.includes('foxified')) {
          console.warn('Detected browser extension interference. Try disabling extensions.')
        }
        setHasError(true)
        // Attempt to recover by forcing a re-render
        setTimeout(() => setHasError(false), 100)
      }
    }

    // Check for extension interference on initial load
    if (document.documentElement.hasAttribute('foxified')) {
      console.warn('Browser extension modifying HTML detected')
    }

    window.addEventListener('error', (e) => handleError(e.error))
    return () => window.removeEventListener('error', (e) => handleError(e.error))
  }, [])

  if (hasError) {
    return (
      <div className="p-4 text-center text-sm text-yellow-500">
        Recovering from hydration mismatch...
      </div>
    )
  }

  return children
}