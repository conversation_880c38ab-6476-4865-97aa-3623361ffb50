<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Natural Speech TTS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison {
            display: flex;
            gap: 20px;
        }
        .comparison > div {
            flex: 1;
        }
        audio {
            width: 100%;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Natural Speech TTS Test</h1>
        <p>This page tests the new natural speech processing functionality using GPT-4o-mini.</p>

        <div class="test-section">
            <h3>Test Text</h3>
            <textarea id="testText" placeholder="Enter text to test...">Hello! I'm excited to help you today. Let me know what you need assistance with and I'll do my best to provide you with accurate and helpful information.</textarea>
            
            <div class="comparison">
                <div>
                    <h4>Standard TTS</h4>
                    <button onclick="testStandardTTS()" id="standardBtn">Test Standard TTS</button>
                    <audio id="standardAudio" controls style="display: none;"></audio>
                    <div id="standardStatus"></div>
                </div>
                
                <div>
                    <h4>Natural Speech TTS</h4>
                    <button onclick="testNaturalTTS()" id="naturalBtn">Test Natural Speech TTS</button>
                    <audio id="naturalAudio" controls style="display: none;"></audio>
                    <div id="naturalStatus"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>API Endpoint Tests</h3>
            <button onclick="testNaturalTTSAPI()" id="apiBtn">Test Natural TTS API</button>
            <div id="apiStatus"></div>
            <pre id="apiResponse" style="background: #f8f9fa; padding: 10px; border-radius: 5px; display: none;"></pre>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        async function testStandardTTS() {
            const text = document.getElementById('testText').value;
            const btn = document.getElementById('standardBtn');
            const audio = document.getElementById('standardAudio');
            
            if (!text.trim()) {
                showStatus('standardStatus', 'Please enter some text to test', 'error');
                return;
            }

            btn.disabled = true;
            showStatus('standardStatus', 'Generating standard TTS...', 'info');

            try {
                const response = await fetch('/api/deepgram-tts?' + new URLSearchParams({
                    text: text,
                    voice: 'aura-2-zeus-en',
                    t: Date.now()
                }));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const audioBlob = await response.blob();
                const audioUrl = URL.createObjectURL(audioBlob);
                
                audio.src = audioUrl;
                audio.style.display = 'block';
                showStatus('standardStatus', 'Standard TTS generated successfully!', 'success');
                
            } catch (error) {
                console.error('Standard TTS error:', error);
                showStatus('standardStatus', `Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function testNaturalTTS() {
            const text = document.getElementById('testText').value;
            const btn = document.getElementById('naturalBtn');
            const audio = document.getElementById('naturalAudio');
            
            if (!text.trim()) {
                showStatus('naturalStatus', 'Please enter some text to test', 'error');
                return;
            }

            btn.disabled = true;
            showStatus('naturalStatus', 'Processing with GPT-4o-mini and generating natural TTS...', 'info');

            try {
                const response = await fetch('/api/natural-tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        voiceModel: 'aura-2-zeus-en',
                        enableNaturalSpeech: true,
                        streamResponse: false
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const audioBlob = await response.blob();
                const audioUrl = URL.createObjectURL(audioBlob);
                
                audio.src = audioUrl;
                audio.style.display = 'block';
                showStatus('naturalStatus', 'Natural speech TTS generated successfully!', 'success');
                
            } catch (error) {
                console.error('Natural TTS error:', error);
                showStatus('naturalStatus', `Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function testNaturalTTSAPI() {
            const text = document.getElementById('testText').value;
            const btn = document.getElementById('apiBtn');
            const responseEl = document.getElementById('apiResponse');
            
            if (!text.trim()) {
                showStatus('apiStatus', 'Please enter some text to test', 'error');
                return;
            }

            btn.disabled = true;
            showStatus('apiStatus', 'Testing Natural TTS API...', 'info');

            try {
                const response = await fetch('/api/natural-tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        voiceModel: 'aura-2-zeus-en',
                        enableNaturalSpeech: true,
                        streamResponse: false
                    })
                });

                const responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: responseHeaders,
                    ok: response.ok
                };

                if (response.ok) {
                    result.contentType = response.headers.get('content-type');
                    result.contentLength = response.headers.get('content-length');
                    showStatus('apiStatus', 'API test successful!', 'success');
                } else {
                    result.error = await response.text();
                    showStatus('apiStatus', `API test failed: ${response.status}`, 'error');
                }

                responseEl.textContent = JSON.stringify(result, null, 2);
                responseEl.style.display = 'block';
                
            } catch (error) {
                console.error('API test error:', error);
                showStatus('apiStatus', `Error: ${error.message}`, 'error');
                responseEl.textContent = `Error: ${error.message}`;
                responseEl.style.display = 'block';
            } finally {
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>
