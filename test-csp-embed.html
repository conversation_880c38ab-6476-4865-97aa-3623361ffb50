<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP-Friendly Embed Test</title>
    <!-- Strict CSP that blocks inline scripts -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app;
        frame-src 'self' https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app;
        frame-ancestors 'self' https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app;
        connect-src 'self' https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app;
    ">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .note {
            background-color: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 10px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .content {
            min-height: 1000px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>CSP-Friendly Embed Test</h1>

    <div class="note">
        <strong>Important:</strong> This page has a strict Content Security Policy (CSP) that blocks inline scripts.
        Please open your browser's developer console (F12 or right-click > Inspect > Console)
        to check for any errors during the script loading process.
    </div>

    <h2>What to Check For:</h2>
    <ol>
        <li><strong>Script loading errors</strong> - Look for any errors related to loading the embed script</li>
        <li><strong>X-Frame-Options errors</strong> - When the chat button is clicked, check if the iframe loads without X-Frame-Options errors</li>
        <li><strong>Content Security Policy (CSP) errors</strong> - Look for messages about CSP blocking scripts or resources</li>
        <li><strong>401 Unauthorized errors</strong> - Check if any API requests are returning 401 status codes</li>
        <li><strong>CORS errors</strong> - Look for messages about cross-origin requests being blocked</li>
        <li><strong>Chat button icon</strong> - Check if the chat button icon renders correctly from the start, without any flickering or refreshing</li>
        <li><strong>Icon after closing</strong> - Close the chat and check if the icon remains correct</li>
        <li><strong>No refresh animation</strong> - When clicking the chat button, the chat should open immediately without any refresh animation</li>
    </ol>

    <div class="content">
        <h2>Sample Content</h2>
        <p>This is some sample content to demonstrate the chat widget. The chat button should appear in the bottom-right corner of the page.</p>
        <p>Click the chat button to open the chat widget and check for any errors in the console.</p>
        <p>Scroll down to see more content...</p>

        <div style="height: 500px;"></div>

        <h2>More Content</h2>
        <p>This is more sample content to demonstrate the chat widget. The chat button should remain fixed in the bottom-right corner of the page.</p>
    </div>

    <!-- BotFusion Chat Widget - CSP-Friendly Version -->
    <!-- Add this script tag at the end of your body section -->
    <script src="https://roo-bot-fusion-kgfs-jg13pb9zg-tellivisions-projects.vercel.app/api/csp-embed-script?chatId=b76a3980-9f8e-47cd-ae7d-f02747552c4d"></script>
</body>
</html>
