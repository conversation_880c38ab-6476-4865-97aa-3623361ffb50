import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function runMigration() {
  // Get Supabase credentials from environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables')
    return
  }

  // Create Supabase client with service role key for admin access
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('Connected to Supabase')
  
  // Read the migration SQL file
  const migrationPath = path.join(__dirname, '..', 'migrations', 'add_powered_by_columns.sql')
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
  
  console.log('Running migration:')
  console.log(migrationSQL)
  
  try {
    // Execute the SQL directly
    const { data, error } = await supabase.rpc('execute_sql', {
      sql: migrationSQL
    })
    
    if (error) {
      console.error('Error running migration:', error)
      return
    }
    
    console.log('Migration completed successfully')
    
    // Verify the columns were added
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'chat_interfaces')
      .in('column_name', ['show_powered_by', 'powered_by_text', 'powered_by_url'])
    
    if (columnsError) {
      console.error('Error verifying columns:', columnsError)
      return
    }
    
    console.log('Columns added to chat_interfaces table:')
    console.table(columns)
  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

runMigration().catch(console.error)
