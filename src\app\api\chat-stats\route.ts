import { NextResponse } from 'next/server'
import { createClient, createServiceClient } from '@/lib/supabase/server'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const chatId = searchParams.get('chatId')
  const forceRefresh = searchParams.get('forceRefresh') === 'true'

  if (!chatId) {
    return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 })
  }

  try {
    console.log(`Getting chat stats for chatId: ${chatId}`)
    const supabase = createClient()

    // Check if we need to update the chat_stats table
    if (forceRefresh) {
      console.log(`Force refreshing stats for chatId: ${chatId}`)
      // Use service client for updating stats to bypass RLS
      const serviceClient = createServiceClient()
      await updateChatStats(serviceClient, chatId)
    }

    // Try to get stats from chat_stats table first (more efficient)
    const { data: statsData, error: statsError } = await supabase
      .from('chat_stats')
      .select('message_count, last_message_at, session_id')
      .eq('chat_interface_id', chatId)

    // If we have stats in the chat_stats table, use them
    if (!statsError && statsData && statsData.length > 0) {
      console.log(`Found ${statsData.length} stats entries for chatId ${chatId}`)

      // Calculate total messages and unique sessions
      const totalMessages = statsData.reduce((sum, stat) => sum + (stat.message_count || 0), 0)
      const uniqueSessions = new Set(statsData.map(stat => stat.session_id)).size

      // Find the most recent activity
      const lastActivityTime = statsData.reduce(
        (latest, stat) => {
          if (!latest || (stat.last_message_at && new Date(stat.last_message_at) > new Date(latest))) {
            return stat.last_message_at
          }
          return latest
        },
        null
      )

      // Get recent messages
      const { data: recentMessages, error: recentMessagesError } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('chat_interface_id', chatId)
        .order('created_at', { ascending: false })
        .limit(10)

      if (recentMessagesError) {
        console.error('Error getting recent messages:', recentMessagesError)
      }

      const response = {
        totalMessages,
        uniqueSessions,
        lastActivity: lastActivityTime,
        recentMessages: recentMessages || []
      }

      console.log(`Returning chat stats from chat_stats table for chatId ${chatId}:`, response)
      return NextResponse.json(response)
    }

    // Fallback to calculating stats directly from chat_messages
    console.log(`No stats found in chat_stats table for chatId ${chatId}, calculating from messages`)

    // Get total message count
    const { count: totalMessages, error: countError } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact', head: true })
      .eq('chat_interface_id', chatId)

    if (countError) {
      console.error('Error getting message count:', countError)
      return NextResponse.json({ error: countError.message }, { status: 500 })
    }

    console.log(`Total messages for chatId ${chatId}: ${totalMessages}`)

    // Get unique session count (unique users/conversations)
    const { data: sessions, error: sessionsError } = await supabase
      .from('chat_messages')
      .select('session_id')
      .eq('chat_interface_id', chatId)
      .eq('sender', 'user') // Only count user messages to avoid duplicating sessions

    if (sessionsError) {
      console.error('Error getting sessions:', sessionsError)
      return NextResponse.json({ error: sessionsError.message }, { status: 500 })
    }

    // Count unique sessions
    const uniqueSessions = sessions && sessions.length > 0 ? new Set(sessions.map(msg => msg.session_id)).size : 0
    console.log(`Unique sessions for chatId ${chatId}: ${uniqueSessions}`)
    console.log(`Session IDs: ${sessions && sessions.length > 0 ? [...new Set(sessions.map(msg => msg.session_id))].join(', ') : 'none'}`)

    // Get last activity timestamp
    const { data: lastActivity, error: lastActivityError } = await supabase
      .from('chat_messages')
      .select('created_at')
      .eq('chat_interface_id', chatId)
      .order('created_at', { ascending: false })
      .limit(1)

    if (lastActivityError) {
      console.error('Error getting last activity:', lastActivityError)
      return NextResponse.json({ error: lastActivityError.message }, { status: 500 })
    }

    const lastActivityTime = lastActivity && lastActivity.length > 0 ? lastActivity[0].created_at : null
    console.log(`Last activity for chatId ${chatId}: ${lastActivityTime}`)

    // Get recent messages
    const { data: recentMessages, error: recentMessagesError } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('chat_interface_id', chatId)
      .order('created_at', { ascending: false })
      .limit(10)

    if (recentMessagesError) {
      console.error('Error getting recent messages:', recentMessagesError)
      return NextResponse.json({ error: recentMessagesError.message }, { status: 500 })
    }

    console.log(`Recent messages for chatId ${chatId}: ${recentMessages ? recentMessages.length : 0}`)

    // Update the chat_stats table for future queries
    // Use service client for updating stats to bypass RLS
    const serviceClient = createServiceClient()
    await updateChatStats(serviceClient, chatId)

    const response = {
      totalMessages: totalMessages || 0,
      uniqueSessions: uniqueSessions || 0,
      lastActivity: lastActivityTime,
      recentMessages: recentMessages || []
    }

    console.log(`Returning chat stats from direct calculation for chatId ${chatId}:`, response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('Error getting chat stats:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * Updates the chat_stats table with the latest statistics from chat_messages
 */
async function updateChatStats(supabase: ReturnType<typeof createClient> | ReturnType<typeof createServiceClient>, chatId: string) {
  try {
    console.log(`Updating chat_stats for chatId: ${chatId}`)

    // Get all sessions for this chat interface
    const { data: sessions, error: sessionsError } = await supabase
      .from('chat_messages')
      .select('session_id')
      .eq('chat_interface_id', chatId)
      .eq('sender', 'user') // Only count user messages to avoid duplicating sessions

    if (sessionsError) {
      console.error('Error getting sessions for stats update:', sessionsError)
      return
    }

    // Get unique session IDs
    const uniqueSessionIds = sessions && sessions.length > 0
      ? [...new Set(sessions.map((msg: { session_id: string }) => msg.session_id))]
      : []

    console.log(`Found ${uniqueSessionIds.length} unique sessions for chatId ${chatId}`)

    // For each session, update or insert stats
    for (const sessionId of uniqueSessionIds) {
      // Count messages for this session
      const { count: messageCount, error: countError } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('chat_interface_id', chatId)
        .eq('session_id', sessionId)

      if (countError) {
        console.error(`Error counting messages for session ${sessionId}:`, countError)
        continue
      }

      // Get last activity for this session
      const { data: lastActivity, error: lastActivityError } = await supabase
        .from('chat_messages')
        .select('created_at')
        .eq('chat_interface_id', chatId)
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false })
        .limit(1)

      if (lastActivityError) {
        console.error(`Error getting last activity for session ${sessionId}:`, lastActivityError)
        continue
      }

      const lastActivityTime = lastActivity && lastActivity.length > 0 ? lastActivity[0].created_at : null

      // Check if stats already exist for this session
      const { data: existingStats, error: existingStatsError } = await supabase
        .from('chat_stats')
        .select('id')
        .eq('chat_interface_id', chatId)
        .eq('session_id', sessionId)
        .limit(1)

      if (existingStatsError) {
        console.error(`Error checking existing stats for session ${sessionId}:`, existingStatsError)
        continue
      }

      if (existingStats && existingStats.length > 0) {
        // Update existing stats
        const { error: updateError } = await supabase
          .from('chat_stats')
          .update({
            message_count: messageCount,
            last_message_at: lastActivityTime,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingStats[0].id)

        if (updateError) {
          console.error(`Error updating stats for session ${sessionId}:`, updateError)
        } else {
          console.log(`Updated stats for session ${sessionId}: ${messageCount} messages, last activity: ${lastActivityTime}`)
        }
      } else {
        // Insert new stats
        const { error: insertError } = await supabase
          .from('chat_stats')
          .insert({
            chat_interface_id: chatId,
            session_id: sessionId,
            message_count: messageCount,
            last_message_at: lastActivityTime,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (insertError) {
          console.error(`Error inserting stats for session ${sessionId}:`, insertError)
        } else {
          console.log(`Inserted stats for session ${sessionId}: ${messageCount} messages, last activity: ${lastActivityTime}`)
        }
      }
    }

    console.log(`Finished updating chat_stats for chatId: ${chatId}`)
  } catch (error) {
    console.error(`Error updating chat_stats for chatId ${chatId}:`, error)
  }
}
