import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Creating user profiles...')

    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    const results = []

    // 1. Check if user_profiles table exists
    console.log('📋 Checking if user_profiles table exists...')
    const { data: tableCheck, error: tableError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1)

    if (tableError) {
      console.error('❌ user_profiles table does not exist:', tableError)
      return NextResponse.json({
        error: 'Database not properly set up',
        details: 'user_profiles table does not exist. Please run the database setup SQL first.',
        sqlFile: 'Run database_setup.sql in your Supabase SQL Editor',
        tableError: tableError.message
      }, { status: 500 })
    }

    console.log('✅ user_profiles table exists')

    // 2. Get all existing users
    console.log('👥 Fetching existing users...')
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError)
      return NextResponse.json({
        error: 'Failed to fetch users',
        details: usersError.message
      }, { status: 500 })
    }

    console.log(`📊 Found ${users.users.length} existing users`)

    // 3. Create profiles for all users
    for (const user of users.users) {
      try {
        // Set admin user to Pro tier, others to Free
        const tier = user.email === '<EMAIL>' ? 'pro' : 'free'
        const now = new Date().toISOString()
        const oneYearFromNow = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()

        console.log(`Creating profile for ${user.email} with ${tier} tier...`)

        const { error: profileError } = await supabase
          .from('user_profiles')
          .upsert({
            id: user.id,
            tier,
            subscription_status: 'active',
            subscription_start_date: now,
            subscription_end_date: tier === 'free' ? null : oneYearFromNow,
            created_at: now,
            updated_at: now
          })

        if (profileError) {
          console.error(`❌ Error creating profile for ${user.email}:`, profileError)
          results.push({
            email: user.email,
            success: false,
            error: profileError.message
          })
        } else {
          console.log(`✅ Profile created for ${user.email} with ${tier} tier`)
          results.push({
            email: user.email,
            tier,
            success: true
          })
        }
      } catch (error) {
        console.error(`❌ Error processing user ${user.email}:`, error)
        results.push({
          email: user.email,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // 4. Verify profiles were created
    console.log('🔍 Verifying profiles...')
    const { data: profileCount, error: countError } = await supabase
      .from('user_profiles')
      .select('tier', { count: 'exact' })

    if (countError) {
      console.error('❌ Error counting profiles:', countError)
    } else {
      console.log(`✅ Total profiles in database: ${profileCount.length}`)
    }

    // 5. Check admin profile specifically
    const { data: adminProfile, error: adminError } = await supabase
      .from('user_profiles')
      .select('tier, subscription_status')
      .eq('id', users.users.find(u => u.email === '<EMAIL>')?.id || '')
      .single()

    const successCount = results.filter(r => r.success).length
    const totalUsers = results.length

    return NextResponse.json({
      message: 'User profile creation completed',
      success: successCount === totalUsers,
      results,
      summary: {
        total: totalUsers,
        successful: successCount,
        failed: totalUsers - successCount,
        adminProfile: adminProfile || null,
        adminProfileError: adminError?.message || null
      },
      verification: {
        totalProfilesInDB: profileCount?.length || 0,
        adminTier: adminProfile?.tier || 'unknown'
      }
    })
  } catch (error) {
    console.error('💥 Error creating profiles:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check profile status
export async function GET() {
  try {
    const supabase = createServiceClient()
    if (!supabase) {
      return NextResponse.json({ 
        error: 'Service client not available' 
      }, { status: 500 })
    }

    // Check table existence
    const { data: tableCheck, error: tableError } = await supabase
      .from('user_profiles')
      .select('id, tier, subscription_status')
      .limit(5)

    if (tableError) {
      return NextResponse.json({
        tableExists: false,
        error: tableError.message,
        solution: 'Run database_setup.sql in Supabase SQL Editor'
      })
    }

    // Get profile counts
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('tier')

    if (profilesError) {
      return NextResponse.json({
        tableExists: true,
        error: profilesError.message
      })
    }

    const tierCounts = profiles.reduce((acc, profile) => {
      acc[profile.tier] = (acc[profile.tier] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Check admin profile
    const { data: users } = await supabase.auth.admin.listUsers()
    const adminUser = users.users.find(u => u.email === '<EMAIL>')
    
    let adminProfile = null
    if (adminUser) {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('tier, subscription_status')
        .eq('id', adminUser.id)
        .single()
      
      adminProfile = profile
    }

    return NextResponse.json({
      tableExists: true,
      totalProfiles: profiles.length,
      tierCounts,
      adminProfile,
      adminEmail: '<EMAIL>',
      sampleProfiles: tableCheck.slice(0, 3)
    })
  } catch (error) {
    console.error('Error checking profile status:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
