// <PERSON>ript to check for console errors on the deployed TTS test page
import puppeteer from 'puppeteer';

// Deployed URL
const DEPLOYED_URL = 'https://roo-bot-fusion-kgfs-bg671eiav-tellivisions-projects.vercel.app/tts-test';

async function checkConsoleErrors() {
  console.log(`Testing console errors on ${DEPLOYED_URL}...`);
  
  // Launch a headless browser
  const browser = await puppeteer.launch({
    headless: false, // Set to true for headless mode
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    // Create a new page
    const page = await browser.newPage();
    
    // Collect console messages
    const consoleMessages = {
      logs: [],
      errors: [],
      warnings: []
    };
    
    // Listen for console events
    page.on('console', (message) => {
      const type = message.type();
      const text = message.text();
      
      console.log(`[Browser Console ${type}]: ${text}`);
      
      if (type === 'error') {
        consoleMessages.errors.push(text);
      } else if (type === 'warning') {
        consoleMessages.warnings.push(text);
      } else {
        consoleMessages.logs.push(text);
      }
    });
    
    // Listen for page errors
    page.on('pageerror', (error) => {
      console.error(`[Browser Page Error]: ${error.message}`);
      consoleMessages.errors.push(error.message);
    });
    
    // Listen for request failures
    page.on('requestfailed', (request) => {
      const failure = request.failure();
      const errorText = `Failed request: ${request.url()} - ${failure ? failure.errorText : 'Unknown error'}`;
      console.error(`[Browser Request Failed]: ${errorText}`);
      consoleMessages.errors.push(errorText);
    });
    
    // Navigate to the page
    console.log('Navigating to the page...');
    await page.goto(DEPLOYED_URL, { waitUntil: 'networkidle2', timeout: 60000 });
    console.log('Page loaded successfully');
    
    // Wait for a moment to capture any delayed errors
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Test the TTS functionality
    console.log('Testing TTS functionality...');
    
    // Click the Generate Speech button
    const generateButton = await page.$('button:not([disabled]):has-text("Generate Speech")');
    if (generateButton) {
      await generateButton.click();
      console.log('Clicked Generate Speech button');
      
      // Wait for potential errors
      await new Promise(resolve => setTimeout(resolve, 10000));
    } else {
      console.warn('Generate Speech button not found or disabled');
    }
    
    // Print summary
    console.log('\n--- Console Messages Summary ---');
    console.log(`Errors: ${consoleMessages.errors.length}`);
    console.log(`Warnings: ${consoleMessages.warnings.length}`);
    console.log(`Logs: ${consoleMessages.logs.length}`);
    
    if (consoleMessages.errors.length > 0) {
      console.log('\n--- Errors ---');
      consoleMessages.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    if (consoleMessages.warnings.length > 0) {
      console.log('\n--- Warnings ---');
      consoleMessages.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }
    
    // Keep the browser open for manual inspection
    console.log('\nBrowser will remain open for manual inspection.');
    console.log('Press Ctrl+C to close the browser and exit the script.');
    
    // Wait indefinitely (until the script is manually terminated)
    await new Promise(() => {});
    
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    // This won't be reached due to the indefinite promise above,
    // but it's here for completeness
    await browser.close();
  }
}

// Run the test
checkConsoleErrors().catch(console.error);
