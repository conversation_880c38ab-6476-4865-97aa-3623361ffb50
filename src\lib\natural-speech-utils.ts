import OpenAI from 'openai';

// Initialize OpenAI client (will be null if API key not configured)
let openai: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
} catch (error) {
  console.warn('Failed to initialize OpenAI client:', error);
}

// Configuration for natural speech processing
export const NATURAL_SPEECH_CONFIG = {
  model: 'gpt-4o-mini', // Default model - can be overridden per request
  maxTokens: 200, // OPTIMIZATION: Reduced from 500 to 200 for faster processing
  temperature: 0.3, // OPTIMIZATION: Reduced from 0.7 to 0.3 for more consistent, faster responses
  systemPrompt: `You are a natural speech optimizer for text-to-speech systems. Transform text to sound more natural when spoken aloud.

RULES:
1. Keep the core meaning and information intact
2. Make the text flow naturally when spoken
3. Add appropriate pauses with commas and periods
4. Break up long sentences into shorter, more digestible chunks
5. Use conversational language and contractions where appropriate
6. Add natural speech patterns like "Well," "You know," "Actually," when it makes sense
7. Remove awkward phrasing that sounds robotic
8. Ensure smooth transitions between ideas
9. Keep the response length similar to the original
10. Make it sound like a human is speaking, not reading from a script

Transform the following text to sound more natural when spoken:`,
};

export interface NaturalSpeechResult {
  originalText: string;
  processedText: string;
  processed: boolean;
  tokensUsed?: number;
  error?: string;
  warning?: string;
}

/**
 * Process text through OpenAI models for more natural speech patterns
 */
export async function processTextForNaturalSpeech(
  text: string,
  enableProcessing: boolean = true,
  model: string = NATURAL_SPEECH_CONFIG.model
): Promise<NaturalSpeechResult> {
  
  // If processing is disabled, return original text
  if (!enableProcessing) {
    return {
      originalText: text,
      processedText: text,
      processed: false
    };
  }

  // If no OpenAI client, return original text
  if (!openai) {
    return {
      originalText: text,
      processedText: text,
      processed: false,
      warning: 'OpenAI API key not configured'
    };
  }

  // OPTIMIZATION: Skip processing for short texts to reduce API overhead
  if (text.length < 50) { // Increased threshold from 20 to 50 characters
    return {
      originalText: text,
      processedText: text,
      processed: false,
      warning: 'Text too short for processing (under 50 chars)'
    };
  }

  try {
    console.log(`🧠 Processing text for natural speech with ${model}...`);
    console.log('📝 Original text length:', text.length);

    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: NATURAL_SPEECH_CONFIG.systemPrompt
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: NATURAL_SPEECH_CONFIG.maxTokens,
      temperature: NATURAL_SPEECH_CONFIG.temperature,
    });

    const processedText = completion.choices[0]?.message?.content?.trim();

    if (!processedText) {
      return {
        originalText: text,
        processedText: text,
        processed: false,
        warning: `No response from ${model}`
      };
    }

    console.log('✅ Text processed successfully for natural speech');
    console.log('📝 Processed text length:', processedText.length);
    console.log('🔄 Transformation preview:');
    console.log('Original:', text.substring(0, 80) + '...');
    console.log('Processed:', processedText.substring(0, 80) + '...');

    return {
      originalText: text,
      processedText: processedText,
      processed: true,
      tokensUsed: completion.usage?.total_tokens || 0
    };

  } catch (error) {
    console.error('OpenAI natural speech processing error:', error);
    
    return {
      originalText: text,
      processedText: text,
      processed: false,
      error: error instanceof Error ? error.message : 'OpenAI processing failed'
    };
  }
}

/**
 * Quick check if natural speech processing is available
 */
export function isNaturalSpeechAvailable(): boolean {
  return openai !== null && !!process.env.OPENAI_API_KEY;
}

/**
 * Process text with fallback - always returns processed text or original
 */
export async function processTextWithFallback(
  text: string,
  enableProcessing: boolean = true,
  model: string = NATURAL_SPEECH_CONFIG.model
): Promise<string> {
  const result = await processTextForNaturalSpeech(text, enableProcessing, model);
  return result.processedText;
}

/**
 * Batch process multiple texts for natural speech
 */
export async function batchProcessTexts(
  texts: string[],
  enableProcessing: boolean = true
): Promise<NaturalSpeechResult[]> {
  const results: NaturalSpeechResult[] = [];
  
  for (const text of texts) {
    const result = await processTextForNaturalSpeech(text, enableProcessing);
    results.push(result);
    
    // Small delay to avoid rate limiting
    if (enableProcessing && openai) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}
