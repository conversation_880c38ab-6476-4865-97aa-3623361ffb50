// User tier management utilities for BotFusion X
import { createClient } from '@/lib/supabase/client'

// Tier definitions matching the database
export type UserTier = 'free' | 'standard' | 'pro'
export type SubscriptionStatus = 'active' | 'cancelled' | 'expired' | 'trial'

export interface UserProfile {
  id: string
  tier: UserTier
  subscription_status: SubscriptionStatus
  subscription_start_date: string
  subscription_end_date?: string
  created_at: string
  updated_at: string
}

export interface TierFeature {
  tier: UserTier
  feature_name: string
  feature_value: any
  description: string
}

export interface TierLimits {
  maxChatInterfaces: number // -1 for unlimited
  voiceFeaturesEnabled: boolean
  voiceModels: string[]
  brandingRemoval: boolean
  whiteLabelingEnabled: boolean
  customBrandingText: boolean
  advancedCustomization: boolean
  analyticsDashboard: boolean
  prioritySupport: boolean
}

// Tier configurations for easy reference
export const TIER_CONFIGS: Record<UserTier, TierLimits> = {
  free: {
    maxChatInterfaces: 1,
    voiceFeaturesEnabled: false,
    voiceModels: [],
    brandingRemoval: false,
    whiteLabelingEnabled: false,
    customBrandingText: false,
    advancedCustomization: false,
    analyticsDashboard: false,
    prioritySupport: false
  },
  standard: {
    maxChatInterfaces: -1, // unlimited
    voiceFeaturesEnabled: true,
    voiceModels: ['thalia', 'asteria', 'helena', 'arcas', 'apollo', 'zeus'], // Original 6 voices
    brandingRemoval: true,
    whiteLabelingEnabled: false,
    customBrandingText: false,
    advancedCustomization: true,
    analyticsDashboard: false,
    prioritySupport: false
  },
  pro: {
    maxChatInterfaces: -1, // unlimited
    voiceFeaturesEnabled: true,
    voiceModels: [
      // Original 6 voices
      'thalia', 'asteria', 'helena', 'arcas', 'apollo', 'zeus',
      // Additional Featured Female Voices
      'andromeda', 'aurora', 'electra', 'harmonia', 'hera', 'iris',
      'luna', 'minerva', 'ophelia', 'phoebe', 'selene', 'vesta',
      // Additional Featured Male Voices
      'aries', 'atlas', 'draco', 'hermes', 'hyperion', 'jupiter',
      'mars', 'neptune', 'odysseus', 'orion', 'orpheus', 'pluto', 'saturn'
    ], // All 31 voices
    brandingRemoval: true,
    whiteLabelingEnabled: true,
    customBrandingText: true,
    advancedCustomization: true,
    analyticsDashboard: true,
    prioritySupport: true
  }
}

// Get user's current tier and profile
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
  
  return data
}

// Helper functions for tier display and pricing
export function getTierDisplayName(tier: UserTier): string {
  switch (tier) {
    case 'free': return 'Free'
    case 'standard': return 'Standard'
    case 'pro': return 'Pro'
    default: return 'Unknown'
  }
}

export function getTierPrice(tier: UserTier): { monthly: number; yearly: number } {
  switch (tier) {
    case 'free': return { monthly: 0, yearly: 0 }
    case 'standard': return { monthly: 29, yearly: 290 }
    case 'pro': return { monthly: 99, yearly: 990 }
    default: return { monthly: 0, yearly: 0 }
  }
}

export function getTierFeatures(tier: UserTier): string[] {
  const config = TIER_CONFIGS[tier]
  const features: string[] = []

  if (config.maxChatInterfaces === -1) {
    features.push('Unlimited Chat Interfaces')
  } else {
    features.push(`${config.maxChatInterfaces} Chat Interface${config.maxChatInterfaces === 1 ? '' : 's'}`)
  }

  if (config.voiceFeaturesEnabled) {
    features.push(`Voice Features (${config.voiceModels.length} voices)`)
  }

  if (config.brandingRemoval) {
    features.push('Branding Removal')
  }

  if (config.whiteLabelingEnabled) {
    features.push('White Labeling')
  }

  if (config.customBrandingText) {
    features.push('Custom Branding')
  }

  if (config.advancedCustomization) {
    features.push('Advanced Customization')
  }

  if (config.analyticsDashboard) {
    features.push('Analytics Dashboard')
  }

  if (config.prioritySupport) {
    features.push('Priority Support')
  }

  return features
}

// Get user's tier features from database
export async function getUserTierFeatures(userId: string): Promise<TierFeature[]> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .rpc('get_user_tier_features', { user_uuid: userId })
  
  if (error) {
    console.error('Error fetching user tier features:', error)
    return []
  }
  
  return data || []
}

// Get user's tier limits (convenience function)
export async function getUserTierLimits(userId: string): Promise<TierLimits> {
  const profile = await getUserProfile(userId)
  
  if (!profile) {
    // Default to free tier if no profile found
    return TIER_CONFIGS.free
  }
  
  return TIER_CONFIGS[profile.tier]
}

// Check if user can create a chat interface
export async function canCreateChatInterface(userId: string): Promise<boolean> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .rpc('can_create_chat_interface', { user_uuid: userId })
  
  if (error) {
    console.error('Error checking chat interface creation permission:', error)
    return false
  }
  
  return data === true
}

// Check if user has access to a specific feature
export async function hasFeatureAccess(userId: string, featureName: string): Promise<boolean> {
  const features = await getUserTierFeatures(userId)
  const feature = features.find(f => f.feature_name === featureName)
  
  if (!feature) return false
  
  // Handle different feature value types
  if (typeof feature.feature_value === 'boolean') {
    return feature.feature_value
  }
  
  if (typeof feature.feature_value === 'string') {
    return feature.feature_value === 'true'
  }
  
  return false
}

// Check if user has access to a specific voice model
export async function hasVoiceModelAccess(userId: string, voiceModel: string): Promise<boolean> {
  const features = await getUserTierFeatures(userId)
  const voiceModelsFeature = features.find(f => f.feature_name === 'voice_models')
  
  if (!voiceModelsFeature) return false
  
  try {
    const voiceModels = JSON.parse(voiceModelsFeature.feature_value as string)
    return Array.isArray(voiceModels) && voiceModels.includes(voiceModel)
  } catch {
    return false
  }
}

// Update user tier (admin function)
export async function updateUserTier(userId: string, newTier: UserTier): Promise<boolean> {
  const supabase = createServiceClient()
  
  if (!supabase) {
    console.error('Service client not available for tier update')
    return false
  }
  
  const { error } = await supabase
    .from('user_profiles')
    .update({ 
      tier: newTier,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
  
  if (error) {
    console.error('Error updating user tier:', error)
    return false
  }
  
  return true
}

// Create user profile (called on signup)
export async function createUserProfile(userId: string, tier: UserTier = 'free'): Promise<boolean> {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('user_profiles')
    .insert({
      id: userId,
      tier,
      subscription_status: 'active'
    })
  
  if (error) {
    console.error('Error creating user profile:', error)
    return false
  }
  
  return true
}

// Get current chat interface count for user
export async function getUserChatInterfaceCount(userId: string): Promise<number> {
  const supabase = createClient()
  
  const { count, error } = await supabase
    .from('chat_interfaces')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
  
  if (error) {
    console.error('Error counting chat interfaces:', error)
    return 0
  }
  
  return count || 0
}

// Tier upgrade/downgrade helpers
export function canUpgradeTo(currentTier: UserTier, targetTier: UserTier): boolean {
  const tierOrder: UserTier[] = ['free', 'standard', 'pro']
  const currentIndex = tierOrder.indexOf(currentTier)
  const targetIndex = tierOrder.indexOf(targetTier)

  return targetIndex > currentIndex
}

export function canDowngradeTo(currentTier: UserTier, targetTier: UserTier): boolean {
  const tierOrder: UserTier[] = ['free', 'standard', 'pro']
  const currentIndex = tierOrder.indexOf(currentTier)
  const targetIndex = tierOrder.indexOf(targetTier)

  return targetIndex < currentIndex
}
